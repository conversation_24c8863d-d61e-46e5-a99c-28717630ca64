{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "functionTimeout": "00:10:00", "httpWorker": {"description": {"defaultExecutablePath": "python", "defaultWorkerPath": "worker.py"}}, "extensions": {"http": {"routePrefix": "api"}}}