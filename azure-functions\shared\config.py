import os
from typing import List
from openai import AzureOpenAI
from langchain_openai import AzureChatOpenAI
from langchain_openai import AzureOpenAIEmbeddings
from llama_index.llms.azure_openai import AzureOpenAI as llama_AzureOpenAI
from llama_index.embeddings.azure_openai import (
    AzureOpenAIEmbedding as llama_AzureOpenAIEmbedding,
)
from azure.storage.blob import BlobServiceClient
from azure.servicebus import ServiceBusClient
from azure.identity import DefaultAzureCredential


class AzureClientManager:
    """
    Manages multiple Azure OpenAI clients and rotates between them to avoid rate limits.
    """
    def __init__(self, api_key, api_version, base_endpoint, deployment_names):
        self.clients = []
        self.current_index = 0

        # Create a client for each deployment name
        for deployment_name in deployment_names:
            # Construct the base URL with the specific deployment name
            base_url = f"{base_endpoint}/openai/deployments/{deployment_name}/chat/completions?api-version={api_version}"

            # Create the client
            client = AzureOpenAI(
                api_key=api_key,
                api_version=api_version,
                base_url=base_url,
            )

            # Add to the list of clients
            self.clients.append(client)

        print(f"Initialized {len(self.clients)} Azure OpenAI clients")

    def get_next_client(self):
        """Get the next client in the rotation"""
        client = self.clients[self.current_index]
        # Rotate to the next client for the next call
        self.current_index = (self.current_index + 1) % len(self.clients)
        return client


class Configs:
    # Base
    PROJECT_NAME: str = "IsecMapper Functions"
    PROJECT_VERSION: str = "v2.0-azure-functions"

    # CORS
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    # Azure Storage configuration
    AZURE_STORAGE_CONNECTION_STRING = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
    AZURE_STORAGE_CONTAINER_NAME = os.getenv("AZURE_STORAGE_CONTAINER_NAME", "isecmapper-documents")
    
    # Azure Service Bus configuration
    AZURE_SERVICEBUS_CONNECTION_STRING = os.getenv("AZURE_SERVICEBUS_CONNECTION_STRING")
    
    # Azure Cosmos DB configuration
    AZURE_COSMOS_CONNECTION_STRING = os.getenv("AZURE_COSMOS_CONNECTION_STRING")

    # Storage paths (now using Azure Blob Storage)
    TEMP_CONTAINER: str = "temp-storage"
    EVIDENCE_CONTAINER: str = "evidence-files"
    POLICY_CONTAINER: str = "policy-documents"
    VECTOR_DB_CONTAINER: str = "vector-db"

    thread_pool_size: int = 10  # Reduced for Functions environment
    rate_limit_per_min: int = 360
    delay_in_seconds: float = 60.0 / rate_limit_per_min

    # Azure OpenAI configuration - load from environment variables
    MSP_AZURE_OPENAI_VERSION = os.getenv("AZURE_OPENAI_VERSION", "2025-01-01-preview")
    MSP_AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
    MSP_AZURE_OPENAI_KEY = os.getenv("AZURE_OPENAI_API_KEY")

    # List of all GPT-4.1 deployment names
    GPT4_DEPLOYMENT_NAMES = [
        "gpt-4.1",
        "gpt-4.1-2", 
        "gpt-4.1-3",
        "gpt-4.1-4",
        "gpt-4.1-5",
        "gpt-4.1-6"
    ]

    # Default deployment names for backward compatibility
    MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME = "gpt-4.1"
    MSP_AZURE_GPT4TURBO_MODEL_NAME = "gpt-4.1"

    MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME = "gpt-4.1"
    MSP_AZURE_GPT4VISION_MODEL_NAME = "gpt-4.1"

    MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME = "embd_test"
    MSP_AZURE_EMBEDDING_MODEL_NAME = "text-embedding-ada-002"

    # Initialize the client manager with all 6 deployments
    azure_client_manager = None
    if MSP_AZURE_OPENAI_KEY and MSP_AZURE_OPENAI_ENDPOINT:
        azure_client_manager = AzureClientManager(
            api_key=MSP_AZURE_OPENAI_KEY,
            api_version=MSP_AZURE_OPENAI_VERSION,
            base_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
            deployment_names=GPT4_DEPLOYMENT_NAMES
        )

    # Azure service clients
    blob_service_client = None
    service_bus_client = None
    
    if AZURE_STORAGE_CONNECTION_STRING:
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    
    if AZURE_SERVICEBUS_CONNECTION_STRING:
        service_bus_client = ServiceBusClient.from_connection_string(AZURE_SERVICEBUS_CONNECTION_STRING)

    # For backward compatibility, keep the original clients
    Azure_client_GPT4_Vision = None
    Azure_client_GPT4_Turbo = None
    Azure_LangChain_client_GPT4_Turbo = None
    Azure_langchain_client_embedding = None
    
    if MSP_AZURE_OPENAI_KEY and MSP_AZURE_OPENAI_ENDPOINT:
        Azure_client_GPT4_Vision = AzureOpenAI(
            api_key=MSP_AZURE_OPENAI_KEY,
            api_version=MSP_AZURE_OPENAI_VERSION,
            base_url=f"{MSP_AZURE_OPENAI_ENDPOINT}/openai/deployments/{MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME}/chat/completions?api-version={MSP_AZURE_OPENAI_VERSION}",
        )

        Azure_client_GPT4_Turbo = AzureOpenAI(
            api_key=MSP_AZURE_OPENAI_KEY,
            api_version=MSP_AZURE_OPENAI_VERSION,
            base_url=f"{MSP_AZURE_OPENAI_ENDPOINT}/openai/deployments/{MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME}/chat/completions?api-version={MSP_AZURE_OPENAI_VERSION}",
        )

        Azure_LangChain_client_GPT4_Turbo = AzureChatOpenAI(
            base_url=f"{MSP_AZURE_OPENAI_ENDPOINT}/openai/deployments/{MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME}/chat/completions?api-version={MSP_AZURE_OPENAI_VERSION}",
            api_key=MSP_AZURE_OPENAI_KEY,
            model_name="gpt-4.1",
            api_version=MSP_AZURE_OPENAI_VERSION,
        )

        Azure_langchain_client_embedding = AzureOpenAIEmbeddings(
            azure_deployment=MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME,
            azure_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
            api_key=MSP_AZURE_OPENAI_KEY,
            show_progress_bar=True,
        )

    aoai_client = {
        "api_key": MSP_AZURE_OPENAI_KEY,
        "api_version": MSP_AZURE_OPENAI_VERSION,
        "azure_endpoint": MSP_AZURE_OPENAI_ENDPOINT,
        "azure_deployment": MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME,
        "model": MSP_AZURE_GPT4TURBO_MODEL_NAME,
    }

    aoai_embedding = {
        "api_key": MSP_AZURE_OPENAI_KEY,
        "api_version": "2023-05-15",
        "azure_endpoint": MSP_AZURE_OPENAI_ENDPOINT,
        "azure_deployment": MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME,
        "model": MSP_AZURE_EMBEDDING_MODEL_NAME,
    }

    # Method to get the next available client from the rotation
    @classmethod
    def get_next_azure_client(cls):
        """
        Get the next available Azure OpenAI client from the rotation.
        This helps distribute API calls across multiple deployments to avoid rate limits.

        Returns:
            An AzureOpenAI client instance
        """
        if cls.azure_client_manager:
            return cls.azure_client_manager.get_next_client()
        return None

    # LlamaIndex clients
    llama_aoai_chat = None
    llama_aoai_embedding = None
    
    if MSP_AZURE_OPENAI_KEY:
        llama_aoai_chat = llama_AzureOpenAI(**aoai_client)
        llama_aoai_embedding = llama_AzureOpenAIEmbedding(**aoai_embedding)

    # Flag to determine whether to use Azure Blob Storage (always True in Functions)
    USE_AZURE_BLOB_STORAGE = True


configs = Configs()
