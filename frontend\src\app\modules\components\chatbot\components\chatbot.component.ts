import { Component, OnInit } from '@angular/core';
import { MaterialModule } from 'src/app/modules/material/material.module';

import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from 'src/environments/environment';
import { WebsocketService } from '../service/websocket.service';
import { FormsModule } from '@angular/forms';
@Component({
  selector: 'app-chatbot',
  standalone: true,
  imports: [MaterialModule, CommonModule, FormsModule],
  templateUrl: './chatbot.component.html',
  styleUrl: './chatbot.component.css',
})
export class ChatbotComponent implements OnInit {
  private socket!: WebSocket;

  file_id: string = '';
  showSpinner: boolean = false;
  chatbotFile: any = [];

  input_message: string = '';

  // message_list = {
  //   "abc":{
  //     "user":"hi",
  //     "chatbot":"hello"
  //   },
  //   "def":{
  //     "user":"hi2",
  //     "chatbot":"hello3"
  //   },

  // }

  // message_order = {
  //   0:"abc",
  //   1:"def",
  //   2:"ghi"
  // }

  messageOrder: { [num: number]: string } = {};
  messageList: { [id: string]: { user: string; chatbot: string } } = {};

  generate_start: boolean = false;
  color: string = 'white';



  constructor(
    private httpClient: HttpClient,
    private websocketService: WebsocketService
  ) {

  }

  ngOnInit(): void {
    // this.file_id = '9e70479228f24019bce3c3940cfdab1d';
    // this.connectWebSocket(this.file_id);
    // this.messageOrder = { 0: 'bd0141731b564ceea930b93c565869b7', 1: '51a6a994b1504c7eb04283ad54fdc530', 2: '2b09113885584d1fba765360f57621fd', 3: 'dce79735f78540d1b8deea6dc74197e9', 4: '22bc6dec771e442fa8b3807912e45732', 5: '4c363f31e4c745769647d4f8a704644c', 6: '064a2d321980424690b82c8412883d9c', 7: 'd3b6f2581e93411ca1882480d08c3c2a', 8: '9bf0cae7543a4b7eb57b4681aa06d084', 9: 'e3c8dde270c349d0af90018f72f08055', 10: '5840a067adf443b8a7bd0b02d81bb013', 11: 'b4129d17f2034023967b65daf5bd73a5', 12: 'fc081661587646f8b39228106fa87406' }

    // this.messageList = {
    //   
    // }

  }

  onChatBotFileChange($event: any) {
    this.chatbotFile = [];

    for (let index = 0; index < $event.target.files.length; index++) {
      const file = $event.target.files[index];
      this.chatbotFile.push(file);
    }
  }

  uploadFile() {
    this.showSpinner = true;
    const fileList: any = [];

    if (this.chatbotFile.length > 0) {
      for (let i = 0; i < this.chatbotFile.length; i++) {
        fileList.push(this.chatbotFile[i]);
      }
    }

    const formData = new FormData();
    for (let index = 0; index < fileList.length; index++) {
      formData.append('file', fileList[index]);
    }



    this.httpClient
      .post(environment.apiBaseUrl + '/upload_chatbot_file', formData)
      .subscribe((response: any) => {
        this.showSpinner = false;
        console.log('This is the response from backend', response);
        this.file_id = response['id'];
        this.connectWebSocket(this.file_id);
      });
  }

  connectWebSocket(file_id: string) {
    console.log('Connecting to WebSocket...');
    // this.websocketService.connect(file_id);
    const socketendpoint = environment.webSocketBaseUrl + '/chatbot/' + file_id;
    this.socket = new WebSocket(socketendpoint);
  }

  sendMessage(message: string) {
    console.log('Sending message:', message);
    console.log("messageOrder -> ", this.messageOrder);
    console.log("messageList -> ", this.messageList);
    this.input_message = '';
    this.socket.send(message);
    console.log('Sent message:', message);
    this.recieveMessage(message);
  }

  recieveMessage(user_message: string) {
    // this.websocketService.websocket.subscribe((event) => {
    //   console.log('Received message:', event.data);
    // });
    // this.socket.onmessage = (event) => {
    //   console.log('Received message:', event.data);
    // };
    this.socket.onmessage = (event) => {
      var recieve = JSON.parse(event.data);

      if (recieve['status'] == 'generate-start') {
        console.log('start');
        this.generateStart(recieve, user_message);
      }
      if (recieve['status'] == 'generate-stream') {
        console.log('stream');
        this.generateContinue(recieve);
      }
      if (recieve['status'] == 'generate-end') {
        console.log('end');
        this.generateEnd(recieve);
      }
      // this.chatbot_message_list.push(event.data);
    };
  }

  generateStart(response: any, user_message: string) {
    // {"status": "generate-start", "id": unique_id}
    // console.log('generateStart -> ', response);
    this.generate_start = true;
    // console.log('at start : ', this.messageOrder);
    // for(let i=0; i<Object.keys(this.messageOrder).length; i++) {
    //   console.log(this.messageOrder[i]);
    // }
    var len = Object.keys(this.messageOrder).length;
    // console.log('length -> ', len);
    this.messageOrder[len] = response['id'];
    this.messageList[response['id']] = { user: user_message, chatbot: '' };
    // console.log('at end : ', this.messageOrder);
  }

  generateContinue(response: any) {
    // {"status": "generate-stream","id": unique_id,"data": stream_msg}
    // console.log('generateContinue -> ', response);
    var id = response.id;
    this.messageList[id]['chatbot'] += response['data'];

    // console.log(this.messageList);
  }

  generateEnd(response: any) {
    // {"status": "generate-end", "id": unique_id}
    // console.log('generateEnd -> ', response);
    this.generate_start = true;
  }
}
