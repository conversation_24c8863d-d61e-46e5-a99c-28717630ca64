# IsecMapper Migration to Azure Functions - Complete Guide

## Executive Summary

**Yes, migrating your FastAPI application to Azure Functions is definitely possible!** However, it requires architectural changes due to the serverless nature of Azure Functions. This guide provides a comprehensive migration strategy with practical implementation steps.

## Current Architecture vs. Azure Functions

### Current FastAPI Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Angular       │    │    FastAPI       │    │  Local Storage  │
│   Frontend      │───▶│   Application    │───▶│   + FAISS DB    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │   WebSocket      │
                    │   Connections    │
                    └──────────────────┘
```

### Proposed Azure Functions Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Angular       │    │  Azure Function  │    │  Azure Storage  │
│   Frontend      │───▶│      App         │───▶│    Account      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                          │
                              ▼                          ▼
                    ┌──────────────────┐    ┌─────────────────┐
                    │  Azure Service   │    │  Azure Cosmos   │
                    │      Bus         │    │      DB         │
                    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │  Azure SignalR   │
                    │    Service       │
                    └──────────────────┘
```

## Migration Challenges & Solutions

### 1. **WebSocket Connections**
- **Challenge**: Azure Functions don't support WebSocket connections
- **Solution**: Use Azure SignalR Service for real-time communication
- **Alternative**: Keep chatbot as separate Azure Container Instance

### 2. **Long-Running Processes**
- **Challenge**: Functions have 5-10 minute execution limits
- **Solution**: Use Azure Service Bus queues + background processing
- **Implementation**: Break analysis into smaller, manageable chunks

### 3. **File Storage**
- **Challenge**: No local file system in serverless environment
- **Solution**: Azure Blob Storage for all file operations
- **Benefit**: Better scalability and durability

### 4. **State Management**
- **Challenge**: Functions are stateless
- **Solution**: Azure Cosmos DB for job tracking and metadata
- **Benefit**: Better reliability and monitoring

## Migration Strategy: Hybrid Approach (Recommended)

### Phase 1: Core API Migration ✅
**Timeline: 1-2 weeks**

Migrate simple, stateless endpoints:
- Region/Domain selection APIs
- Document retrieval APIs
- Health checks and status endpoints

**Benefits:**
- Quick wins with immediate cloud benefits
- No breaking changes to frontend
- Easy to test and validate

### Phase 2: File Processing Migration 🔄
**Timeline: 2-3 weeks**

Transform file upload and analysis:
- Implement Azure Blob Storage integration
- Add Service Bus for job queuing
- Create job status tracking with Cosmos DB

**New Workflow:**
1. Upload files → Store in Blob Storage
2. Queue analysis job → Service Bus
3. Process asynchronously → Background function
4. Track progress → Cosmos DB
5. Notify completion → SignalR (optional)

### Phase 3: Real-time Communication 📋
**Timeline: 1-2 weeks**

Replace WebSocket functionality:
- **Option A**: Azure SignalR Service integration
- **Option B**: Keep chatbot as separate Azure App Service
- **Option C**: Polling-based updates

## Implementation Steps

### Step 1: Set Up Azure Resources

```powershell
# Run the provided deployment script
.\azure-functions\deploy.ps1 `
    -ResourceGroupName "rg-isecmapper-prod" `
    -FunctionAppName "func-isecmapper-prod" `
    -StorageAccountName "stisecmapperprod" `
    -ServiceBusNamespace "sb-isecmapper-prod" `
    -CosmosAccountName "cosmos-isecmapper-prod"
```

### Step 2: Configure Environment Variables

Update your Azure Function App settings:
```bash
# Azure OpenAI (your existing configuration)
AZURE_OPENAI_API_KEY=your-key
AZURE_OPENAI_ENDPOINT=your-endpoint
AZURE_OPENAI_VERSION=2025-01-01-preview

# Azure Storage (auto-configured by deployment script)
AZURE_STORAGE_CONNECTION_STRING=auto-configured
AZURE_STORAGE_CONTAINER_NAME=isecmapper-documents

# Azure Service Bus (auto-configured)
AZURE_SERVICEBUS_CONNECTION_STRING=auto-configured

# Azure Cosmos DB (auto-configured)
AZURE_COSMOS_CONNECTION_STRING=auto-configured
```

### Step 3: Deploy Functions

```bash
# Navigate to functions directory
cd azure-functions

# Install dependencies
pip install -r requirements.txt

# Deploy to Azure
func azure functionapp publish func-isecmapper-prod --python
```

### Step 4: Update Frontend Configuration

Update your Angular environment files:

```typescript
// environment.prod.ts
export const environment = {
  production: true,
  apiBaseUrl: 'https://func-isecmapper-prod.azurewebsites.net/api',
  // Keep existing WebSocket URL for chatbot (if using separate service)
  webSocketBaseUrl: 'wss://your-chatbot-service/api/chatbot/',
  // Or use SignalR
  signalRUrl: 'https://your-signalr-service.service.signalr.net'
};
```

## Cost Analysis

### Current Costs (Estimated)
- **VM/App Service**: $50-200/month (depending on size)
- **Storage**: $10-30/month
- **Bandwidth**: $5-20/month
- **Total**: ~$65-250/month

### Azure Functions Costs (Estimated)
- **Function Execution**: $5-50/month (consumption plan)
- **Azure Storage**: $10-30/month
- **Service Bus**: $5-15/month
- **Cosmos DB**: $25-100/month (depending on usage)
- **SignalR Service**: $5-25/month
- **Total**: ~$50-220/month

**Cost Benefits:**
- Pay only for actual usage
- Automatic scaling
- No infrastructure management
- Better cost predictability

## Performance Considerations

### Advantages
- **Auto-scaling**: Handles traffic spikes automatically
- **Global distribution**: Deploy to multiple regions
- **No cold start for warm functions**: Premium plan available
- **Parallel processing**: Natural horizontal scaling

### Potential Issues
- **Cold starts**: 1-3 second delay for first request
- **Memory limits**: 1.5GB in consumption plan
- **Execution time limits**: 10 minutes maximum

### Optimization Strategies
1. **Use Premium Plan** for production (eliminates cold starts)
2. **Implement warming strategies** for consumption plan
3. **Optimize function size** and dependencies
4. **Use connection pooling** for database connections

## Testing Strategy

### 1. Local Development
```bash
# Start functions locally
cd azure-functions
func start

# Test endpoints
curl http://localhost:7071/api/health
curl http://localhost:7071/api/region_selection
```

### 2. Integration Testing
- Test file upload workflow
- Verify job queuing and processing
- Validate status tracking
- Check error handling

### 3. Performance Testing
- Load test with multiple concurrent requests
- Test large file uploads
- Verify auto-scaling behavior
- Monitor cold start performance

## Monitoring & Observability

### Built-in Monitoring
- **Application Insights**: Automatic logging and metrics
- **Azure Monitor**: Resource utilization and alerts
- **Function App Logs**: Real-time log streaming

### Custom Monitoring
```python
# Add custom telemetry
import logging
from azure.monitor.opentelemetry import configure_azure_monitor

# Configure Application Insights
configure_azure_monitor()

# Custom logging
logging.info("Processing analysis job", extra={
    "job_id": job_id,
    "file_count": len(files),
    "processing_time": processing_time
})
```

## Security Considerations

### Authentication & Authorization
- Use Azure AD integration
- Implement API key authentication
- Set up CORS policies
- Enable HTTPS only

### Data Protection
- Encrypt data at rest (Azure Storage)
- Use managed identities
- Implement proper access controls
- Regular security audits

## Rollback Strategy

### Blue-Green Deployment
1. **Keep existing FastAPI running** during migration
2. **Deploy Functions to staging slot**
3. **Test thoroughly** with subset of traffic
4. **Switch traffic gradually** using Azure Traffic Manager
5. **Keep rollback option** available for 30 days

### Rollback Steps
```bash
# If issues arise, switch back to original
az webapp traffic-routing set \
    --distribution staging=0 production=100 \
    --name func-isecmapper-prod \
    --resource-group rg-isecmapper-prod
```

## Timeline & Milestones

### Week 1-2: Foundation
- [ ] Set up Azure resources
- [ ] Migrate core API endpoints
- [ ] Basic testing and validation

### Week 3-4: File Processing
- [ ] Implement blob storage integration
- [ ] Add job queuing system
- [ ] Create status tracking

### Week 5-6: Advanced Features
- [ ] Migrate evidence analysis
- [ ] Implement error handling
- [ ] Performance optimization

### Week 7-8: Production Readiness
- [ ] Security hardening
- [ ] Monitoring setup
- [ ] Load testing
- [ ] Documentation

## Success Metrics

### Technical Metrics
- **Response time**: < 2 seconds for API calls
- **Availability**: > 99.9% uptime
- **Error rate**: < 1% of requests
- **Cold start time**: < 3 seconds

### Business Metrics
- **Cost reduction**: 10-30% savings
- **Scalability**: Handle 10x traffic spikes
- **Maintenance**: 50% reduction in ops overhead
- **Feature velocity**: Faster deployment cycles

## Next Steps

1. **Review this migration plan** with your team
2. **Set up development environment** with Azure Functions
3. **Start with Phase 1 migration** (core APIs)
4. **Test thoroughly** before proceeding to Phase 2
5. **Plan production deployment** with proper rollback strategy

## Support & Resources

- **Azure Functions Documentation**: https://docs.microsoft.com/en-us/azure/azure-functions/
- **Migration Tools**: Azure Migrate, Azure App Service Migration Assistant
- **Best Practices**: Azure Well-Architected Framework
- **Community Support**: Stack Overflow, Azure Forums

## Feature Comparison: FastAPI vs Azure Functions

| Feature | Current FastAPI | Azure Functions | Migration Effort |
|---------|----------------|-----------------|------------------|
| **API Endpoints** | ✅ Full REST API | ✅ HTTP Triggers | 🟡 Low - Direct mapping |
| **File Uploads** | ✅ Multipart handling | 🟡 Requires adaptation | 🟡 Medium - Blob integration |
| **WebSocket** | ✅ Native support | ❌ Not supported | 🔴 High - SignalR needed |
| **Long Tasks** | ✅ ThreadPool | 🟡 Service Bus queues | 🟡 Medium - Async pattern |
| **File Storage** | ✅ Local filesystem | ✅ Azure Blob Storage | 🟡 Medium - API changes |
| **Database** | 🟡 FAISS local | ✅ Cosmos DB + Blob | 🟡 Medium - Schema design |
| **Scaling** | 🟡 Manual scaling | ✅ Auto-scaling | 🟢 Low - Automatic |
| **Monitoring** | 🟡 Custom logging | ✅ App Insights | 🟢 Low - Built-in |
| **Cost** | 🟡 Fixed monthly | ✅ Pay-per-use | 🟢 Low - Configuration |
| **Deployment** | 🟡 Manual process | ✅ CI/CD ready | 🟢 Low - Automated |

**Legend:** ✅ Excellent | 🟡 Good/Requires work | ❌ Not available | 🟢 Low effort | 🟡 Medium effort | 🔴 High effort

## Final Recommendations

### ✅ **Proceed with Migration** - Here's Why:

1. **Cost Efficiency**: 20-40% cost reduction with pay-per-use model
2. **Auto-scaling**: Handle traffic spikes without manual intervention
3. **Reduced Maintenance**: No server management or patching required
4. **Better Reliability**: Built-in redundancy and disaster recovery
5. **Future-proof**: Serverless is the direction of cloud computing

### 🎯 **Recommended Migration Path**:

**Phase 1 (Week 1-2)**: Migrate core APIs
- Start with `region_selection`, `domain_selection` endpoints
- Low risk, high confidence building
- Immediate cloud benefits

**Phase 2 (Week 3-4)**: File processing transformation
- Implement blob storage + job queuing
- Most complex part, but highest value

**Phase 3 (Week 5-6)**: WebSocket alternative
- Azure SignalR for real-time features
- Or keep chatbot as separate service

### 🚀 **Quick Start Commands**:

```bash
# 1. Deploy Azure resources
cd azure-functions
.\deploy.ps1 -ResourceGroupName "rg-isecmapper" -FunctionAppName "func-isecmapper" -StorageAccountName "stisecmapper"

# 2. Test locally
func start
python test_functions.py http://localhost:7071

# 3. Deploy to Azure
func azure functionapp publish func-isecmapper --python

# 4. Test production
python test_functions.py https://func-isecmapper.azurewebsites.net
```

### ⚠️ **Important Considerations**:

1. **WebSocket Migration**: Plan for SignalR integration or separate chatbot service
2. **File Size Limits**: Test with your largest files (Azure Functions handle up to 100MB)
3. **Cold Starts**: Consider Premium plan for production if latency is critical
4. **Testing**: Thoroughly test file upload workflows before going live

---

**Ready to start the migration?** The provided Azure Functions code in the `azure-functions/` directory gives you a solid foundation to begin with. Start with the core API endpoints and gradually migrate more complex functionality.

**Need help?** The migration is definitely achievable and will provide significant benefits. Start with Phase 1 to build confidence, then tackle the more complex file processing in Phase 2.
