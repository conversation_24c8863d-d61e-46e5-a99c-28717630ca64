{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "python", "FUNCTIONS_EXTENSION_VERSION": "~4", "AZURE_OPENAI_API_KEY": "your-azure-openai-key", "AZURE_OPENAI_ENDPOINT": "https://your-openai-endpoint.openai.azure.com/", "AZURE_OPENAI_VERSION": "2025-01-01-preview", "AZURE_STORAGE_CONNECTION_STRING": "your-storage-connection-string", "AZURE_STORAGE_CONTAINER_NAME": "isecmapper-documents", "AZURE_SERVICEBUS_CONNECTION_STRING": "your-servicebus-connection-string", "AZURE_COSMOS_CONNECTION_STRING": "your-cosmos-connection-string"}}