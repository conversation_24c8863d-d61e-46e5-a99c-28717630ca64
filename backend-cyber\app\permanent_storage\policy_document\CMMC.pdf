%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R /F3 6 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 7 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Oblique
>>
endobj
7 0 obj
<< /Length 5000 >>
stream
BT
/F1 24 Tf
50 750 Td
(Cybersecurity Maturity Model Certification (CMMC)) Tj

/F2 12 Tf
0 -30 Td
(Department of Defense Cybersecurity Framework) Tj
0 -15 Td
(Document Version: 1.0) Tj
0 -15 Td
(Last Updated: April 2025) Tj

/F1 18 Tf
-10 -40 Td
(1. Introduction) Tj
/F2 12 Tf
10 -20 Td
(The Cybersecurity Maturity Model Certification (CMMC) is 
a unified standard for implementing cybersecurity across 
the Defense Industrial Base (DIB), which includes 
over 300,000 companies in the supply chain.

CMMC is designed to protect sensitive unclassified 
information that is shared by the Department 
of Defense (DoD) with its contractors and 
subcontractors. It builds upon existing regulations like 
DFARS 252.204-7012 and NIST SP 800-171 to 
verify that cybersecurity controls are implemented correctly.

The framework combines various cybersecurity standards and 
best practices into one unified standard that 
is tiered to reflect the level of 
cybersecurity maturity needed based on the sensitivity 
of information being handled.) Tj

/F1 18 Tf
-10 -30 Td
(2. History and Development) Tj
/F2 12 Tf
10 -20 Td
(The development of CMMC began in 2019 
when the Department of Defense recognized the 
need for a unified cybersecurity standard for 
the Defense Industrial Base. The initial version, 
CMMC 1.0, was released in January 2020.

In November 2021, the DoD announced CMMC 
2.0, which streamlined the model, reduced the 
number of maturity levels from five to 
three, and aligned more closely with NIST 
SP 800-171. This revision was designed to 
reduce costs for small businesses while maintaining 
robust cybersecurity standards.

The CMMC program is being implemented in 
phases, with a phased rollout through the 
Defense Federal Acquisition Regulation Supplement (DFARS) rule-making 
process. Full implementation is expected to take 
several years, with CMMC requirements gradually being 
incorporated into DoD contracts.

The program is overseen by the Office 
of the Under Secretary of Defense for 
Acquisition and Sustainment (OUSD(A&S)) and is supported 
by the CMMC Accreditation Body (CMMC-AB), which 
was established to train and certify assessors.) Tj

/F1 18 Tf
-10 -30 Td
(3. Key Components) Tj
/F2 12 Tf
10 -20 Td
(CMMC 2.0 encompasses several key components:

â€¢ Maturity Levels
  - Level 1: Foundational Cybersecurity Practices (17 practices)
    * Basic safeguarding of Federal Contract Information (FCI)
    * Equivalent to FAR 52.204-21 basic safeguarding requirements
    * Annual self-assessment required
  - Level 2: Advanced Cybersecurity Practices (110 practices)
    * Protection of Controlled Unclassified Information (CUI)
    * Equivalent to NIST SP 800-171
    * Third-party assessment or self-assessment depending on criticality
  - Level 3: Expert Cybersecurity Practices (110+ practices)
    * Protection of CUI against advanced persistent threats
    * Includes NIST SP 800-171 plus additional practices
    * Government-led assessments required

â€¢ Capability Domains
  - Access Control
  - Asset Management
  - Audit and Accountability
  - Awareness and Training
  - Configuration Management
  - Identification and Authentication
  - Incident Response
  - Maintenance
  - Media Protection
  - Personnel Security
  - Physical Protection
  - Recovery
  - Risk Management
  - Security Assessment
  - Situational Awareness
  - System and Communications Protection
  - System and Information Integrity

â€¢ Assessment Methodology
  - Certified Third-Party Assessment Organizations (C3PAOs)
  - CMMC Assessment Process
  - Evidence collection requirements
  - Assessment criteria
  - Certification documentation

â€¢ Certification Process
  - Scoping of assessment
  - Preparation and documentation
  - Assessment planning
  - On-site assessment
  - Findings and remediation
  - Certification decision
  - Continuous monitoring

â€¢ Governance Structure
  - Department of Defense oversight
  - CMMC Accreditation Body (CMMC-AB)
  - Certified Assessors
  - Certified Instructors
  - Certified Professionals) Tj

/F1 18 Tf
-10 -30 Td
(4. Implementation Process) Tj
/F2 12 Tf
10 -20 Td
(Implementing CMMC compliance typically involves the following 
steps:

1. Determine required CMMC level
   - Review current and future DoD contracts
   - Identify types of information handled (FCI, CUI)
   - Understand program requirements
   - Determine applicable CMMC level
   - Identify assessment requirements (self or third-party)

2. Define assessment scope
   - Identify systems that process, store, or transmit FCI/CUI
   - Document system boundaries
   - Map data flows
   - Identify enclaves if applicable
   - Document out-of-scope systems and justification

3. Conduct gap assessment
   - Compare current practices to CMMC requirements
   - Identify missing or inadequate controls
   - Document current implementation status
   - Prioritize gaps based on risk
   - Develop remediation strategy

4. Implement required practices
   - Address identified gaps
   - Implement technical controls
   - Develop and document policies and procedures
   - Train personnel on security practices
   - Test control effectiveness

5. Document implementation
   - Create System Security Plan (SSP)
   - Document practice implementation
   - Collect and organize evidence
   - Prepare for assessment questions
   - Develop supporting documentation

6. Conduct readiness assessment
   - Perform internal pre-assessment
   - Engage with Registered Practitioner Organization (RPO) if needed
   - Identify and address remaining gaps
   - Validate evidence completeness
   - Prepare personnel for interviews

7. Undergo certification assessment
   - Contract with C3PAO (for Level 2 critical or Level 3)
   - Participate in assessment planning
   - Provide access to systems and documentation
   - Facilitate interviews and demonstrations
   - Address assessor questions

8. Maintain certification
   - Implement continuous monitoring
   - Address changes to environment
   - Report significant changes
   - Prepare for annual affirmation
   - Plan for reassessment (every 3 years)) Tj

/F1 18 Tf
-10 -30 Td
(5. Benefits of Implementation) Tj
/F2 12 Tf
10 -20 Td
(Implementing CMMC provides numerous benefits to defense 
contractors:

â€¢ Contract eligibility
  - Qualification for DoD contracts
  - Competitive advantage in defense contracting
  - Preparation for future contract requirements
  - Demonstration of security commitment
  - Differentiation from non-certified competitors

â€¢ Enhanced security posture
  - Comprehensive cybersecurity framework
  - Defense-in-depth approach
  - Protection against common threats
  - Reduced vulnerability to attacks
  - Improved detection and response capabilities

â€¢ Protection of sensitive information
  - Safeguarding of controlled unclassified information
  - Reduced risk of data breaches
  - Protection of intellectual property
  - Preservation of competitive advantage
  - Maintenance of customer trust

â€¢ Supply chain security
  - Improved security throughout the defense supply chain
  - Better protection of shared information
  - Enhanced trust with DoD and prime contractors
  - Reduced third-party risk
  - Support for national security objectives

â€¢ Operational improvements
  - Better security governance
  - Improved security awareness
  - Enhanced incident response
  - More effective risk management
  - Streamlined security operations

â€¢ Business benefits
  - Reduced security incident costs
  - Improved business resilience
  - Enhanced reputation
  - Potential insurance benefits
  - Alignment with other security frameworks

â€¢ Compliance efficiency
  - Alignment with existing regulations (DFARS, FAR)
  - Foundation for compliance with other standards
  - Structured approach to security implementation
  - Clear security requirements
  - Reduced compliance confusion) Tj

/F3 10 Tf
-20 -30 Td
(This document provides an overview of Cybersecurity Maturity Model Certification (CMMC). For complete and authoritative information,) Tj
0 -15 Td
(please refer to the official documentation and standards publications.) Tj
ET
endstream
endobj
xref
0 8
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
0000000444 00000 n
trailer
<< /Size 8
/Root 1 0 R
>>
startxref
5496
%%EOF