# Define a function to create a PDF with content
function Create-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$KeyPoints
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1500 >>
stream
BT
/F1 24 Tf
100 700 Td
($Title) Tj
/F2 12 Tf
0 -40 Td
($Description) Tj
0 -30 Td
/F1 14 Tf
(Key Components:) Tj
/F2 12 Tf
0 -20 Td
($KeyPoints) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1925
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created PDF with content: $safeFileName.pdf"
}

# 31. ISO/IEC 27018
Create-PDF -FileName "ISO/IEC 27018" `
    -Title "ISO/IEC 27018" `
    -Description "ISO/IEC 27018 is an international standard that focuses on protection of personally identifiable information (PII) in public cloud computing environments. It provides a code of practice for PII protection in public clouds acting as PII processors." `
    -KeyPoints "• Consent for Use of PII
• Control Over PII Use
• Transparency of PII Use
• Communication of PII Disclosures
• Information Security Incident Management for PII
• PII Disclosure to Third Parties
• PII Return, Transfer and Disposal
• Confidentiality Agreements for Cloud Staff
• PII Temporary Files
• PII Transmission Controls
• PII Retention Periods
• PII Deletion or Return
• Disclosure of Subcontractors
• Access to PII by Cloud Provider Personnel"

# 32. CSA STAR
Create-PDF -FileName "CSA STAR" `
    -Title "CSA STAR" `
    -Description "The Cloud Security Alliance Security Trust Assurance and Risk (CSA STAR) program is a free, publicly accessible registry that documents the security controls provided by various cloud computing offerings." `
    -KeyPoints "• Self-Assessment (Level 1)
• Third-party Assessment-based Certification (Level 2)
• Continuous Monitoring-based Certification (Level 3)
• Cloud Controls Matrix (CCM)
• Consensus Assessments Initiative Questionnaire (CAIQ)
• Security, Trust & Assurance Registry (STAR)
• Transparency and Documentation
• Risk Management
• Compliance Validation
• Security Posture Assessment
• Continuous Improvement
• Industry Recognition"

# 33. SOC 2 Type II
Create-PDF -FileName "SOC 2 Type II" `
    -Title "SOC 2 Type II" `
    -Description "SOC 2 Type II is an auditing procedure that verifies service providers securely manage data to protect the interests of the organization and the privacy of its clients. Type II reports evaluate the effectiveness of controls over a period of time (typically 6-12 months)." `
    -KeyPoints "• Security: Protection against unauthorized access
• Availability: System availability for operation and use
• Processing Integrity: System processing is complete, accurate, timely, and authorized
• Confidentiality: Information designated as confidential is protected
• Privacy: Personal information is collected, used, retained, disclosed, and disposed of properly
• Testing Over Time (6-12 months)
• Detailed Description of Tests
• Results of Tests
• Independent Auditor Opinion
• Management Assertion
• System Description"

# 34. ISO 28000
Create-PDF -FileName "ISO 28000" `
    -Title "ISO 28000" `
    -Description "ISO 28000 is an international standard that specifies the requirements for a security management system for the supply chain. It helps organizations establish, implement, maintain and improve their security management systems." `
    -KeyPoints "• Security Management Policy
• Security Risk Assessment and Planning
• Implementation and Operation
• Checking and Corrective Action
• Management Review and Continual Improvement
• Supply Chain Security
• Physical Security
• Access Controls
• Procedural Security
• Personnel Security
• Information and Documentation Security
• Transport and Conveyance Security
• Business Partner Security
• Security Training and Awareness"

Write-Host "Created final batch of PDFs (31-34)"
