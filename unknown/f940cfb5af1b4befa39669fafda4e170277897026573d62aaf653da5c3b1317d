import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class WebsocketService {
  private socket!: WebSocket;

  constructor() { }

  connect(file_id:string): void {
    console.log('Connecting to WebSocket...');
    const socketendpoint = environment.webSocketBaseUrl+file_id;
    this.socket = new WebSocket(socketendpoint);

    this.socket.onopen = () => {
      console.log('WebSocket connection established.');
    };

    this.socket.onmessage = (event) => {
      console.log('Received message:', event.data);
    };

    this.socket.onclose = (event) => {
      console.log('WebSocket connection closed:', event);
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  sendMessage(message: string): void {
    this.socket.send(message);
  }

  closeConnection(): void {
    this.socket.close();
  }

//   messageReceived():Observable<any> {
//     return this.socket.addEventListener("message", (event) => {
//         console.log('Received message:', event.data);
//     });

//     // = (event) => {
//     //   console.log('Received message:', event.data);
//     // };
//   }
}