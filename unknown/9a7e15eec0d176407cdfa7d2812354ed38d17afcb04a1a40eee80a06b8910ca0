.region-selection-container {
  padding: 20px;
  background-color: #252531;
  border-radius: 10px;
}

.header {
  margin-bottom: 15px;
}

.selection-form {
  margin-top: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.full-width {
  width: 100%;
}

.document-list {
  margin-top: 20px;
  padding: 15px;
  background-color: #333340;
  border-radius: 5px;
}

.document-category {
  margin-bottom: 15px;
}

.document-category h6 {
  color: #ccc;
  margin-bottom: 5px;
}

.document-category ul {
  list-style-type: none;
  padding-left: 10px;
}

.document-category li {
  margin-bottom: 5px;
  color: #fff;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

label {
  color: #ccc;
  margin-bottom: 5px;
  display: block;
}

/* Document Selection Styles */
.document-selection-container {
  margin-top: 20px;
  padding: 15px;
  background-color: #333340;
  border-radius: 5px;
}

.document-selection-container h5 {
  color: #ffff00;
  margin-bottom: 15px;
}

.selection-actions {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.btn-select, .btn-deselect {
  padding: 5px 10px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
}

.btn-select {
  background-color: #4CAF50;
  color: white;
}

.btn-deselect {
  background-color: #f44336;
  color: white;
}

.selection-count {
  margin-left: auto;
  color: #ccc;
  font-weight: bold;
}

.document-category-selection {
  background-color: #252531;
  border-radius: 5px;
  margin-bottom: 15px;
  overflow: hidden;
}

.category-header {
  padding: 10px;
  background-color: #3a3a4a;
  border-bottom: 1px solid #444;
}

.category-header label {
  display: flex;
  align-items: center;
  color: white;
  font-weight: bold;
  cursor: pointer;
  margin: 0;
}

.category-header input {
  margin-right: 10px;
  width: 18px;
  height: 18px;
}

.document-items {
  max-height: 200px;
  overflow-y: auto;
  padding: 5px;
}

.document-item {
  padding: 8px 10px;
  border-bottom: 1px solid #333340;
}

.document-item:hover {
  background-color: #3a3a4a;
}

.document-item label {
  display: flex;
  align-items: center;
  color: #ccc;
  cursor: pointer;
  margin: 0;
  width: 100%;
}

.document-item input {
  margin-right: 10px;
  width: 16px;
  height: 16px;
}

/* Scrollbar styling */
.document-items::-webkit-scrollbar {
  width: 8px;
}

.document-items::-webkit-scrollbar-track {
  background: #252531;
}

.document-items::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 4px;
}

.document-items::-webkit-scrollbar-thumb:hover {
  background: #555;
}
