.headerContainer {
  border-bottom: 1px solid yellow;

  .mat-toolbar {
    background-color: #000 !important;
  }

  img {
    height: auto;
    /* // width: 7em;
      //width: 44px; */
    margin-left: 1em;
    cursor: pointer;
    margin: 0 10px 0 10px;
  }

  .mat-icon-button {
    width: 35px;
    height: 35px;
    line-height: 35px;
    border-radius: 8px !important;
    /* box-shadow: -5px -4px 9px 2px rgb(255 255 255 / 50%), 4px 4px 6px rgb(104 132 157 / 20%); */
    background: #000;
    border: none;

    &:hover,
    &:focus,
    &:active {
      /* box-shadow: -5px -4px 9px 2px rgba(255, 255, 255, 0.5), 5px 6px 8px rgba(104, 132, 157, 0.23), inset -4px -4px 4px rgba(255, 255, 255, 0.08), inset 4px 4px 8px rgba(43, 51, 66, 0.15); */
    }
  }

  .hamburger-icon {
    color: yellow;
    margin: 10px;
    background-color: #000;
    width: 35px;
    height: 35px;
    line-height: 35px;
    border-radius: 8px !important;
    background: #252531;
    text-align: center;
  }

  .hamburger-icon:hover {
    background-color: #1a1919;
  }
}

@media only screen and (max-width: 480px) {
  .headerContainer {
    img {
      height: auto;
      width: 5em;
      margin-left: 0.5em;
      cursor: pointer;
    }
  }
}