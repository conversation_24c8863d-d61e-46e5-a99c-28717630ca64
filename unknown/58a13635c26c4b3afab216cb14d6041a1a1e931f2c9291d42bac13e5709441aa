$fileName = "CIS Benchmarks"

$content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R /F3 6 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 7 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Oblique
>>
endobj
7 0 obj
<< /Length 5000 >>
stream
BT
/F1 24 Tf
50 750 Td
(CIS Benchmarks) Tj

/F2 12 Tf
0 -30 Td
(Center for Internet Security Benchmarks) Tj
0 -15 Td
(Document Version: 1.0) Tj
0 -15 Td
(Last Updated: April 2025) Tj

/F1 18 Tf
-10 -40 Td
(1. Introduction) Tj
/F2 12 Tf
10 -20 Td
(CIS Benchmarks are consensus-developed secure configuration guidelines for hardening) Tj
0 -15 Td
(systems and applications. They provide prescriptive guidance for configuring technology) Tj
0 -15 Td
(systems to protect against today's evolving cyber threats. CIS Benchmarks are recognized) Tj
0 -15 Td
(as industry-accepted system hardening standards and are used by organizations in virtually) Tj
0 -15 Td
(every industry sector and vertical.) Tj

/F1 18 Tf
-10 -30 Td
(2. Development Process) Tj
/F2 12 Tf
10 -20 Td
(CIS Benchmarks are developed through a consensus review process involving cybersecurity) Tj
0 -15 Td
(professionals and subject matter experts around the world. The development process includes:) Tj
0 -15 Td
(• Initial draft creation by subject matter experts) Tj
0 -15 Td
(• Community review and feedback) Tj
0 -15 Td
(• Consensus building among participants) Tj
0 -15 Td
(• Final publication after thorough validation) Tj
0 -15 Td
(• Regular updates to address emerging threats and technologies) Tj
0 -15 Td
(This collaborative approach ensures that CIS Benchmarks represent the most effective and) Tj
0 -15 Td
(widely accepted security configuration guidance available.) Tj

/F1 18 Tf
-10 -30 Td
(3. Benchmark Categories) Tj
/F2 12 Tf
10 -20 Td
(CIS Benchmarks cover a wide range of technologies across several categories:) Tj
0 -15 Td
(• Operating Systems:) Tj
0 -15 Td
(  - Microsoft Windows (Desktop and Server editions)) Tj
0 -15 Td
(  - Linux distributions (RHEL, Ubuntu, SUSE, Debian, etc.)) Tj
0 -15 Td
(  - macOS) Tj
0 -15 Td
(  - Unix variants (Solaris, AIX, HP-UX)) Tj
0 -15 Td
(• Server Software:) Tj
0 -15 Td
(  - Microsoft SQL Server) Tj
0 -15 Td
(  - Oracle Database) Tj
0 -15 Td
(  - PostgreSQL) Tj
0 -15 Td
(  - MySQL/MariaDB) Tj
0 -15 Td
(  - Microsoft Exchange Server) Tj
0 -15 Td
(  - Microsoft SharePoint) Tj
0 -15 Td
(  - Apache HTTP Server) Tj
0 -15 Td
(  - Nginx) Tj
0 -15 Td
(  - Docker) Tj
0 -15 Td
(• Cloud Providers:) Tj
0 -15 Td
(  - Amazon Web Services (AWS)) Tj
0 -15 Td
(  - Microsoft Azure) Tj
0 -15 Td
(  - Google Cloud Platform (GCP)) Tj
0 -15 Td
(  - IBM Cloud) Tj
0 -15 Td
(  - Oracle Cloud Infrastructure) Tj
0 -15 Td
(• Network Devices:) Tj
0 -15 Td
(  - Cisco IOS) Tj
0 -15 Td
(  - Cisco Firepower) Tj
0 -15 Td
(  - Palo Alto Networks PAN-OS) Tj
0 -15 Td
(  - Juniper Networks) Tj
0 -15 Td
(  - F5 Networks) Tj
0 -15 Td
(• Desktop Software:) Tj
0 -15 Td
(  - Microsoft Office) Tj
0 -15 Td
(  - Google Chrome) Tj
0 -15 Td
(  - Mozilla Firefox) Tj
0 -15 Td
(  - Microsoft Edge) Tj
0 -15 Td
(• Mobile Devices:) Tj
0 -15 Td
(  - Apple iOS) Tj
0 -15 Td
(  - Google Android) Tj

/F1 18 Tf
-10 -30 Td
(4. Benchmark Structure) Tj
/F2 12 Tf
10 -20 Td
(Each CIS Benchmark includes:) Tj
0 -15 Td
(• Profiles: Different security levels (Level 1 and Level 2)) Tj
0 -15 Td
(  - Level 1: Practical security configuration baseline for general-purpose systems) Tj
0 -15 Td
(  - Level 2: More stringent security settings for high-security environments) Tj
0 -15 Td
(• Recommendations: Specific configuration settings with:) Tj
0 -15 Td
(  - Title and description) Tj
0 -15 Td
(  - Rationale for the recommendation) Tj
0 -15 Td
(  - Impact of implementing the recommendation) Tj
0 -15 Td
(  - Audit procedures to verify implementation) Tj
0 -15 Td
(  - Remediation procedures to implement the recommendation) Tj
0 -15 Td
(  - References to industry standards and best practices) Tj
0 -15 Td
(  - Scoring information) Tj

/F1 18 Tf
-10 -30 Td
(5. Implementation Tools) Tj
/F2 12 Tf
10 -20 Td
(CIS provides several tools to help organizations implement and assess compliance with) Tj
0 -15 Td
(CIS Benchmarks:) Tj
0 -15 Td
(• CIS-CAT Pro: Automated assessment tool that evaluates systems against CIS Benchmarks) Tj
0 -15 Td
(• CIS-CAT Lite: Free version with limited functionality) Tj
0 -15 Td
(• CIS Build Kits: Pre-configured templates for rapid deployment) Tj
0 -15 Td
(• CIS Hardened Images: Pre-configured virtual machine images) Tj
0 -15 Td
(• CIS Controls Self Assessment Tool (CSAT): For assessing implementation of CIS Controls) Tj

/F1 18 Tf
-10 -30 Td
(6. Implementation Process) Tj
/F2 12 Tf
10 -20 Td
(Implementing CIS Benchmarks typically follows these steps:) Tj
0 -15 Td
(1. Select the appropriate benchmark for your technology) Tj
0 -15 Td
(2. Review the recommendations and understand their impact) Tj
0 -15 Td
(3. Test the settings in a non-production environment) Tj
0 -15 Td
(4. Create a baseline of your current configuration) Tj
0 -15 Td
(5. Implement the recommended settings, prioritizing based on:) Tj
0 -15 Td
(   - Critical security settings) Tj
0 -15 Td
(   - Operational impact) Tj
0 -15 Td
(   - Compliance requirements) Tj
0 -15 Td
(6. Document any deviations from the benchmark recommendations and the justification) Tj
0 -15 Td
(7. Verify the implementation using CIS-CAT Pro or other compliance checking tools) Tj
0 -15 Td
(8. Maintain the secure configuration through change management processes) Tj

/F1 18 Tf
-10 -30 Td
(7. Benefits of Implementation) Tj
/F2 12 Tf
10 -20 Td
(• Consensus-developed security guidance from a global community of experts) Tj
0 -15 Td
(• Protection against the most pervasive and dangerous threats) Tj
0 -15 Td
(• Alignment with major compliance frameworks (NIST, PCI DSS, HIPAA, etc.)) Tj
0 -15 Td
(• Reduction in security vulnerabilities and attack surface) Tj
0 -15 Td
(• Clear, step-by-step guidance for implementation) Tj
0 -15 Td
(• Regular updates to address emerging threats and technologies) Tj
0 -15 Td
(• Vendor-neutral recommendations) Tj
0 -15 Td
(• Different profile levels to balance security and usability) Tj
0 -15 Td
(• Improved security posture and reduced risk of successful attacks) Tj

/F3 10 Tf
-20 -30 Td
(This document provides an overview of CIS Benchmarks. For complete and authoritative information,) Tj
0 -15 Td
(please refer to the official Center for Internet Security documentation.) Tj
ET
endstream
endobj
xref
0 8
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
0000000444 00000 n
trailer
<< /Size 8
/Root 1 0 R
>>
startxref
5496
%%EOF
"@

# Write the PDF file
[System.IO.File]::WriteAllText("$fileName.pdf", $content)

Write-Host "Created detailed PDF for CIS Benchmarks"
