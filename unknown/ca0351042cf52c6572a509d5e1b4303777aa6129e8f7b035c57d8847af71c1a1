<div class="domain-selection-container">
  <div class="header">
    <h4>Domain-Based Selection</h4>
  </div>
  <mat-divider></mat-divider>

  <div class="selection-form">
    <!-- Domain Selection -->
    <div class="form-group">
      <label for="domain">Domain:</label>
      <mat-form-field appearance="fill" class="full-width">
        <mat-select
          id="domain"
          [(ngModel)]="selectedDomain"
          (selectionChange)="onDomainChange()"
          placeholder="Select a domain">
          @for (domain of domains; track domain) {
            <mat-option [value]="domain">{{ domain }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>

    <!-- CSP Selection (only for Cloud Security/SaaS) -->
    @if (showCspSelection()) {
      <div class="form-group">
        <label for="csp">Cloud Service Provider:</label>
        <mat-form-field appearance="fill" class="full-width">
          <mat-select
            id="csp"
            [(ngModel)]="selectedCsp"
            (selectionChange)="onCspChange()"
            placeholder="Select a CSP"
            [disabled]="!selectedDomain">
            @for (csp of csps; track csp) {
              <mat-option [value]="csp">{{ csp }}</mat-option>
            }
          </mat-select>
        </mat-form-field>
      </div>
    }
  </div>

  <!-- Document List -->
  @if (getDocumentCount() > 0) {
    <div class="document-list">
      <h5>Selected Documents ({{ getDocumentCount() }})</h5>

      @if (documents.Standard.length > 0) {
        <div class="document-category">
          <h6>Standards</h6>
          <ul>
            @for (doc of documents.Standard; track doc) {
              <li>{{ doc }}</li>
            }
          </ul>
        </div>
      }

      @if (documents.Guidelines.length > 0) {
        <div class="document-category">
          <h6>Guidelines</h6>
          <ul>
            @for (doc of documents.Guidelines; track doc) {
              <li>{{ doc }}</li>
            }
          </ul>
        </div>
      }

      @if (documents.Framework.length > 0) {
        <div class="document-category">
          <h6>Frameworks</h6>
          <ul>
            @for (doc of documents.Framework; track doc) {
              <li>{{ doc }}</li>
            }
          </ul>
        </div>
      }

      @if (documents.Regulation.length > 0) {
        <div class="document-category">
          <h6>Regulations</h6>
          <ul>
            @for (doc of documents.Regulation; track doc) {
              <li>{{ doc }}</li>
            }
          </ul>
        </div>
      }
    </div>

    <!-- Document Selection -->
    <div class="document-selection-container">
      <h5>Select Domain Documents</h5>

      <div class="selection-actions">
        <button class="btn-select" (click)="selectAllDocuments()">Select All</button>
        <button class="btn-deselect" (click)="deselectAllDocuments()">Deselect All</button>
        <span class="selection-count">Selected: {{ selectedDocuments.length }} of {{ getDocumentCount() }}</span>
      </div>

      <!-- Standards -->
      @if (documents.Standard.length > 0) {
        <div class="document-category-selection">
          <div class="category-header">
            <label>
              <input
                type="checkbox"
                [checked]="isCategorySelected('Standard')"
                (change)="toggleCategory('Standard')"
              >
              <span>Standards ({{ documents.Standard.length }})</span>
            </label>
          </div>
          <div class="document-items">
            @for (doc of documents.Standard; track doc) {
              <div class="document-item">
                <label>
                  <input
                    type="checkbox"
                    [checked]="isDocumentSelected(doc)"
                    (change)="toggleDocument(doc)"
                  >
                  <span>{{ doc }}</span>
                </label>
              </div>
            }
          </div>
        </div>
      }

      <!-- Guidelines -->
      @if (documents.Guidelines.length > 0) {
        <div class="document-category-selection">
          <div class="category-header">
            <label>
              <input
                type="checkbox"
                [checked]="isCategorySelected('Guidelines')"
                (change)="toggleCategory('Guidelines')"
              >
              <span>Guidelines ({{ documents.Guidelines.length }})</span>
            </label>
          </div>
          <div class="document-items">
            @for (doc of documents.Guidelines; track doc) {
              <div class="document-item">
                <label>
                  <input
                    type="checkbox"
                    [checked]="isDocumentSelected(doc)"
                    (change)="toggleDocument(doc)"
                  >
                  <span>{{ doc }}</span>
                </label>
              </div>
            }
          </div>
        </div>
      }

      <!-- Frameworks -->
      @if (documents.Framework.length > 0) {
        <div class="document-category-selection">
          <div class="category-header">
            <label>
              <input
                type="checkbox"
                [checked]="isCategorySelected('Framework')"
                (change)="toggleCategory('Framework')"
              >
              <span>Frameworks ({{ documents.Framework.length }})</span>
            </label>
          </div>
          <div class="document-items">
            @for (doc of documents.Framework; track doc) {
              <div class="document-item">
                <label>
                  <input
                    type="checkbox"
                    [checked]="isDocumentSelected(doc)"
                    (change)="toggleDocument(doc)"
                  >
                  <span>{{ doc }}</span>
                </label>
              </div>
            }
          </div>
        </div>
      }

      <!-- Regulations -->
      @if (documents.Regulation.length > 0) {
        <div class="document-category-selection">
          <div class="category-header">
            <label>
              <input
                type="checkbox"
                [checked]="isCategorySelected('Regulation')"
                (change)="toggleCategory('Regulation')"
              >
              <span>Regulations ({{ documents.Regulation.length }})</span>
            </label>
          </div>
          <div class="document-items">
            @for (doc of documents.Regulation; track doc) {
              <div class="document-item">
                <label>
                  <input
                    type="checkbox"
                    [checked]="isDocumentSelected(doc)"
                    (change)="toggleDocument(doc)"
                  >
                  <span>{{ doc }}</span>
                </label>
              </div>
            }
          </div>
        </div>
      }
    </div>
  }

  @if (loading) {
    <div class="loading-indicator">
      <p>Loading...</p>
    </div>
  }
</div>
