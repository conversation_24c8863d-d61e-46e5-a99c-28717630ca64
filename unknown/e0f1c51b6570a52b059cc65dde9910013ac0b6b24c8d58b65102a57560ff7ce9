# IsecMapper Enhancements

This document outlines the enhancements made to the IsecMapper application.

## New Features

### Domain and Benchmark Selection

- Added a new domain selection page that allows users to select from 11 industry domains
- Added a benchmark selection feature that displays relevant benchmarks based on selected domains
- Common benchmarks are displayed for all domains

### Question Selection Options

- Added three options for selecting questions:
  1. **Hardcoded Questions**: Predefined questions based on selected domains
  2. **Generate Questions**: Placeholder for future implementation
  3. **Upload Custom Questionnaire**: Existing functionality to upload Excel files

### Backend Improvements

- Modified the evidence analyzer to process only the selected benchmark documents
- Removed the requirement for control ID prefixes in filenames
- Each Excel control is now compared against all documents
- All relevant evidence files used in the analysis are displayed in the results

## Usage

1. Click on "IsecMapper" in the sidebar
2. Click "Explore IsecMapper" button
3. Select domains and benchmarks relevant to your organization
4. Choose a question source (hardcoded, generate, or upload)
5. If uploading a custom questionnaire, upload your Excel file
6. Upload evidence files (PDFs, images, etc.)
7. View the analysis results

## Technical Details

### New Components

- `DomainSelectionComponent`: Handles domain and benchmark selection
- `QuestionSelectionComponent`: Provides options for question sources

### Modified Backend

- Updated the evidence analyzer API to accept benchmark selections
- Modified the document processing logic to filter based on selected benchmarks
- Enhanced the file grouping to make all files available to all controls

### Configuration

- The policy documents are stored in the `permanent_storage/policy_document` folder
- Each benchmark has its own PDF file (e.g., "NIST CSF 2.0.pdf")
- Only the selected benchmark documents are processed during analysis
