import os
from openai import AzureOpenAI
from dotenv import load_dotenv

# Load environment variables
env_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), ".env")
print(f"Loading .env file from: {env_path}")
print(f"File exists: {os.path.exists(env_path)}")

load_dotenv(
    override=True,
    verbose=True,
    dotenv_path=env_path,
)

# Get environment variables
MSP_AZURE_OPENAI_VERSION = os.getenv("MSP_AZURE_OPENAI_VERSION")
MSP_AZURE_OPENAI_ENDPOINT = os.getenv("MSP_AZURE_OPENAI_ENDPOINT")
MSP_AZURE_OPENAI_KEY = os.getenv("MSP_AZURE_OPENAI_KEY")

print(f"\nAzure OpenAI Configuration:")
print(f"MSP_AZURE_OPENAI_VERSION: {MSP_AZURE_OPENAI_VERSION}")
print(f"MSP_AZURE_OPENAI_ENDPOINT: {MSP_AZURE_OPENAI_ENDPOINT}")
print(f"MSP_AZURE_OPENAI_KEY: {MSP_AZURE_OPENAI_KEY}")

# Test different GPT deployments
deployments_to_test = [
    "gpt-4",
    "gpt-4o",
    "gpt-35-turbo",
    "gpt-4-turbo-2024-04-09",
    "gpt-4o-2024-05-13"
]

for deployment in deployments_to_test:
    print(f"\nTesting deployment: {deployment}")
    try:
        client = AzureOpenAI(
            api_key=MSP_AZURE_OPENAI_KEY,
            api_version=MSP_AZURE_OPENAI_VERSION,
            azure_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
            azure_deployment=deployment
        )
        
        # Test completion
        response = client.chat.completions.create(
            model=deployment,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello, how are you?"}
            ],
            max_tokens=10
        )
        print(f"Response: {response.choices[0].message.content}")
        print(f"Deployment {deployment} test successful!")
    except Exception as e:
        print(f"Error testing deployment {deployment}: {e}")

print("\nScript completed.")
