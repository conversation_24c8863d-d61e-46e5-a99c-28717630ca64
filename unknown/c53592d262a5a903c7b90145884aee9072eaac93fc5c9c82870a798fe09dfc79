import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root'
})

export class GlobalDataService {

    private analysisData: any = [];
    private selectedDomains: any[] = [];
    private selectedBenchmarks: any[] = [];
    private questionSelectionMethod: string = '';
    private hardcodedQuestions: any[] = [];

    // New properties for region and domain selection
    private regionSelection: any = null;
    private domainSelection: any = null;
    private selectedDocuments: string[] = [];

    getAnalysisData() {
        return this.analysisData;
    }

    setAnalysisData(data: any) {
        this.analysisData = data;
        console.log(this.analysisData);
    }

    // Legacy methods for backward compatibility
    getSelectedDomains() {
        return this.selectedDomains;
    }

    setSelectedDomains(domains: any[]) {
        this.selectedDomains = domains;
    }

    getSelectedBenchmarks() {
        return this.selectedBenchmarks;
    }

    setSelectedBenchmarks(benchmarks: any[]) {
        this.selectedBenchmarks = benchmarks;
    }

    // New methods for region and domain selection
    getRegionSelection() {
        return this.regionSelection;
    }

    setRegionSelection(selection: any) {
        this.regionSelection = selection;
    }

    getDomainSelection() {
        return this.domainSelection;
    }

    setDomainSelection(selection: any) {
        this.domainSelection = selection;
    }

    getSelectedDocuments() {
        return this.selectedDocuments;
    }

    setSelectedDocuments(documents: string[]) {
        this.selectedDocuments = documents;

        // For backward compatibility, also set selectedBenchmarks
        this.selectedBenchmarks = documents.map(doc => ({ name: doc }));
    }

    getQuestionSelectionMethod() {
        return this.questionSelectionMethod;
    }

    setQuestionSelectionMethod(method: string) {
        this.questionSelectionMethod = method;
    }

    getHardcodedQuestions() {
        return this.hardcodedQuestions;
    }

    setHardcodedQuestions(questions: any[]) {
        this.hardcodedQuestions = questions;
    }

    // Get benchmark names as an array of strings (legacy method)
    getSelectedBenchmarkNames(): string[] {
        if (this.selectedBenchmarks.length > 0) {
            return this.selectedBenchmarks.map(benchmark => benchmark.name);
        }
        return this.selectedDocuments;
    }
}