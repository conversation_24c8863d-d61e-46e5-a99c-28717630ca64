<div class="main-container">
  <div class="container p-4">
    <h2>IsecMapper - Question Selection</h2>
  </div>

  <div class="container">
    <div class="row justify-content-md-center align-items-center">
      <div class="col-md-10 mx-2 justify-content-md-center backdrop">
        <div class="header">
          <h4>Selected Domains</h4>
        </div>
        <mat-divider></mat-divider>
        <div class="body py-4">
          <div class="selected-items">
            @for (domain of selectedDomains; track domain.id) {
              <div class="selected-item">{{ domain.name }}</div>
            }
          </div>
        </div>
      </div>
    </div>

    <div class="row justify-content-md-center align-items-center mt-4">
      <div class="col-md-10 mx-2 justify-content-md-center backdrop">
        <div class="header">
          <h4>Selected Benchmarks</h4>
        </div>
        <mat-divider></mat-divider>
        <div class="body py-4">
          <div class="selected-items">
            @for (benchmark of selectedBenchmarks; track benchmark.id) {
              <div class="selected-item">{{ benchmark.name }}</div>
            }
          </div>
        </div>
      </div>
    </div>

    <div class="row justify-content-md-center align-items-center mt-4">
      <div class="col-md-10 mx-2 justify-content-md-center backdrop">
        <div class="header">
          <h4>Choose Question Source</h4>
        </div>
        <mat-divider></mat-divider>
        <div class="body py-4">
          <div class="option-cards">
            <!-- Option 1: Hardcoded Questions -->
            <div class="option-card" [class.selected]="selectedOption === 'hardcoded'" (click)="selectOption('hardcoded')">
              <div class="option-header">
                <h5>Use Predefined Questions</h5>
              </div>
              <div class="option-body">
                <p>Use a set of predefined questions based on your selected domains.</p>

                @if (selectedOption === 'hardcoded') {
                  <div class="question-preview">
                    <h6>Sample Questions:</h6>
                    <ul>
                      @for (question of combinedQuestions.slice(0, 5); track question.id) {
                        <li>
                          <strong>{{ question.domain }}:</strong> {{ question.question }}
                        </li>
                      }
                      @if (combinedQuestions.length > 5) {
                        <li>...and {{ combinedQuestions.length - 5 }} more</li>
                      }
                    </ul>
                  </div>
                }
              </div>
            </div>

            <!-- Option 2: Generate Questions -->
            <div class="option-card" [class.selected]="selectedOption === 'generate'" (click)="selectOption('generate')">
              <div class="option-header">
                <h5>Generate Questions</h5>
              </div>
              <div class="option-body">
                <p>Generate custom questions based on your selected domains and benchmarks.</p>

                @if (selectedOption === 'generate') {
                  <div class="coming-soon">
                    <p><em>This feature will be available soon.</em></p>
                  </div>
                }
              </div>
            </div>

            <!-- Option 3: Upload Custom Questionnaire -->
            <div class="option-card" [class.selected]="selectedOption === 'upload'" (click)="selectOption('upload')">
              <div class="option-header">
                <h5>Upload Custom Questionnaire</h5>
              </div>
              <div class="option-body">
                <p>Upload your own custom questionnaire in Excel format.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row justify-content-md-center align-items-center mt-4">
      <div class="col-md-10 mx-2 justify-content-md-center">
        <div class="d-flex justify-content-end">
          <button mat-button-raised class="btn btn-primary" (click)="navigateToUploadQuestionnaire()" [disabled]="!selectedOption">
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
