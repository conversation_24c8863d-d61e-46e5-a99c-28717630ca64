import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MaterialModule } from '../../material/material.module';
import { QuestionSelectionComponent } from './components/question-selection.component';
import { QuestionSelectionRoutingModule } from './question-selection-routing.module';

@NgModule({
  declarations: [],
  providers: [],
  imports: [
    CommonModule,
    MaterialModule,
    QuestionSelectionRoutingModule,
    QuestionSelectionComponent,
  ],
})
export class QuestionSelectionModule {}
