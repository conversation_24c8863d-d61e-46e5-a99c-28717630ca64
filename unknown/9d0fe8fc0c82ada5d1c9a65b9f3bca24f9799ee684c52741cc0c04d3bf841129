import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatStepperModule } from '@angular/material/stepper';
import { FormsModule } from '@angular/forms';

import { RegionSelectionComponent } from '../../region-selection/components/region-selection.component';
import { DomainBasedSelectionComponent } from '../../domain-based-selection/components/domain-based-selection.component';
import { GlobalDataService } from '../../../models/gloabl_data.model';

// Define document categories interface
interface DocumentCategories {
  Standard: string[];
  Guidelines: string[];
  Framework: string[];
  Regulation: string[];
  [key: string]: string[];
}

@Component({
  selector: 'app-document-selection',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatDividerModule,
    MatStepperModule,
    RegionSelectionComponent,
    DomainBasedSelectionComponent
  ],
  templateUrl: './document-selection.component.html',
  styleUrls: ['./document-selection.component.css']
})
export class DocumentSelectionComponent implements OnInit {
  // Selection state
  regionSelection: {
    region: string;
    industry: string;
    sector: string;
    documents: DocumentCategories;
  } | null = null;

  domainSelection: {
    domain: string;
    csp?: string;
    documents: DocumentCategories;
  } | null = null;

  // Selected documents from each section
  regionSelectedDocuments: string[] = [];
  domainSelectedDocuments: string[] = [];

  // Document selection tracking
  regionDocumentSelections: { [key: string]: boolean } = {};
  domainDocumentSelections: { [key: string]: boolean } = {};

  constructor(
    private router: Router,
    private globalDataService: GlobalDataService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    // Initialize selections
  }

  onRegionSelectionChanged(selection: {
    region: string;
    industry: string;
    sector: string;
    documents: DocumentCategories;
    selectedDocuments: string[];
  }): void {
    this.regionSelection = selection;
    console.log('Region selection updated:', selection);

    // Use the selected documents from the region selection
    this.regionSelectedDocuments = selection.selectedDocuments || [];
  }

  onDomainSelectionChanged(selection: {
    domain: string;
    csp?: string;
    documents: DocumentCategories;
    selectedDocuments: string[];
  }): void {
    this.domainSelection = selection;
    console.log('Domain selection updated:', selection);

    // Use the selected documents from the domain selection
    this.domainSelectedDocuments = selection.selectedDocuments || [];
  }

  // Region document selection methods
  initializeRegionDocumentSelections(): void {
    this.regionDocumentSelections = {};
    this.regionSelectedDocuments = [];

    if (this.regionSelection && this.regionSelection.documents) {
      for (const category in this.regionSelection.documents) {
        for (const doc of this.regionSelection.documents[category]) {
          // Select all documents by default
          this.regionDocumentSelections[doc] = true;
          this.regionSelectedDocuments.push(doc);
        }
      }
    }
  }

  isRegionDocumentSelected(doc: string): boolean {
    return this.regionDocumentSelections[doc] === true;
  }

  toggleRegionDocument(doc: string): void {
    this.regionDocumentSelections[doc] = !this.regionDocumentSelections[doc];
    this.updateRegionSelectedDocuments();
  }

  isRegionCategorySelected(category: string): boolean {
    if (!this.regionSelection || !this.regionSelection.documents[category] ||
        this.regionSelection.documents[category].length === 0) {
      return false;
    }

    return this.regionSelection.documents[category].every((doc: string) =>
      this.regionDocumentSelections[doc] === true
    );
  }

  toggleRegionCategory(category: string): void {
    if (!this.regionSelection || !this.regionSelection.documents[category]) {
      return;
    }

    const newState = !this.isRegionCategorySelected(category);

    for (const doc of this.regionSelection.documents[category]) {
      this.regionDocumentSelections[doc] = newState;
    }

    this.updateRegionSelectedDocuments();
  }

  selectAllRegionDocuments(): void {
    if (!this.regionSelection) {
      return;
    }

    for (const category in this.regionSelection.documents) {
      for (const doc of this.regionSelection.documents[category]) {
        this.regionDocumentSelections[doc] = true;
      }
    }

    this.updateRegionSelectedDocuments();
  }

  deselectAllRegionDocuments(): void {
    if (!this.regionSelection) {
      return;
    }

    for (const category in this.regionSelection.documents) {
      for (const doc of this.regionSelection.documents[category]) {
        this.regionDocumentSelections[doc] = false;
      }
    }

    this.updateRegionSelectedDocuments();
  }

  updateRegionSelectedDocuments(): void {
    this.regionSelectedDocuments = [];

    for (const doc in this.regionDocumentSelections) {
      if (this.regionDocumentSelections[doc]) {
        this.regionSelectedDocuments.push(doc);
      }
    }
  }

  // Domain document selection methods
  initializeDomainDocumentSelections(): void {
    this.domainDocumentSelections = {};
    this.domainSelectedDocuments = [];

    if (this.domainSelection && this.domainSelection.documents) {
      for (const category in this.domainSelection.documents) {
        for (const doc of this.domainSelection.documents[category]) {
          // Select all documents by default
          this.domainDocumentSelections[doc] = true;
          this.domainSelectedDocuments.push(doc);
        }
      }
    }
  }

  isDomainDocumentSelected(doc: string): boolean {
    return this.domainDocumentSelections[doc] === true;
  }

  toggleDomainDocument(doc: string): void {
    this.domainDocumentSelections[doc] = !this.domainDocumentSelections[doc];
    this.updateDomainSelectedDocuments();
  }

  isDomainCategorySelected(category: string): boolean {
    if (!this.domainSelection || !this.domainSelection.documents[category] ||
        this.domainSelection.documents[category].length === 0) {
      return false;
    }

    return this.domainSelection.documents[category].every((doc: string) =>
      this.domainDocumentSelections[doc] === true
    );
  }

  toggleDomainCategory(category: string): void {
    if (!this.domainSelection || !this.domainSelection.documents[category]) {
      return;
    }

    const newState = !this.isDomainCategorySelected(category);

    for (const doc of this.domainSelection.documents[category]) {
      this.domainDocumentSelections[doc] = newState;
    }

    this.updateDomainSelectedDocuments();
  }

  selectAllDomainDocuments(): void {
    if (!this.domainSelection) {
      return;
    }

    for (const category in this.domainSelection.documents) {
      for (const doc of this.domainSelection.documents[category]) {
        this.domainDocumentSelections[doc] = true;
      }
    }

    this.updateDomainSelectedDocuments();
  }

  deselectAllDomainDocuments(): void {
    if (!this.domainSelection) {
      return;
    }

    for (const category in this.domainSelection.documents) {
      for (const doc of this.domainSelection.documents[category]) {
        this.domainDocumentSelections[doc] = false;
      }
    }

    this.updateDomainSelectedDocuments();
  }

  updateDomainSelectedDocuments(): void {
    this.domainSelectedDocuments = [];

    for (const doc in this.domainDocumentSelections) {
      if (this.domainDocumentSelections[doc]) {
        this.domainSelectedDocuments.push(doc);
      }
    }
  }

  // Navigation and summary methods
  navigateToQuestionSelection(): void {
    // Check if at least one document is selected
    if (this.getTotalSelectedDocuments() === 0) {
      this.snackBar.open('Please select at least one document.', 'Close', {
        duration: 5000
      });
      return;
    }

    // Combine all selected documents
    const allSelectedDocuments = [...this.regionSelectedDocuments];

    // Add domain documents (avoiding duplicates)
    for (const doc of this.domainSelectedDocuments) {
      if (!allSelectedDocuments.includes(doc)) {
        allSelectedDocuments.push(doc);
      }
    }

    // Store the selections in the global service
    this.globalDataService.setRegionSelection(this.regionSelection);
    this.globalDataService.setDomainSelection(this.domainSelection);
    this.globalDataService.setSelectedDocuments(allSelectedDocuments);

    // Navigate to the question selection page
    this.router.navigateByUrl('/question-selection');
  }

  getAllAvailableDocuments(): string[] {
    const documents: string[] = [];

    // Add documents from region selection
    if (this.regionSelection && this.regionSelection.documents) {
      for (const category in this.regionSelection.documents) {
        documents.push(...this.regionSelection.documents[category]);
      }
    }

    // Add documents from domain selection
    if (this.domainSelection && this.domainSelection.documents) {
      for (const category in this.domainSelection.documents) {
        // Add only unique documents
        for (const doc of this.domainSelection.documents[category]) {
          if (!documents.includes(doc)) {
            documents.push(doc);
          }
        }
      }
    }

    return documents;
  }

  getDocumentCount(): number {
    return this.getAllAvailableDocuments().length;
  }

  getTotalSelectedDocuments(): number {
    // Count unique documents from both selections
    const uniqueDocuments = new Set([
      ...this.regionSelectedDocuments,
      ...this.domainSelectedDocuments
    ]);

    return uniqueDocuments.size;
  }
}
