.backdrop {
    /* background-color: #30304598; */
    padding: 10px;
    /* border-radius: 10px; */

    border-radius: 10px;
    background: #252531;
    /* box-shadow:  5px 5px 0px #282839,
                 -5px -5px 0px #383851; */
}

.header {
    display: flex;
    flex-direction: row;

    h4 {
        padding-right: 10px;
        margin-right: 10px;

    }

    .back-button {


        border: 0px solid black;
        border-radius: 1000px;
        width: 25px;
        height: 25px;
        margin-top: 5px;
    }

}


.with_white_space {
    /* white-space: pre-line; */
    white-space: pre-wrap;
}


.custom-badge {
    border-radius: 200px;
    padding: 5px 15px;
    font-size: 16px;

}

.partially-compliant {
    background-color: #FE9900;
    color: #000;
    font-weight: 500;
}

.compliant {
    background-color: #7DDA58;
    color: #000;
    font-weight: 500;
}

.non-compliant {
    background-color: #C61A09;
    color: #fff;
    font-weight: 500;
}

.dashboard-container {
    flex-direction: row;
    padding-bottom: 50px;

}

.summary {
    width: 60%;
}

.summary-content {

    margin-top: 20px;
    text-align: left;
    padding-right: 40px;


}

.chart {
    width: 40%;
}

.btn {
    background: yellow;
    color: #333;
    border: none;
    font-weight: bold;
}

.dropdown-field {
    width: 150px;
    margin-bottom: -1.25em;
}

::ng-deep .dropdown-field .mat-mdc-form-field-subscript-wrapper {
    display: none;
}

::ng-deep .dropdown-field .mat-mdc-text-field-wrapper {
    background-color: #333;
    border-radius: 4px;
}

::ng-deep .dropdown-field .mat-mdc-form-field-infix {
    padding-top: 8px;
    padding-bottom: 8px;
    min-height: unset;
}