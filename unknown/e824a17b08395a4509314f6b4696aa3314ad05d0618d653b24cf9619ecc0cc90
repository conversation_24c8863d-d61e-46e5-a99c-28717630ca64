import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { MaterialModule } from '../../../material/material.module';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GlobalDataService } from '../../../models/gloabl_data.model';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-question-selection',
  standalone: true,
  imports: [
    MaterialModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDividerModule,
    MatButtonModule
  ],
  templateUrl: './question-selection.component.html',
  styleUrl: './question-selection.component.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class QuestionSelectionComponent implements OnInit {
  selectedDomains: any[] = [];
  selectedBenchmarks: any[] = [];
  selectedOption: string = '';

  // Sample hardcoded questions for each domain
  domainQuestions: any = {
    'Cross-Industry': [
      { id: 1, question: 'Does the organization have a documented information security policy?' },
      { id: 2, question: 'Is there a process for risk assessment and treatment?' },
      { id: 3, question: 'Are security awareness training programs in place?' }
    ],
    'Cloud Security': [
      { id: 1, question: 'Are cloud resources protected with proper access controls?' },
      { id: 2, question: 'Is data encrypted both in transit and at rest?' },
      { id: 3, question: 'Are cloud security configurations regularly audited?' }
    ],
    'AI': [
      { id: 1, question: 'Does the organization have AI governance policies?' },
      { id: 2, question: 'Are AI models regularly tested for bias and fairness?' },
      { id: 3, question: 'Is there a process for AI risk assessment?' }
    ],
    'Financial Services': [
      { id: 1, question: 'Are financial transactions properly secured and monitored?' },
      { id: 2, question: 'Is customer financial data protected according to regulations?' },
      { id: 3, question: 'Are fraud detection mechanisms in place?' }
    ],
    'Healthcare': [
      { id: 1, question: 'Is patient data protected according to HIPAA requirements?' },
      { id: 2, question: 'Are medical devices secured against cyber threats?' },
      { id: 3, question: 'Is there a process for handling security incidents involving PHI?' }
    ],
    'Government / Defense': [
      { id: 1, question: 'Are classified systems properly secured?' },
      { id: 2, question: 'Is there compliance with government security requirements?' },
      { id: 3, question: 'Are supply chain security controls in place?' }
    ],
    'Energy / Utilities': [
      { id: 1, question: 'Are industrial control systems (ICS) properly secured?' },
      { id: 2, question: 'Is there a process for OT/IT security convergence?' },
      { id: 3, question: 'Are critical infrastructure protection measures in place?' }
    ],
    'Telecom': [
      { id: 1, question: 'Are network infrastructure components secured?' },
      { id: 2, question: 'Is customer communication data protected?' },
      { id: 3, question: 'Are there controls to prevent service disruption?' }
    ],
    'Retail / eCommerce': [
      { id: 1, question: 'Is payment card data protected according to PCI DSS?' },
      { id: 2, question: 'Are point-of-sale systems secured?' },
      { id: 3, question: 'Is customer personal information properly protected?' }
    ],
    'Cloud / SaaS': [
      { id: 1, question: 'Are multi-tenant environments properly segregated?' },
      { id: 2, question: 'Is customer data isolated and protected?' },
      { id: 3, question: 'Are there controls for service availability and continuity?' }
    ],
    'Manufacturing / Industrial': [
      { id: 1, question: 'Are industrial systems protected from cyber threats?' },
      { id: 2, question: 'Is there security for connected manufacturing equipment?' },
      { id: 3, question: 'Are supply chain security controls implemented?' }
    ],
    // Add the four domains from the screenshot
    'Organizational': [
      { id: 1, question: 'Does the organization have a documented security governance structure?' },
      { id: 2, question: 'Are roles and responsibilities for security clearly defined?' },
      { id: 3, question: 'Is there a process for security policy review and updates?' }
    ],
    'People': [
      { id: 1, question: 'Are background checks performed for all employees?' },
      { id: 2, question: 'Is security awareness training provided to all staff?' },
      { id: 3, question: 'Are there procedures for handling employee termination?' }
    ],
    'Physical': [
      { id: 1, question: 'Are physical access controls in place for secure areas?' },
      { id: 2, question: 'Is there environmental protection for critical systems?' },
      { id: 3, question: 'Are there procedures for secure equipment disposal?' }
    ],
    'Technological': [
      { id: 1, question: 'Are technical vulnerability assessments performed regularly?' },
      { id: 2, question: 'Is there a patch management process in place?' },
      { id: 3, question: 'Are encryption controls implemented for sensitive data?' }
    ]
  };

  // Combined questions from all selected domains
  combinedQuestions: any[] = [];

  constructor(
    private router: Router,
    private globalDataService: GlobalDataService
  ) { }

  ngOnInit(): void {
    // Get selected domains and benchmarks from the global service
    this.selectedDomains = this.globalDataService.getSelectedDomains();
    this.selectedBenchmarks = this.globalDataService.getSelectedBenchmarks();

    // Combine questions from all selected domains
    this.updateCombinedQuestions();
  }

  updateCombinedQuestions(): void {
    this.combinedQuestions = [];
    this.selectedDomains.forEach(domain => {
      const domainName = domain.name;
      if (this.domainQuestions[domainName]) {
        this.domainQuestions[domainName].forEach((question: any) => {
          this.combinedQuestions.push({
            ...question,
            domain: domainName
          });
        });
      }
    });
  }

  selectOption(option: string): void {
    this.selectedOption = option;
  }

  navigateToUploadQuestionnaire(): void {
    // Store the selected option in the global service
    this.globalDataService.setQuestionSelectionMethod(this.selectedOption);

    if (this.selectedOption === 'hardcoded') {
      // Store the hardcoded questions
      this.globalDataService.setHardcodedQuestions(this.combinedQuestions);
    }

    // Navigate to the evidence analyzer page
    this.router.navigateByUrl('/automated-control-validation');
  }
}
