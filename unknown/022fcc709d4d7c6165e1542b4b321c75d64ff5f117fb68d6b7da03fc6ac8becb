<div class="document-selection-container">
  <div class="header">
    <h4>Select Documents</h4>
    <p class="selection-count">
      Selected: {{ getSelectedCount() }} of {{ getTotalCount() }}
    </p>
    <div class="selection-actions">
      <button mat-button (click)="selectAll()" class="action-button">Select All</button>
      <button mat-button (click)="deselectAll()" class="action-button">Deselect All</button>
    </div>
  </div>
  <mat-divider></mat-divider>

  <div class="document-categories">
    <!-- Standards -->
    @if (documents.Standard.length > 0) {
      <mat-expansion-panel expanded="true">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div class="category-header">
              <mat-checkbox 
                [checked]="categorySelectionState['Standard']"
                [indeterminate]="getCategorySelectedCount('Standard') > 0 && getCategorySelectedCount('Standard') < documents.Standard.length"
                (change)="toggleCategory('Standard')"
                color="primary">
                Standards
              </mat-checkbox>
              <span class="category-count">
                {{ getCategorySelectedCount('Standard') }}/{{ documents.Standard.length }}
              </span>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="document-list">
          @for (doc of documents.Standard; track doc) {
            <div class="document-item">
              <mat-checkbox 
                [(ngModel)]="selectedDocuments[doc]"
                (change)="toggleDocument(doc)"
                color="primary">
                {{ doc }}
              </mat-checkbox>
            </div>
          }
        </div>
      </mat-expansion-panel>
    }

    <!-- Guidelines -->
    @if (documents.Guidelines.length > 0) {
      <mat-expansion-panel expanded="true">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div class="category-header">
              <mat-checkbox 
                [checked]="categorySelectionState['Guidelines']"
                [indeterminate]="getCategorySelectedCount('Guidelines') > 0 && getCategorySelectedCount('Guidelines') < documents.Guidelines.length"
                (change)="toggleCategory('Guidelines')"
                color="primary">
                Guidelines
              </mat-checkbox>
              <span class="category-count">
                {{ getCategorySelectedCount('Guidelines') }}/{{ documents.Guidelines.length }}
              </span>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="document-list">
          @for (doc of documents.Guidelines; track doc) {
            <div class="document-item">
              <mat-checkbox 
                [(ngModel)]="selectedDocuments[doc]"
                (change)="toggleDocument(doc)"
                color="primary">
                {{ doc }}
              </mat-checkbox>
            </div>
          }
        </div>
      </mat-expansion-panel>
    }

    <!-- Frameworks -->
    @if (documents.Framework.length > 0) {
      <mat-expansion-panel expanded="true">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div class="category-header">
              <mat-checkbox 
                [checked]="categorySelectionState['Framework']"
                [indeterminate]="getCategorySelectedCount('Framework') > 0 && getCategorySelectedCount('Framework') < documents.Framework.length"
                (change)="toggleCategory('Framework')"
                color="primary">
                Frameworks
              </mat-checkbox>
              <span class="category-count">
                {{ getCategorySelectedCount('Framework') }}/{{ documents.Framework.length }}
              </span>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="document-list">
          @for (doc of documents.Framework; track doc) {
            <div class="document-item">
              <mat-checkbox 
                [(ngModel)]="selectedDocuments[doc]"
                (change)="toggleDocument(doc)"
                color="primary">
                {{ doc }}
              </mat-checkbox>
            </div>
          }
        </div>
      </mat-expansion-panel>
    }

    <!-- Regulations -->
    @if (documents.Regulation.length > 0) {
      <mat-expansion-panel expanded="true">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div class="category-header">
              <mat-checkbox 
                [checked]="categorySelectionState['Regulation']"
                [indeterminate]="getCategorySelectedCount('Regulation') > 0 && getCategorySelectedCount('Regulation') < documents.Regulation.length"
                (change)="toggleCategory('Regulation')"
                color="primary">
                Regulations
              </mat-checkbox>
              <span class="category-count">
                {{ getCategorySelectedCount('Regulation') }}/{{ documents.Regulation.length }}
              </span>
            </div>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div class="document-list">
          @for (doc of documents.Regulation; track doc) {
            <div class="document-item">
              <mat-checkbox 
                [(ngModel)]="selectedDocuments[doc]"
                (change)="toggleDocument(doc)"
                color="primary">
                {{ doc }}
              </mat-checkbox>
            </div>
          }
        </div>
      </mat-expansion-panel>
    }
  </div>
</div>
