<mat-sidenav-container>
  <mat-sidenav [opened]="sidebarViewService.show$ | async" mode="side">
    <mat-nav-list>

      <mat-list-item routerLink="/home" routerLinkActive="active-link" (click)="updateDisplay()">
        <p matListItemTitle>Home</p>
      </mat-list-item>

      <mat-list-item  routerLink="/isecmapper" routerLinkActive="active-link"
        (click)="updateDisplay()">
        <p matListItemTitle>IsecMapper</p>
      </mat-list-item>

      @if(displayDashboard){
      <mat-list-item routerLink="/control-result" routerLinkActive="active-link" (click)="updateDisplay()">
        <p matListItemTitle>Summary Dashboard</p>
      </mat-list-item>
      }

      <mat-list-item routerLink="/chatbot" routerLinkActive="active-link" (click)="updateDisplay()">
        <p matListItemTitle>QnA Chatbot</p>
      </mat-list-item>

      <mat-list-item routerLink="/pia" routerLinkActive="active-link" (click)="updateDisplay()">
        <p matListItemTitle>PIA</p>
      </mat-list-item>

      <mat-list-item routerLink="/cyber-pioneer" routerLinkActive="active-link" (click)="updateDisplay()">
        <p matListItemTitle>Cyber Pioneer</p>
      </mat-list-item>


    </mat-nav-list>
  </mat-sidenav>
  <mat-sidenav-content>
    <router-outlet></router-outlet>
  </mat-sidenav-content>
</mat-sidenav-container>