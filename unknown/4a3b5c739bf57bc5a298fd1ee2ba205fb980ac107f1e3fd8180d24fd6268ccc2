import re
import difflib
from typing import List, Dict, Any, Optional

def find_best_matching_file(file_name: str, all_files: List[str]) -> Optional[str]:
    """
    Find the best matching file from all_files for the given file_name.

    Args:
        file_name: The file name to match
        all_files: List of all available file names

    Returns:
        The best matching file name or None if no good match found
    """
    # First try exact match
    if file_name in all_files:
        return file_name

    # Try with common extensions if no extension in file_name
    if '.' not in file_name:
        for ext in ['.pdf', '.docx', '.doc', '.txt', '.jpg', '.jpeg', '.png']:
            if file_name + ext in all_files:
                return file_name + ext

    # Try fuzzy matching
    best_match = None
    highest_ratio = 0

    for file in all_files:
        # Compare with and without extension
        file_base = file.rsplit('.', 1)[0]

        # Calculate similarity ratios
        ratio1 = difflib.SequenceMatcher(None, file_name.lower(), file.lower()).ratio()
        ratio2 = difflib.SequenceMatcher(None, file_name.lower(), file_base.lower()).ratio()

        # Use the higher ratio
        ratio = max(ratio1, ratio2)

        # Update best match if this is better
        if ratio > highest_ratio and ratio > 0.7:  # 0.7 is a threshold for "good enough" match
            highest_ratio = ratio
            best_match = file

    return best_match

def extract_referenced_files(observation: str, all_files: List[str]) -> List[str]:
    """
    Extract file names that are referenced in the observation text.

    Args:
        observation: The observation text from the AI
        all_files: List of all file names that were available for analysis

    Returns:
        List of file names that were referenced in the observation
    """
    referenced_files = []

    # First, try to extract files from the structured format
    structured_match = re.search(r'RELEVANT_FILES:\s*(.*?)(?:\n|$)', observation, re.IGNORECASE)
    if structured_match:
        files_text = structured_match.group(1).strip()
        if files_text.lower() != "none":
            # Split by comma and clean up each file name
            file_names = [name.strip() for name in files_text.split(',')]

            # Match these names against actual files
            for file_name in file_names:
                # Find the closest matching file
                best_match = find_best_matching_file(file_name, all_files)
                if best_match and best_match not in referenced_files:
                    referenced_files.append(best_match)

            # If we found files in the structured format, return them
            if referenced_files:
                return referenced_files

    # If no structured format or no files found, fall back to the current approach
    # Sort files by length (descending) to ensure longer file names are matched first
    sorted_files = sorted(all_files, key=len, reverse=True)

    for file in sorted_files:
        # Remove file extension for matching
        file_base = file.rsplit('.', 1)[0]

        # Create patterns to match the file name with or without extension
        patterns = [
            re.escape(file),  # Exact match with extension
            re.escape(file_base),  # Match without extension
            re.escape(file).replace('_', ' '),  # Replace underscores with spaces
            re.escape(file_base).replace('_', ' ')  # Without extension, replace underscores
        ]

        # Check if any pattern matches in the observation
        for pattern in patterns:
            if re.search(r'\b' + pattern + r'\b', observation, re.IGNORECASE):
                referenced_files.append(file)
                break

    return referenced_files

def track_files_for_control(control_id: str, observation: str, file_groups: Dict[str, Any],
                           evidence_folder_path: str) -> List[str]:
    """
    Track which files were used for a specific control based on the observation.

    Args:
        control_id: The control ID
        observation: The observation text from the AI
        file_groups: The file groups dictionary
        evidence_folder_path: Path to the evidence folder

    Returns:
        List of file names that were referenced in the observation
    """
    # Get all files that were available for this control
    if control_id == "all":
        all_text_files = file_groups["all"]["text_files"]
        all_image_files = file_groups["all"]["image_files"]
    else:
        all_text_files = file_groups[control_id]["text_files"]
        all_image_files = file_groups[control_id]["image_files"]

    all_files = all_text_files + all_image_files

    # Extract referenced files from the observation
    referenced_files = extract_referenced_files(observation, all_files)

    # If no files were explicitly referenced, use a smarter fallback
    if not referenced_files:
        # Instead of returning all files, try to find the most relevant ones
        # This could be based on file size, recency, or other heuristics
        # For now, just limit to a maximum of 5 files as a simple improvement
        max_files = 5
        if len(all_files) > max_files:
            # Prioritize PDF files as they're often more comprehensive
            pdfs = [f for f in all_files if f.lower().endswith('.pdf')]
            if len(pdfs) > 0:
                return pdfs[:max_files]
            # Otherwise just take the first few files
            return all_files[:max_files]
        return all_files

    # Limit to a maximum of 5 files
    if len(referenced_files) > 5:
        referenced_files = referenced_files[:5]

    return referenced_files
