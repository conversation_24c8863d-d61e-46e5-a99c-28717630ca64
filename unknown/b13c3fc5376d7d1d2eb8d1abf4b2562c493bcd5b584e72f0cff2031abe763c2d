$fileName = "NIST SP 800-53"
$title = "NIST SP 800-53"

$content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1200 >>
stream
BT
/F1 24 Tf
100 700 Td
($title) Tj
/F2 12 Tf
0 -40 Td
(NIST Special Publication 800-53 provides a catalog of security and privacy) Tj
0 -20 Td
(controls for federal information systems and organizations. It is published by) Tj
0 -20 Td
(the National Institute of Standards and Technology.) Tj
0 -30 Td
/F1 14 Tf
(Control Families:) Tj
/F2 12 Tf
0 -20 Td
(• AC: Access Control) Tj
0 -20 Td
(• AT: Awareness and Training) Tj
0 -20 Td
(• AU: Audit and Accountability) Tj
0 -20 Td
(• CA: Assessment, Authorization, and Monitoring) Tj
0 -20 Td
(• CM: Configuration Management) Tj
0 -20 Td
(• CP: Contingency Planning) Tj
0 -20 Td
(• IA: Identification and Authentication) Tj
0 -20 Td
(• IR: Incident Response) Tj
0 -20 Td
(• MA: Maintenance) Tj
0 -20 Td
(• MP: Media Protection) Tj
0 -20 Td
(• PE: Physical and Environmental Protection) Tj
0 -20 Td
(• PL: Planning) Tj
0 -20 Td
(• PM: Program Management) Tj
0 -20 Td
(• PS: Personnel Security) Tj
0 -20 Td
(• RA: Risk Assessment) Tj
0 -20 Td
(• SA: System and Services Acquisition) Tj
0 -20 Td
(• SC: System and Communications Protection) Tj
0 -20 Td
(• SI: System and Information Integrity) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1625
%%EOF
"@

# Write the PDF file
[System.IO.File]::WriteAllText("$fileName.pdf", $content)

Write-Host "Created PDF with content: $fileName.pdf"
