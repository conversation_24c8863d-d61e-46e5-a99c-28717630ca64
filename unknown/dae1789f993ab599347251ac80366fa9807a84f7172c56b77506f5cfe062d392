import { NgModule, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MaterialModule } from '../../material/material.module';
import { PinoeerHomeComponent } from './components/pinoeer-home.component';
import { PinoeerRoutingModule } from './pinoeer-home-routing.module';

@NgModule({
  declarations: [],
  providers: [],
  imports: [
    CommonModule,
    MaterialModule,
    PinoeerRoutingModule,
    PinoeerHomeComponent,
  ],
})
export class PinoeerHomeModule implements OnInit {

  ngOnInit(): void {
    console.log('PinoeerHomeModule initialized');
  }
}
