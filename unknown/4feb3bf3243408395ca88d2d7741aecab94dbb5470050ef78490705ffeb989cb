"""
Utility functions for Azure Blob Storage operations.
"""
import os
from typing import List, Optional
from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient
from azure.core.exceptions import ResourceExistsError, ResourceNotFoundError

from app.core.config import configs


def get_blob_service_client() -> Optional[BlobServiceClient]:
    """
    Get a BlobServiceClient using the connection string.
    
    Returns:
        BlobServiceClient or None if connection string is not configured
    """
    if not configs.AZURE_STORAGE_CONNECTION_STRING:
        print("Azure Storage connection string is not configured.")
        return None
    
    try:
        return BlobServiceClient.from_connection_string(configs.AZURE_STORAGE_CONNECTION_STRING)
    except Exception as e:
        print(f"Error creating BlobServiceClient: {str(e)}")
        return None


def create_container_if_not_exists() -> bool:
    """
    Create the blob container if it doesn't exist.
    
    Returns:
        True if the container exists or was created, False otherwise
    """
    blob_service_client = get_blob_service_client()
    if not blob_service_client:
        return False
    
    try:
        container_client = blob_service_client.get_container_client(configs.AZURE_STORAGE_CONTAINER_NAME)
        if not container_client.exists():
            container_client.create_container()
            print(f"Container '{configs.AZURE_STORAGE_CONTAINER_NAME}' created.")
        return True
    except Exception as e:
        print(f"Error creating container: {str(e)}")
        return False


def upload_file_to_blob(local_file_path: str, blob_name: Optional[str] = None) -> bool:
    """
    Upload a file to Azure Blob Storage.
    
    Args:
        local_file_path: Path to the local file
        blob_name: Name to use for the blob (defaults to the file name)
        
    Returns:
        True if upload was successful, False otherwise
    """
    if not configs.USE_AZURE_BLOB_STORAGE:
        print("Azure Blob Storage is not enabled.")
        return False
    
    blob_service_client = get_blob_service_client()
    if not blob_service_client:
        return False
    
    # Create the container if it doesn't exist
    if not create_container_if_not_exists():
        return False
    
    # Use the file name as the blob name if not provided
    if not blob_name:
        blob_name = os.path.basename(local_file_path)
    
    try:
        # Get a blob client and upload the file
        blob_client = blob_service_client.get_blob_client(
            container=configs.AZURE_STORAGE_CONTAINER_NAME,
            blob=blob_name
        )
        
        with open(local_file_path, "rb") as data:
            blob_client.upload_blob(data, overwrite=True)
        
        print(f"File '{local_file_path}' uploaded to blob '{blob_name}'.")
        return True
    except Exception as e:
        print(f"Error uploading file to blob: {str(e)}")
        return False


def download_blob_to_file(blob_name: str, local_file_path: str) -> bool:
    """
    Download a blob to a local file.
    
    Args:
        blob_name: Name of the blob to download
        local_file_path: Path to save the downloaded file
        
    Returns:
        True if download was successful, False otherwise
    """
    if not configs.USE_AZURE_BLOB_STORAGE:
        print("Azure Blob Storage is not enabled.")
        return False
    
    blob_service_client = get_blob_service_client()
    if not blob_service_client:
        return False
    
    try:
        # Get a blob client and download the blob
        blob_client = blob_service_client.get_blob_client(
            container=configs.AZURE_STORAGE_CONTAINER_NAME,
            blob=blob_name
        )
        
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
        
        with open(local_file_path, "wb") as download_file:
            download_file.write(blob_client.download_blob().readall())
        
        print(f"Blob '{blob_name}' downloaded to '{local_file_path}'.")
        return True
    except ResourceNotFoundError:
        print(f"Blob '{blob_name}' not found.")
        return False
    except Exception as e:
        print(f"Error downloading blob: {str(e)}")
        return False


def list_blobs(prefix: Optional[str] = None) -> List[str]:
    """
    List all blobs in the container, optionally filtered by prefix.
    
    Args:
        prefix: Optional prefix to filter blobs
        
    Returns:
        List of blob names
    """
    if not configs.USE_AZURE_BLOB_STORAGE:
        print("Azure Blob Storage is not enabled.")
        return []
    
    blob_service_client = get_blob_service_client()
    if not blob_service_client:
        return []
    
    try:
        container_client = blob_service_client.get_container_client(configs.AZURE_STORAGE_CONTAINER_NAME)
        
        # List the blobs, optionally filtered by prefix
        blob_list = container_client.list_blobs(name_starts_with=prefix)
        
        # Extract just the blob names
        return [blob.name for blob in blob_list]
    except ResourceNotFoundError:
        print(f"Container '{configs.AZURE_STORAGE_CONTAINER_NAME}' not found.")
        return []
    except Exception as e:
        print(f"Error listing blobs: {str(e)}")
        return []


def delete_blob(blob_name: str) -> bool:
    """
    Delete a blob from the container.
    
    Args:
        blob_name: Name of the blob to delete
        
    Returns:
        True if deletion was successful, False otherwise
    """
    if not configs.USE_AZURE_BLOB_STORAGE:
        print("Azure Blob Storage is not enabled.")
        return False
    
    blob_service_client = get_blob_service_client()
    if not blob_service_client:
        return False
    
    try:
        blob_client = blob_service_client.get_blob_client(
            container=configs.AZURE_STORAGE_CONTAINER_NAME,
            blob=blob_name
        )
        
        blob_client.delete_blob()
        print(f"Blob '{blob_name}' deleted.")
        return True
    except ResourceNotFoundError:
        print(f"Blob '{blob_name}' not found.")
        return False
    except Exception as e:
        print(f"Error deleting blob: {str(e)}")
        return False
