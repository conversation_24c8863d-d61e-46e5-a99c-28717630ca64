$fileName = "NIST CSF 2.0"

$content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R /F3 6 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 7 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Oblique
>>
endobj
7 0 obj
<< /Length 5000 >>
stream
BT
/F1 24 Tf
50 750 Td
(NIST Cybersecurity Framework 2.0) Tj

/F2 12 Tf
0 -30 Td
(National Institute of Standards and Technology) Tj
0 -15 Td
(Document Version: 1.0) Tj
0 -15 Td
(Last Updated: April 2025) Tj

/F1 18 Tf
-10 -40 Td
(1. Introduction) Tj
/F2 12 Tf
10 -20 Td
(The NIST Cybersecurity Framework (CSF) 2.0 is a voluntary framework consisting of standards,) Tj
0 -15 Td
(guidelines, and best practices to manage cybersecurity risk. Developed by the National Institute) Tj
0 -15 Td
(of Standards and Technology, it provides organizations with a flexible and cost-effective) Tj
0 -15 Td
(approach to enhancing cybersecurity and managing risk. The framework is designed to be) Tj
0 -15 Td
(adaptable to organizations of all sizes across all sectors.) Tj

/F1 18 Tf
-10 -30 Td
(2. History and Development) Tj
/F2 12 Tf
10 -20 Td
(The NIST CSF was initially developed in response to Executive Order 13636, "Improving) Tj
0 -15 Td
(Critical Infrastructure Cybersecurity," issued in February 2013. The first version was) Tj
0 -15 Td
(published in 2014, with version 1.1 released in April 2018. Version 2.0, released in 2023,) Tj
0 -15 Td
(represents a significant update that incorporates feedback from users, evolving threats,) Tj
0 -15 Td
(and new technologies. The framework was developed through collaboration between government) Tj
0 -15 Td
(and the private sector, with input from thousands of people and organizations.) Tj

/F1 18 Tf
-10 -30 Td
(3. Framework Core Components) Tj
/F2 12 Tf
10 -20 Td
(The Framework Core consists of five concurrent and continuous Functions:) Tj
0 -15 Td
(• IDENTIFY (ID): Develop organizational understanding to manage cybersecurity risk to) Tj
0 -15 Td
(  systems, people, assets, data, and capabilities.) Tj
0 -15 Td
(  - Categories: Asset Management, Business Environment, Governance, Risk Assessment,) Tj
0 -15 Td
(    Risk Management Strategy, Supply Chain Risk Management) Tj
0 -15 Td
(• PROTECT (PR): Develop and implement appropriate safeguards to ensure delivery of) Tj
0 -15 Td
(  critical services.) Tj
0 -15 Td
(  - Categories: Identity Management and Access Control, Awareness and Training, Data) Tj
0 -15 Td
(    Security, Information Protection Processes and Procedures, Maintenance, Protective) Tj
0 -15 Td
(    Technology) Tj
0 -15 Td
(• DETECT (DE): Develop and implement appropriate activities to identify the occurrence) Tj
0 -15 Td
(  of a cybersecurity event.) Tj
0 -15 Td
(  - Categories: Anomalies and Events, Security Continuous Monitoring, Detection Processes) Tj
0 -15 Td
(• RESPOND (RS): Develop and implement appropriate activities to take action regarding a) Tj
0 -15 Td
(  detected cybersecurity incident.) Tj
0 -15 Td
(  - Categories: Response Planning, Communications, Analysis, Mitigation, Improvements) Tj
0 -15 Td
(• RECOVER (RC): Develop and implement appropriate activities to maintain plans for) Tj
0 -15 Td
(  resilience and to restore any capabilities or services that were impaired due to a) Tj
0 -15 Td
(  cybersecurity incident.) Tj
0 -15 Td
(  - Categories: Recovery Planning, Improvements, Communications) Tj

/F1 18 Tf
-10 -30 Td
(4. Implementation Tiers) Tj
/F2 12 Tf
10 -20 Td
(The Framework Implementation Tiers provide context on how an organization views) Tj
0 -15 Td
(cybersecurity risk and the processes in place to manage that risk:) Tj
0 -15 Td
(• Tier 1: Partial - Risk management practices are not formalized, and risk is managed in) Tj
0 -15 Td
(  an ad hoc and sometimes reactive manner.) Tj
0 -15 Td
(• Tier 2: Risk Informed - Risk management practices are approved by management but may) Tj
0 -15 Td
(  not be established as organizational-wide policy.) Tj
0 -15 Td
(• Tier 3: Repeatable - The organization's risk management practices are formally approved) Tj
0 -15 Td
(  and expressed as policy.) Tj
0 -15 Td
(• Tier 4: Adaptive - The organization adapts its cybersecurity practices based on lessons) Tj
0 -15 Td
(  learned and predictive indicators derived from previous and current activities.) Tj

/F1 18 Tf
-10 -30 Td
(5. Framework Profiles) Tj
/F2 12 Tf
10 -20 Td
(Framework Profiles represent the outcomes based on business needs that an organization) Tj
0 -15 Td
(has selected from the Framework Categories and Subcategories:) Tj
0 -15 Td
(• Current Profile: The "as is" state of the organization's cybersecurity activities) Tj
0 -15 Td
(• Target Profile: The "to be" state of cybersecurity activities) Tj
0 -15 Td
(Comparing these profiles enables the organization to identify gaps that should be addressed) Tj
0 -15 Td
(to meet cybersecurity risk management objectives.) Tj

/F1 18 Tf
-10 -30 Td
(6. New in CSF 2.0) Tj
/F2 12 Tf
10 -20 Td
(CSF 2.0 includes several significant enhancements:) Tj
0 -15 Td
(• Enhanced governance guidance) Tj
0 -15 Td
(• Expanded supply chain risk management) Tj
0 -15 Td
(• More comprehensive implementation examples) Tj
0 -15 Td
(• Integration with other NIST frameworks (Privacy Framework, AI Risk Management Framework)) Tj
0 -15 Td
(• Updated references to current standards and practices) Tj
0 -15 Td
(• Improved guidance for small and medium-sized businesses) Tj
0 -15 Td
(• Enhanced metrics and measurement approaches) Tj

/F1 18 Tf
-10 -30 Td
(7. Benefits of Implementation) Tj
/F2 12 Tf
10 -20 Td
(• Provides a common language for understanding, managing, and expressing cybersecurity risk) Tj
0 -15 Td
(• Helps identify and prioritize actions for reducing cybersecurity risk) Tj
0 -15 Td
(• Enables more efficient communication about risk with internal and external stakeholders) Tj
0 -15 Td
(• Helps organizations comply with existing regulatory requirements) Tj
0 -15 Td
(• Supports integration of cybersecurity risk into broader enterprise risk management) Tj
0 -15 Td
(• Scales across organizations of different sizes, sectors, and security maturity levels) Tj
0 -15 Td
(• Provides a flexible, risk-based approach rather than prescriptive requirements) Tj
0 -15 Td
(• Enables cost-effective implementation based on business needs and risk tolerance) Tj

/F3 10 Tf
-20 -30 Td
(This document provides an overview of NIST CSF 2.0. For complete and authoritative information,) Tj
0 -15 Td
(please refer to the official NIST Cybersecurity Framework documentation.) Tj
ET
endstream
endobj
xref
0 8
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
0000000444 00000 n
trailer
<< /Size 8
/Root 1 0 R
>>
startxref
5496
%%EOF
"@

# Write the PDF file
[System.IO.File]::WriteAllText("$fileName.pdf", $content)

Write-Host "Created detailed PDF for NIST CSF 2.0"
