"""
Document mappings for region and domain-based selection.
"""
from typing import Dict, List, Optional, Set

# Region-based mapping
REGION_MAPPING = {
    "America": {
        "General": {
            "Cross-Industry": {
                "Standard": ["ISO/IEC 27001", "NIST SP 800-53", "ISO 22301"],
                "Guidelines": ["CIS Benchmarks"],
                "Framework": ["SOC 2 (AICPA)", "MITRE ATT&CK", "NIST CSF 2.0"],
                "Regulation": []
            },
            "Cloud Security": {
                "Standard": [],
                "Guidelines": ["AWS Security Practices", "Azure Security Practices", "GCP Security Practices"],
                "Framework": [],
                "Regulation": []
            },
            "AI": {
                "Standard": ["ISO 42001"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Cloud / SaaS": {
                "Standard": ["ISO/IEC 27017", "ISO/IEC 27018"],
                "Guidelines": [],
                "Framework": ["CSA STAR", "SOC 2 Type II"],
                "Regulation": []
            }
        },
        "Supply Chain": {
            "Supply Chain": {
                "Standard": ["TPRM"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Consumer & Health": {
            "Retail": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "": {
                "Standard": ["ISO/IEC 29100"],
                "Guidelines": [],
                "Framework": ["NIST Privacy Framework"],
                "Regulation": []
            },
            "Healthcare": {
                "Standard": [],
                "Guidelines": [],
                "Framework": [],
                "Regulation": ["HIPAA Security Rule", "HITECH/HITRUST"]
            }
        },
        "Industrial & Energy": {
            "Advanced Manufacturing": {
                "Standard": ["IEC 62443", "NIST SP 800-82", "ISO 28000"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Oil, Gas, Mining & Metals": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Power & Utilities": {
                "Standard": ["IEC 62443", "NIST SP 800-82"],
                "Guidelines": [],
                "Framework": ["DOE C2M2"],
                "Regulation": []
            }
        },
        "Gov't & Public Sector": {
            "Gov't & Public Sector": {
                "Standard": ["NIST SP 800-171"],
                "Guidelines": [],
                "Framework": ["FedRAMP", "CMMC"],
                "Regulation": ["ITAR / EAR"]
            }
        },
        "TMT": {
            "Telecom, Media & Entertainment": {
                "Standard": ["ISO/IEC 27011", "ETSI EN 303 645"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Financial Services & Insurance": {
            "FS & Insurance": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": ["FFIEC CAT"],
                "Regulation": []
            }
        }
    },
    "Philippines": {
        "General": {
            "Cross-Industry": {
                "Standard": ["ISO/IEC 27001", "NIST CSF 2.0"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Cloud Security": {
                "Standard": [],
                "Guidelines": ["AWS Security Practices", "Azure Security Practices", "GCP Security Practices"],
                "Framework": [],
                "Regulation": []
            },
            "Cloud / SaaS": {
                "Standard": ["ISO/IEC 27017", "ISO/IEC 27018"],
                "Guidelines": [],
                "Framework": ["CSA STAR", "SOC 2 Type II"],
                "Regulation": []
            },
            "AI": {
                "Standard": ["ISO 42001"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Supply Chain": {
            "TPRM": {
                "Standard": ["TPRM"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Consumer & Health": {
            "Retail": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "": {
                "Standard": ["ISO/IEC 29100"],
                "Guidelines": [],
                "Framework": ["NIST Privacy Framework"],
                "Regulation": []
            },
            "Healthcare": {
                "Standard": [],
                "Guidelines": [],
                "Framework": [],
                "Regulation": ["HIPAA Security Rule", "HITECH"]
            }
        },
        "Industrial & Energy": {
            "Advanced Manufacturing": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Oil, Gas, Mining & Metals": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Power & Utilities": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": ["DOE C2M2"],
                "Regulation": []
            }
        },
        "Gov't & Public Sector": {
            "Gov't & Public Sector": {
                "Standard": ["NIST SP 800-171"],
                "Guidelines": [],
                "Framework": ["FedRAMP", "CMMC"],
                "Regulation": ["ITAR / EAR"]
            }
        },
        "TMT": {
            "Telecom, Media & Entertainment": {
                "Standard": ["ISO/IEC 27011", "ETSI EN 303 645"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Financial Services & Insurance": {
            "FS & Insurance": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": ["FFIEC CAT"],
                "Regulation": []
            }
        }
    },
    "EMEA": {
        "General": {
            "Cross-Industry": {
                "Standard": ["ISO/IEC 27001", "ISO/IEC 27002", "ISO 22301"],
                "Guidelines": [],
                "Framework": ["CIS Benchmarks", "MITRE ATT&CK"],
                "Regulation": []
            },
            "Cloud Security": {
                "Standard": [],
                "Guidelines": ["AWS Security Practices", "Azure Security Practices", "GCP Security Practices"],
                "Framework": [],
                "Regulation": []
            },
            "AI": {
                "Standard": ["ISO 42001"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Cloud / SaaS": {
                "Standard": ["ISO/IEC 27017", "ISO/IEC 27018"],
                "Guidelines": [],
                "Framework": ["CSA STAR", "SOC 2 Type II"],
                "Regulation": []
            }
        },
        "Supply Chain (TPRM)": {
            "TPRM": {
                "Standard": ["TPRM"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Consumer & Health": {
            "Retail": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "": {
                "Standard": ["ISO/IEC 29100"],
                "Guidelines": [],
                "Framework": ["NIST Privacy Framework"],
                "Regulation": []
            },
            "Healthcare": {
                "Standard": [],
                "Guidelines": [],
                "Framework": [],
                "Regulation": ["HIPAA Security Rule", "HITECH"]
            }
        },
        "Industrial & Energy": {
            "Advanced Manufacturing": {
                "Standard": ["IEC 62443", "ISO 28000"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Oil, Gas, Mining & Metals": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Power & Utilities": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": ["DOE C2M2"],
                "Regulation": []
            }
        },
        "Gov't & Public Sector": {
            "Gov't & Public Sector": {
                "Standard": ["NIST SP 800-171"],
                "Guidelines": [],
                "Framework": ["FedRAMP", "CMMC"],
                "Regulation": ["ITAR / EAR"]
            }
        },
        "TMT": {
            "Telecom, Media & Entertainment": {
                "Standard": ["ISO/IEC 27011", "ETSI EN 303 645"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Financial Services & Insurance": {
            "FS & Insurance": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": ["FFIEC CAT"],
                "Regulation": ["NIS2 & DORA"]
            }
        }
    },
    "MENA": {
        "General": {
            "Cross-Industry": {
                "Standard": ["ISO/IEC 27001"],
                "Guidelines": [],
                "Framework": ["CIS Benchmarks"],
                "Regulation": []
            },
            "Cloud Security": {
                "Standard": ["AWS Security Practices", "Azure Security Practices", "GCP Security Practices"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "AI": {
                "Standard": ["ISO 42001"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Cloud / SaaS": {
                "Standard": [],
                "Guidelines": [],
                "Framework": ["ISO/IEC 27018", "CSA STAR", "SOC 2 Type II"],
                "Regulation": []
            }
        },
        "Supply Chain (TPRM)": {
            "TPRM": {
                "Standard": ["TPRM"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Consumer & Health": {
            "Retail": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "": {
                "Standard": ["ISO/IEC 29100", "NIST Privacy Framework"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Healthcare": {
                "Standard": ["HIPAA Security Rule", "HITECH", "ISO/IEC 27799"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Industrial & Energy": {
            "Advanced Manufacturing": {
                "Standard": ["IEC 62443", "ISO 28000"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Oil, Gas, Mining & Metals": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Power & Utilities": {
                "Standard": ["IEC 62443", "DOE C2M2"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Gov't & Public Sector": {
            "Gov't & Public Sector": {
                "Standard": ["NIST SP 800-171", "FedRAMP", "CMMC", "ITAR / EAR"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "TMT": {
            "Telecom, Media & Entertainment": {
                "Standard": ["ISO/IEC 27011", "ETSI EN 303 645"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Financial Services & Insurance": {
            "FS & Insurance": {
                "Standard": ["PCI DSS", "FFIEC CAT", "NIS2 & DORA"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        }
    },
    "Oceania": {
        "General": {
            "Cross-Industry": {
                "Standard": ["ISO/IEC 27001", "NIST CSF"],
                "Guidelines": ["Essential Eight; NZISM"],
                "Framework": ["National Security Guidelines"],
                "Regulation": []
            },
            "Cloud Security": {
                "Standard": ["AWS Security Practices", "Azure Security Practices", "GCP Security Practices"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "AI": {
                "Standard": ["ISO 42001"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Cloud / SaaS": {
                "Standard": ["ISO/IEC 27017", "ISO/IEC 27018"],
                "Guidelines": [],
                "Framework": ["CSA STAR", "SOC 2 Type II"],
                "Regulation": []
            }
        },
        "Supply Chain (TPRM)": {
            "TPRM": {
                "Standard": ["TPRM"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Consumer & Health": {
            "Retail": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "": {
                "Standard": ["ISO/IEC 29100", "NIST Privacy Framework"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Healthcare": {
                "Standard": ["HIPAA Security Rule", "HITECH", "ISO/IEC 27799"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Industrial & Energy": {
            "Advanced Manufacturing": {
                "Standard": ["IEC 62443", "ISO 28000"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Oil, Gas, Mining & Metals": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Power & Utilities": {
                "Standard": ["IEC 62443", "DOE C2M2"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Gov't & Public Sector": {
            "Gov't & Public Sector": {
                "Standard": ["NIST SP 800-171", "FedRAMP", "CMMC", "ITAR / EAR"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "TMT": {
            "Telecom, Media & Entertainment": {
                "Standard": ["ISO/IEC 27011", "ETSI EN 303 645"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Financial Services & Insurance": {
            "FS & Insurance": {
                "Standard": ["PCI DSS", "FFIEC CAT"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        }
    },
    "APAC (Excluding Philippines)": {
        "General": {
            "Cross-Industry": {
                "Standard": ["ISO 27001"],
                "Guidelines": [],
                "Framework": ["NIST CSF 2.0"],
                "Regulation": []
            },
            "Cloud Security": {
                "Standard": [],
                "Guidelines": ["AWS Security Practices", "Azure Security Practices", "GCP Security Practices"],
                "Framework": [],
                "Regulation": []
            },
            "Cloud / SaaS": {
                "Standard": ["ISO/IEC 27017", "ISO/IEC 27018"],
                "Guidelines": [],
                "Framework": ["CSA STAR", "SOC 2 Type II"],
                "Regulation": []
            },
            "AI": {
                "Standard": ["ISO 42001"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Supply Chain (TPRM)": {
            "TPRM": {
                "Standard": ["TPRM"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Consumer & Health": {
            "Retail": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "": {
                "Standard": ["ISO/IEC 29100"],
                "Guidelines": [],
                "Framework": ["NIST Privacy Framework"],
                "Regulation": []
            },
            "Healthcare": {
                "Standard": [],
                "Guidelines": [],
                "Framework": [],
                "Regulation": ["HIPAA Security Rule", "HITECH"]
            }
        },
        "Industrial & Energy": {
            "Advanced Manufacturing": {
                "Standard": ["IEC 62443", "NIST SP 800-82", "ISO 28000"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Oil, Gas, Mining & Metals": {
                "Standard": ["IEC 62443"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            },
            "Power & Utilities": {
                "Standard": ["IEC 62443", "NIST SP 800-82"],
                "Guidelines": [],
                "Framework": ["DOE C2M2"],
                "Regulation": []
            }
        },
        "Gov't & Public Sector": {
            "Gov't & Public Sector": {
                "Standard": ["NIST SP 800-171"],
                "Guidelines": [],
                "Framework": ["FedRAMP", "CMMC"],
                "Regulation": ["ITAR / EAR"]
            }
        },
        "TMT": {
            "Telecom, Media & Entertainment": {
                "Standard": ["ISO/IEC 27011", "ETSI EN 303 645"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }
        },
        "Financial Services & Insurance": {
            "FS & Insurance": {
                "Standard": ["PCI DSS"],
                "Guidelines": [],
                "Framework": [],
                "Regulation": ["FFIEC CAT"]
            }
        }
    }
}

# Domain-based mapping
DOMAIN_MAPPING = {
    "Cloud Security/SaaS": {
        "Azure": {
            "Standard": ["ISO/IEC 27017", "ISO/IEC 27018"],
            "Guidelines": ["Azure Security Practices"],
            "Framework": ["CSA STAR", "SOC 2 Type II"],
            "Regulation": []
        },
        "AWS": {
            "Standard": ["ISO/IEC 27017", "ISO/IEC 27018"],
            "Guidelines": ["AWS Security Practices"],
            "Framework": ["SOC 2 Type II", "CSA STAR"],
            "Regulation": []
        },
        "GCP": {
            "Standard": ["ISO/IEC 27017", "ISO/IEC 27018"],
            "Guidelines": ["GCP Security Practices"],
            "Framework": ["SOC 2 Type II", "CSA STAR"],
            "Regulation": []
        }
    },
    "AI": {
        "": {
            "Standard": [
                "ISO 42001",
                "NIST AT 100",
                "NIST AI 600",
                "NIST AI RMF",
                "OWASP Top 10 for LLM",
                "Responsible AI Standard (Microsoft)"
            ],
            "Guidelines": [
                "Open AI Guidelines",
                "Meta| Responsible AI Practices"
            ],
            "Framework": ["ENISA AI Threat Landscape"],
            "Regulation": ["EU AI Act", "GDPR"]
        }
    },
    "TPRM": {
        "": {
            "Standard": ["TPRM/NIST"],
            "Guidelines": [],
            "Framework": [],
            "Regulation": []
        }
    },
    "OT": {
        "": {
            "Standard": ["NIST 800-82/ NIST 800-83"],
            "Guidelines": ["(temporary mapping)"],
            "Framework": [],
            "Regulation": []
        }
    }
}

def get_documents_for_region_selection(region: str, industry: str, sector: str) -> Dict[str, List[str]]:
    """
    Get the list of documents for a specific region, industry, and sector selection.
    
    Args:
        region: The selected region
        industry: The selected industry
        sector: The selected sector
        
    Returns:
        Dictionary with document categories and their document lists
    """
    result = {
        "Standard": [],
        "Guidelines": [],
        "Framework": [],
        "Regulation": []
    }
    
    if region in REGION_MAPPING:
        if industry in REGION_MAPPING[region]:
            if sector in REGION_MAPPING[region][industry]:
                for category in result:
                    result[category] = REGION_MAPPING[region][industry][sector].get(category, [])
    
    return result

def get_documents_for_domain_selection(domain: str, csp: Optional[str] = None) -> Dict[str, List[str]]:
    """
    Get the list of documents for a specific domain and CSP selection.
    
    Args:
        domain: The selected domain
        csp: The selected CSP (only applicable for Cloud Security/SaaS)
        
    Returns:
        Dictionary with document categories and their document lists
    """
    result = {
        "Standard": [],
        "Guidelines": [],
        "Framework": [],
        "Regulation": []
    }
    
    if domain in DOMAIN_MAPPING:
        # For Cloud Security/SaaS, we need a CSP
        if domain == "Cloud Security/SaaS":
            if csp and csp in DOMAIN_MAPPING[domain]:
                for category in result:
                    result[category] = DOMAIN_MAPPING[domain][csp].get(category, [])
        else:
            # For other domains, use the empty key
            if "" in DOMAIN_MAPPING[domain]:
                for category in result:
                    result[category] = DOMAIN_MAPPING[domain][""].get(category, [])
    
    return result

def get_all_documents() -> Set[str]:
    """
    Get a set of all document names from both mappings.
    
    Returns:
        Set of all document names
    """
    all_docs = set()
    
    # Add documents from region mapping
    for region, industries in REGION_MAPPING.items():
        for industry, sectors in industries.items():
            for sector, categories in sectors.items():
                for category, docs in categories.items():
                    all_docs.update(docs)
    
    # Add documents from domain mapping
    for domain, csps in DOMAIN_MAPPING.items():
        for csp, categories in csps.items():
            for category, docs in categories.items():
                all_docs.update(docs)
    
    return all_docs

def get_combined_documents(
    region: str, 
    industry: str, 
    sector: str, 
    domain: str, 
    csp: Optional[str] = None
) -> Dict[str, List[str]]:
    """
    Get the combined list of documents from both region and domain selections.
    
    Args:
        region: The selected region
        industry: The selected industry
        sector: The selected sector
        domain: The selected domain
        csp: The selected CSP (only applicable for Cloud Security/SaaS)
        
    Returns:
        Dictionary with document categories and their document lists
    """
    region_docs = get_documents_for_region_selection(region, industry, sector)
    domain_docs = get_documents_for_domain_selection(domain, csp)
    
    result = {
        "Standard": [],
        "Guidelines": [],
        "Framework": [],
        "Regulation": []
    }
    
    # Combine documents from both selections
    for category in result:
        result[category] = list(set(region_docs[category] + domain_docs[category]))
    
    return result
