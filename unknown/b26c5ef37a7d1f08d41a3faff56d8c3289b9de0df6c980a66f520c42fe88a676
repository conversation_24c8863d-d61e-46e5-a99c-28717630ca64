# Define a function to create a PDF with content
function Create-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$KeyPoints
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1500 >>
stream
BT
/F1 24 Tf
100 700 Td
($Title) Tj
/F2 12 Tf
0 -40 Td
($Description) Tj
0 -30 Td
/F1 14 Tf
(Key Components:) Tj
/F2 12 Tf
0 -20 Td
($KeyPoints) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1925
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created PDF with content: $safeFileName.pdf"
}

# 1. ISO/IEC 27001
Create-PDF -FileName "ISO/IEC 27001" `
    -Title "ISO/IEC 27001" `
    -Description "ISO/IEC 27001 is an international standard for information security management. It specifies the requirements for establishing, implementing, maintaining, and continually improving an information security management system (ISMS)." `
    -KeyPoints "• Information Security Policies
• Organization of Information Security
• Human Resource Security
• Asset Management
• Access Control
• Cryptography
• Physical and Environmental Security
• Operations Security
• Communications Security
• System Acquisition, Development and Maintenance
• Supplier Relationships
• Information Security Incident Management
• Business Continuity Management
• Compliance"

# 2. NIST CSF 2.0
Create-PDF -FileName "NIST CSF 2.0" `
    -Title "NIST CSF 2.0" `
    -Description "The NIST Cybersecurity Framework 2.0 is a voluntary framework developed by the National Institute of Standards and Technology to help organizations better manage and reduce cybersecurity risk." `
    -KeyPoints "• IDENTIFY: Develop organizational understanding to manage cybersecurity risk
• PROTECT: Develop and implement appropriate safeguards
• DETECT: Develop and implement appropriate activities to identify cybersecurity events
• RESPOND: Develop and implement appropriate activities to take action regarding incidents
• RECOVER: Develop and implement appropriate activities to maintain plans for resilience
• Enhanced governance guidance
• Supply chain risk management
• Expanded implementation examples"

# 3. NIST SP 800-53
Create-PDF -FileName "NIST SP 800-53" `
    -Title "NIST SP 800-53" `
    -Description "NIST Special Publication 800-53 provides a catalog of security and privacy controls for federal information systems and organizations. It is published by the National Institute of Standards and Technology." `
    -KeyPoints "• AC: Access Control
• AT: Awareness and Training
• AU: Audit and Accountability
• CA: Assessment, Authorization, and Monitoring
• CM: Configuration Management
• CP: Contingency Planning
• IA: Identification and Authentication
• IR: Incident Response
• MA: Maintenance
• MP: Media Protection
• PE: Physical and Environmental Protection
• PL: Planning
• PM: Program Management
• PS: Personnel Security
• RA: Risk Assessment
• SA: System and Services Acquisition
• SC: System and Communications Protection
• SI: System and Information Integrity"

# 4. CIS Benchmarks
Create-PDF -FileName "CIS Benchmarks" `
    -Title "CIS Benchmarks" `
    -Description "CIS Benchmarks are consensus-developed secure configuration guidelines for hardening systems and applications. They are published by the Center for Internet Security." `
    -KeyPoints "• Operating System Benchmarks (Windows, Linux, macOS)
• Server Software Benchmarks (Web servers, database servers)
• Cloud Provider Benchmarks (AWS, Azure, GCP)
• Mobile Device Benchmarks
• Network Device Benchmarks
• Desktop Software Benchmarks
• Multi-function Print Device Benchmarks
• Virtualization Benchmarks"

# 5. ISO 22301
Create-PDF -FileName "ISO 22301" `
    -Title "ISO 22301" `
    -Description "ISO 22301 is an international standard for business continuity management systems. It provides a framework to plan, establish, implement, operate, monitor, review, maintain and continually improve a business continuity management system." `
    -KeyPoints "• Business Continuity Policy
• Business Impact Analysis
• Risk Assessment
• Business Continuity Strategy
• Business Continuity Procedures
• Exercising and Testing
• Performance Evaluation
• Management Review
• Continual Improvement"

Write-Host "Created first batch of PDFs (1-5)"
