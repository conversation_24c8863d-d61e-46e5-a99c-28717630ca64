# imports from packages
from fastapi import UploadFile
import os
import shutil
from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
)
from llama_index.core import Settings
from llama_index.core.memory import Chat<PERSON><PERSON>ory<PERSON><PERSON><PERSON>

# from llama_index.readers.file import PyMuPDFReader

# custom imports
from app.core.config import configs
from app.core.llama_prompts import llama_prompts
from app.util.utils import get_unique_id, create_dir

# TODO: check if the below works in deployment
import nest_asyncio

nest_asyncio.apply()

Settings.llm = configs.llama_aoai_chat
Settings.embed_model = configs.llama_aoai_embedding


def save_file_create_embeddings(files: list[UploadFile]) -> dict:
    """
    Saves the file to the temp storage and returns the file name, unique id
    """
    id = get_unique_id()

    folder_path = os.path.join(
        configs.curr_dir, configs.TEMP_CONSTANT, configs.CHATBOT_TEMP_FILE_PATH, id
    )

    create_dir(folder_path)
    for file in files:
        file_name = file.filename
        file_path = os.path.join(folder_path, file_name)
        with open(file_path, "wb") as buffer:
            buffer.write(file.file.read())

    # create embeddings
    create_index(folder_path=folder_path)

    return {"id": id}


def create_index(folder_path: str):
    print("Creating vector store index")
    storage_context = StorageContext.from_defaults()

    # file_extractor = {".pdf": PyMuPDFReader()}
    # documents = SimpleDirectoryReader(input_dir=folder_path,file_extractor=file_extractor).load_data()
    documents = SimpleDirectoryReader(input_dir=folder_path).load_data()

    index = VectorStoreIndex(  # noqa: F841
        documents,
        show_progress=True,
        # service_context=service_context,
        storage_context=storage_context,
        use_async=True,
    )
    presist_dir = os.path.join(folder_path, configs.LLAMA_INDEX_VECTOR_STORE)
    storage_context.persist(persist_dir=presist_dir)


def load_index_from_localstorage(unique_id: str):
    print("Loading vector store index from local")
    # if not os.listdir(configs.LLAMA_INDEX_VECTOR_STORE):
    #     # create_index(service_context=service_context)
    #     create_index()

    persist_dir = os.path.join(
        configs.curr_dir,
        configs.TEMP_CONSTANT,
        configs.CHATBOT_TEMP_FILE_PATH,
        unique_id,
        configs.LLAMA_INDEX_VECTOR_STORE,
    )
    storage_context = StorageContext.from_defaults(persist_dir=persist_dir)

    cur_index = load_index_from_storage(storage_context=storage_context)
    # service_context=service_context

    return cur_index


def get_llama_chatengine(unique_id: str):
    """
    Returns the chat engine for the given unique id
    """
    vector_index = load_index_from_localstorage(unique_id)
    memory = ChatMemoryBuffer.from_defaults(llm=configs.llama_aoai_chat)
    query_engine = vector_index.as_query_engine(similarity_top_k=20)

    chat_engine = vector_index.as_chat_engine(
        chat_mode="context",
        memory=memory,
        system_prompt=llama_prompts.llama_default_prompt,
        verbose=True,
        query_engine=query_engine,
    )

    return chat_engine


def stream_chat_response(chat_engine, message: str):
    response = chat_engine.stream_chat(message)

    for token in response.response_gen:
        # for char in token:
        yield token


def delete_llama_index(unique_id: str):
    """
    Deletes the index from the storage
    """
    folder_path = os.path.join(
        configs.curr_dir,
        configs.TEMP_CONSTANT,
        configs.CHATBOT_TEMP_FILE_PATH,
        unique_id,
    )
    shutil.rmtree(folder_path)
