import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface DocumentCategories {
  Standard: string[];
  Guidelines: string[];
  Framework: string[];
  Regulation: string[];
  [key: string]: string[];
}

@Component({
  selector: 'app-simple-document-selection',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  template: `
    <div class="document-selection-container">
      <div class="header">
        <h4>Select Documents</h4>
        <p class="selection-count">
          Selected: {{ getSelectedCount() }} of {{ getTotalCount() }}
        </p>
        <div class="selection-actions">
          <button (click)="selectAll()" class="action-button">Select All</button>
          <button (click)="deselectAll()" class="action-button">Deselect All</button>
        </div>
      </div>
      <hr>

      <div class="document-categories">
        <!-- Standards -->
        <div *ngIf="documents.Standard.length > 0" class="category-section">
          <div class="category-header">
            <label>
              <input 
                type="checkbox" 
                [checked]="categorySelectionState['Standard']"
                (change)="toggleCategory('Standard')"
              >
              <span class="category-title">Standards</span>
            </label>
            <span class="category-count">
              {{ getCategorySelectedCount('Standard') }}/{{ documents.Standard.length }}
            </span>
          </div>
          <div class="document-list">
            <div *ngFor="let doc of documents.Standard" class="document-item">
              <label>
                <input 
                  type="checkbox" 
                  [(ngModel)]="selectedDocuments[doc]"
                  (change)="toggleDocument(doc)"
                >
                <span>{{ doc }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Guidelines -->
        <div *ngIf="documents.Guidelines.length > 0" class="category-section">
          <div class="category-header">
            <label>
              <input 
                type="checkbox" 
                [checked]="categorySelectionState['Guidelines']"
                (change)="toggleCategory('Guidelines')"
              >
              <span class="category-title">Guidelines</span>
            </label>
            <span class="category-count">
              {{ getCategorySelectedCount('Guidelines') }}/{{ documents.Guidelines.length }}
            </span>
          </div>
          <div class="document-list">
            <div *ngFor="let doc of documents.Guidelines" class="document-item">
              <label>
                <input 
                  type="checkbox" 
                  [(ngModel)]="selectedDocuments[doc]"
                  (change)="toggleDocument(doc)"
                >
                <span>{{ doc }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Frameworks -->
        <div *ngIf="documents.Framework.length > 0" class="category-section">
          <div class="category-header">
            <label>
              <input 
                type="checkbox" 
                [checked]="categorySelectionState['Framework']"
                (change)="toggleCategory('Framework')"
              >
              <span class="category-title">Frameworks</span>
            </label>
            <span class="category-count">
              {{ getCategorySelectedCount('Framework') }}/{{ documents.Framework.length }}
            </span>
          </div>
          <div class="document-list">
            <div *ngFor="let doc of documents.Framework" class="document-item">
              <label>
                <input 
                  type="checkbox" 
                  [(ngModel)]="selectedDocuments[doc]"
                  (change)="toggleDocument(doc)"
                >
                <span>{{ doc }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Regulations -->
        <div *ngIf="documents.Regulation.length > 0" class="category-section">
          <div class="category-header">
            <label>
              <input 
                type="checkbox" 
                [checked]="categorySelectionState['Regulation']"
                (change)="toggleCategory('Regulation')"
              >
              <span class="category-title">Regulations</span>
            </label>
            <span class="category-count">
              {{ getCategorySelectedCount('Regulation') }}/{{ documents.Regulation.length }}
            </span>
          </div>
          <div class="document-list">
            <div *ngFor="let doc of documents.Regulation" class="document-item">
              <label>
                <input 
                  type="checkbox" 
                  [(ngModel)]="selectedDocuments[doc]"
                  (change)="toggleDocument(doc)"
                >
                <span>{{ doc }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .document-selection-container {
      padding: 20px;
      background-color: #252531;
      border-radius: 10px;
      border: 1px solid #333340;
    }

    .header {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
    }

    .selection-count {
      color: #ccc;
      margin-top: 5px;
      margin-bottom: 10px;
      font-weight: bold;
    }

    .selection-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
    }

    .action-button {
      background-color: #333340;
      color: #fff;
      font-size: 14px;
      padding: 5px 10px;
      border: 1px solid #444;
      cursor: pointer;
    }

    .action-button:hover {
      background-color: #444450;
    }

    .document-categories {
      margin-top: 20px;
    }

    .category-section {
      margin-bottom: 20px;
      padding: 10px;
      background-color: #333340;
      border-radius: 5px;
    }

    .category-header {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 5px 0;
      border-bottom: 1px solid #444;
      margin-bottom: 10px;
    }

    .category-title {
      margin-left: 8px;
      font-weight: bold;
      color: #fff;
    }

    .category-count {
      margin-left: auto;
      color: #ccc;
      font-size: 14px;
      font-weight: bold;
    }

    .document-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px 0;
      max-height: 300px;
      overflow-y: auto;
    }

    .document-item {
      padding: 8px 5px;
      border-bottom: 1px solid #333340;
    }

    .document-item:hover {
      background-color: #252531;
    }

    .document-item label {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #fff;
    }

    .document-item span {
      margin-left: 8px;
    }

    input[type="checkbox"] {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }

    hr {
      border: none;
      border-top: 1px solid #333340;
      margin: 15px 0;
    }

    /* Scrollbar styling */
    .document-list::-webkit-scrollbar {
      width: 8px;
    }

    .document-list::-webkit-scrollbar-track {
      background: #252531;
    }

    .document-list::-webkit-scrollbar-thumb {
      background-color: #444;
      border-radius: 4px;
    }

    .document-list::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  `]
})
export class SimpleDocumentSelectionComponent implements OnInit {
  @Input() documents: DocumentCategories = {
    Standard: [],
    Guidelines: [],
    Framework: [],
    Regulation: []
  };

  @Output() selectionChanged = new EventEmitter<string[]>();

  selectedDocuments: { [key: string]: boolean } = {};
  
  // Track selection state for each category
  categorySelectionState: { [key: string]: boolean } = {
    Standard: false,
    Guidelines: false,
    Framework: false,
    Regulation: false
  };

  constructor() { }

  ngOnInit(): void {
    this.initializeSelectedDocuments();
  }

  initializeSelectedDocuments(): void {
    // Initialize all documents as selected by default
    for (const category in this.documents) {
      if (this.documents[category].length > 0) {
        for (const doc of this.documents[category]) {
          this.selectedDocuments[doc] = true;
        }
      }
    }
    
    // Update category selection states
    this.updateCategorySelectionStates();
    
    // Emit initial selection
    this.emitSelectedDocuments();
  }

  toggleDocument(document: string): void {
    this.selectedDocuments[document] = !this.selectedDocuments[document];
    
    // Update category selection states
    this.updateCategorySelectionStates();
    
    // Emit updated selection
    this.emitSelectedDocuments();
  }

  toggleCategory(category: string): void {
    const newState = !this.categorySelectionState[category];
    
    // Update all documents in this category
    for (const doc of this.documents[category]) {
      this.selectedDocuments[doc] = newState;
    }
    
    // Update category selection state
    this.categorySelectionState[category] = newState;
    
    // Emit updated selection
    this.emitSelectedDocuments();
  }

  updateCategorySelectionStates(): void {
    for (const category in this.documents) {
      if (this.documents[category].length > 0) {
        // Check if all documents in this category are selected
        const allSelected = this.documents[category].every((doc: string) => this.selectedDocuments[doc]);
        this.categorySelectionState[category] = allSelected;
      }
    }
  }

  emitSelectedDocuments(): void {
    const selected: string[] = [];
    
    for (const doc in this.selectedDocuments) {
      if (this.selectedDocuments[doc]) {
        selected.push(doc);
      }
    }
    
    this.selectionChanged.emit(selected);
  }

  selectAll(): void {
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.selectedDocuments[doc] = true;
      }
      this.categorySelectionState[category] = true;
    }
    
    this.emitSelectedDocuments();
  }

  deselectAll(): void {
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.selectedDocuments[doc] = false;
      }
      this.categorySelectionState[category] = false;
    }
    
    this.emitSelectedDocuments();
  }

  getSelectedCount(): number {
    let count = 0;
    for (const doc in this.selectedDocuments) {
      if (this.selectedDocuments[doc]) {
        count++;
      }
    }
    return count;
  }

  getTotalCount(): number {
    let count = 0;
    for (const category in this.documents) {
      count += this.documents[category].length;
    }
    return count;
  }

  getCategorySelectedCount(category: string): number {
    let count = 0;
    for (const doc of this.documents[category]) {
      if (this.selectedDocuments[doc]) {
        count++;
      }
    }
    return count;
  }
}
