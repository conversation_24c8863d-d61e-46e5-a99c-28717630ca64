$fileNames = @(
    "ISO/IEC 27001",
    "NIST CSF 2.0",
    "NIST SP 800-53",
    "CIS Benchmarks",
    "ISO 22301",
    "SOC 2 (AICPA)",
    "MITRE ATT@CK",
    "AWS Security Practice",
    "Azure Security Practice",
    "GCP Security Practice",
    "ISO 42001",
    "PCI DSS",
    "FFIEC CAT",
    "NIS2",
    "DORA",
    "HIPAA Security Rule",
    "HITECH",
    "ISO/IEC 27799",
    "NIST SP 800-171",
    "FedRAMP",
    "CMMC",
    "ITAR/EAR",
    "IEC 62443",
    "DOE C2M2",
    "NIST SP 800-82",
    "ISO/IEC 27011",
    "ETSI EN 303 645",
    "ISO/IEC 29100",
    "NIST Privacy Framework",
    "ISO/IEC 27017",
    "ISO/IEC 27018",
    "CSA STAR",
    "SOC 2 Type II",
    "ISO 28000"
)

# Create a minimal valid PDF template
$pdfTemplate = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 5 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
5 0 obj
<< /Length 68 >>
stream
BT
/F1 24 Tf
100 700 Td
(TITLE_PLACEHOLDER) Tj
ET
endstream
endobj
xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000302 00000 n
trailer
<< /Size 6
/Root 1 0 R
>>
startxref
420
%%EOF
"@

foreach ($fileName in $fileNames) {
    # Replace invalid characters in filename
    $safeFileName = $fileName -replace '/', '-' -replace '@', 'at'
    $pdfFileName = "$safeFileName.pdf"
    
    # Create PDF content with the title
    $pdfContent = $pdfTemplate -replace 'TITLE_PLACEHOLDER', $fileName
    
    # Write the PDF file
    [System.IO.File]::WriteAllText($pdfFileName, $pdfContent)
    
    Write-Host "Created valid PDF: $pdfFileName"
}

Write-Host "All 34 valid PDF files have been created."
