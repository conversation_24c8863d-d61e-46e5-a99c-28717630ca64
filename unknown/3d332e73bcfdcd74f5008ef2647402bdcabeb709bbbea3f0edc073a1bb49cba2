# Define a function to create a PDF with content
function Create-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$KeyPoints
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1500 >>
stream
BT
/F1 24 Tf
100 700 Td
($Title) Tj
/F2 12 Tf
0 -40 Td
($Description) Tj
0 -30 Td
/F1 14 Tf
(Key Components:) Tj
/F2 12 Tf
0 -20 Td
($KeyPoints) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1925
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created PDF with content: $safeFileName.pdf"
}

# 26. ISO/IEC 27011
Create-PDF -FileName "ISO/IEC 27011" `
    -Title "ISO/IEC 27011" `
    -Description "ISO/IEC 27011 is an international standard that provides guidelines for information security management in telecommunications organizations based on ISO/IEC 27002." `
    -KeyPoints "• Telecommunications-specific Security Controls
• Network Infrastructure Security
• Service Delivery Management
• Customer Information Protection
• Business Continuity for Telecommunications
• Telecommunications Equipment Security
• Network Management Security
• Service Monitoring
• Incident Management for Telecommunications
• Compliance with Legal and Regulatory Requirements
• Physical Security for Telecommunications Facilities
• Personnel Security in Telecommunications Organizations
• Third-party Management for Telecommunications Services"

# 27. ETSI EN 303 645
Create-PDF -FileName "ETSI EN 303 645" `
    -Title "ETSI EN 303 645" `
    -Description "ETSI EN 303 645 is a European standard for cybersecurity in Internet of Things (IoT) devices. It establishes a security baseline for internet-connected consumer products and provides a basis for future IoT certification schemes." `
    -KeyPoints "• No Universal Default Passwords
• Implement a Vulnerability Disclosure Policy
• Keep Software Updated
• Securely Store Sensitive Security Parameters
• Communicate Securely
• Minimize Exposed Attack Surfaces
• Ensure Software Integrity
• Ensure Personal Data Protection
• Make Systems Resilient to Outages
• Examine System Telemetry Data
• Make it Easy for Users to Delete Personal Data
• Make Installation and Maintenance of Devices Easy
• Validate Input Data"

# 28. ISO/IEC 29100
Create-PDF -FileName "ISO/IEC 29100" `
    -Title "ISO/IEC 29100" `
    -Description "ISO/IEC 29100 is an international standard that provides a privacy framework for the protection of personally identifiable information (PII). It defines privacy safeguarding requirements for information technology systems." `
    -KeyPoints "• Consent and Choice
• Purpose Legitimacy and Specification
• Collection Limitation
• Data Minimization
• Use, Retention and Disclosure Limitation
• Accuracy and Quality
• Openness, Transparency and Notice
• Individual Participation and Access
• Accountability
• Information Security
• Privacy Compliance
• Privacy by Design
• Privacy Impact Assessment"

# 29. NIST Privacy Framework
Create-PDF -FileName "NIST Privacy Framework" `
    -Title "NIST Privacy Framework" `
    -Description "The NIST Privacy Framework is a voluntary tool developed to help organizations identify and manage privacy risks. It helps organizations build better privacy foundations by bringing privacy risk into parity with their broader enterprise risk portfolio." `
    -KeyPoints "• Core Functions: Identify-P, Govern-P, Control-P, Communicate-P, Protect-P
• Privacy Risk Assessment
• Data Processing Ecosystem Risk Management
• Privacy Requirements and System Design
• Data Processing Management
• Disassociated Processing
• Data Mapping
• De-identification
• Consent Management
• Data Quality
• Data Management
• Transparency
• Individual Data Rights
• Privacy Notice"

# 30. ISO/IEC 27017
Create-PDF -FileName "ISO/IEC 27017" `
    -Title "ISO/IEC 27017" `
    -Description "ISO/IEC 27017 is an international standard that provides guidelines for information security controls applicable to the provision and use of cloud services. It builds upon the existing ISO/IEC 27002 standard." `
    -KeyPoints "• Shared Roles and Responsibilities
• Removal/Return of Assets
• Protection and Separation of Virtual Environments
• Virtual Machine Hardening
• Administrator's Operational Security
• Cloud Service Customer Data Monitoring
• Virtual Network Environment Security
• Data Security and Information Lifecycle Management
• Cloud Computing Compliance
• Cloud Service Agreement
• Cloud Service Customer Audit
• Cloud Service Provider Audit and Risk Assessment
• Supply Chain Management
• Virtualization Security"

Write-Host "Created sixth batch of PDFs (26-30)"
