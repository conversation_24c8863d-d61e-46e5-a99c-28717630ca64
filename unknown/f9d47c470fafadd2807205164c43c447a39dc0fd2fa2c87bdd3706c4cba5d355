"""
Utility script to generate dummy PDF documents for all the documents in the mappings.
"""
import os
import sys
from pathlib import Path
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer

# Add the parent directory to the path so we can import the model
sys.path.append(str(Path(__file__).parent.parent))
from model.document_mappings import get_all_documents
from core.config import configs

def create_dummy_pdf(doc_name: str, output_path: str):
    """
    Create a dummy PDF document with the given name.
    
    Args:
        doc_name: The name of the document
        output_path: The path to save the PDF to
    """
    doc = SimpleDocTemplate(
        output_path,
        pagesize=letter,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=72
    )
    
    styles = getSampleStyleSheet()
    story = []
    
    # Add title
    title = Paragraph(f"<b>{doc_name}</b>", styles["Title"])
    story.append(title)
    story.append(Spacer(1, 12))
    
    # Add introduction
    intro = Paragraph(
        f"This is a dummy document for {doc_name}. "
        "It contains sample content for demonstration purposes only.",
        styles["Normal"]
    )
    story.append(intro)
    story.append(Spacer(1, 12))
    
    # Add some sections with dummy content
    for i in range(1, 6):
        section_title = Paragraph(f"<b>Section {i}: Sample Content</b>", styles["Heading2"])
        story.append(section_title)
        story.append(Spacer(1, 6))
        
        content = Paragraph(
            f"This is sample content for section {i}. "
            "It demonstrates how the document would look with real content. "
            "In a real document, this would contain actual requirements, guidelines, "
            "or regulations related to cybersecurity and compliance.",
            styles["Normal"]
        )
        story.append(content)
        story.append(Spacer(1, 12))
    
    # Add a sample control
    control_title = Paragraph("<b>Sample Control: Access Control</b>", styles["Heading2"])
    story.append(control_title)
    story.append(Spacer(1, 6))
    
    control_content = Paragraph(
        "Access to systems and applications shall be controlled. "
        "Access to information and application system functions shall be restricted "
        "in accordance with the access control policy. "
        "Users shall only be provided with access to the network and network services "
        "that they have been specifically authorized to use.",
        styles["Normal"]
    )
    story.append(control_content)
    
    # Build the PDF
    doc.build(story)

def generate_all_dummy_documents():
    """
    Generate dummy PDF documents for all documents in the mappings.
    """
    # Get all document names from the mappings
    all_docs = get_all_documents()
    print(f"Found {len(all_docs)} unique documents in the mappings")
    
    # Create the policy document directory if it doesn't exist
    policy_doc_path = os.path.join(
        configs.curr_dir, 
        configs.permanent_storage, 
        configs.POLICY_FILE_PATH
    )
    os.makedirs(policy_doc_path, exist_ok=True)
    
    # Create a dummy PDF for each document
    for doc_name in all_docs:
        # Clean the document name to use as a filename
        clean_name = doc_name.replace("/", "-").replace(" ", "_").replace("&", "and")
        pdf_path = os.path.join(policy_doc_path, f"{clean_name}.pdf")
        
        # Skip if the file already exists
        if os.path.exists(pdf_path):
            print(f"Skipping existing document: {pdf_path}")
            continue
        
        print(f"Creating dummy PDF for: {doc_name}")
        create_dummy_pdf(doc_name, pdf_path)
    
    print(f"Generated dummy PDFs in: {policy_doc_path}")

if __name__ == "__main__":
    generate_all_dummy_documents()
