# Function to create a better formatted PDF
function Create-Better-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$Overview,
        [string]$KeyComponents,
        [string]$Implementation,
        [string]$Benefits
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    # Create a more complex PDF with better formatting
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R /F3 6 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 7 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Oblique
>>
endobj
7 0 obj
<< /Length 4000 >>
stream
BT
/F1 28 Tf
50 750 Td
($Title) Tj

/F2 12 Tf
0 -30 Td
(Document Version: 1.0) Tj
0 -15 Td
(Last Updated: April 2025) Tj

/F1 18 Tf
-10 -40 Td
(Description) Tj
/F2 12 Tf
10 -20 Td
($Description) Tj

/F1 18 Tf
-10 -30 Td
(Overview) Tj
/F2 12 Tf
10 -20 Td
($Overview) Tj

/F1 18 Tf
-10 -30 Td
(Key Components) Tj
/F2 12 Tf
10 -20 Td
($KeyComponents) Tj

/F1 18 Tf
-10 -30 Td
(Implementation Guidelines) Tj
/F2 12 Tf
10 -20 Td
($Implementation) Tj

/F1 18 Tf
-10 -30 Td
(Benefits) Tj
/F2 12 Tf
10 -20 Td
($Benefits) Tj

/F3 10 Tf
-20 -40 Td
(This document provides an overview of $Title. For complete and authoritative information,) Tj
0 -15 Td
(please refer to the official documentation and standards publications.) Tj
ET
endstream
endobj
xref
0 8
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
0000000444 00000 n
trailer
<< /Size 8
/Root 1 0 R
>>
startxref
4496
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created improved PDF: $safeFileName.pdf"
}

# 4. CIS Benchmarks
Create-Better-PDF -FileName "CIS Benchmarks" `
    -Title "CIS Benchmarks" `
    -Description "CIS Benchmarks are consensus-developed secure configuration guidelines for hardening systems and applications. They provide prescriptive guidance for configuring technology systems to protect against today's evolving cyber threats." `
    -Overview "The Center for Internet Security (CIS) Benchmarks are developed through a consensus review process involving cybersecurity professionals and subject matter experts around the world. The benchmarks are vendor-agnostic, globally recognized best practices for securing IT systems and data against attacks. CIS Benchmarks are available for more than 100 technologies, spanning operating systems, cloud providers, mobile devices, desktop software, server software, and network devices." `
    -KeyComponents "• Operating System Benchmarks:
  - Microsoft Windows (Desktop and Server)
  - Linux distributions (RHEL, Ubuntu, SUSE, etc.)
  - macOS
  - Unix variants

• Server Software Benchmarks:
  - Microsoft SQL Server
  - Oracle Database
  - PostgreSQL
  - MySQL/MariaDB
  - Microsoft Exchange Server
  - Microsoft SharePoint
  - Apache HTTP Server
  - Nginx
  - Docker

• Cloud Provider Benchmarks:
  - Amazon Web Services (AWS)
  - Microsoft Azure
  - Google Cloud Platform (GCP)
  - IBM Cloud
  - Oracle Cloud Infrastructure
  - Alibaba Cloud

• Network Device Benchmarks:
  - Cisco IOS
  - Cisco Firepower
  - Palo Alto Networks PAN-OS
  - Juniper Networks
  - F5 Networks

• Desktop Software Benchmarks:
  - Microsoft Office
  - Google Chrome
  - Mozilla Firefox
  - Microsoft Edge

• Mobile Device Benchmarks:
  - Apple iOS
  - Google Android" `
    -Implementation "Implementing CIS Benchmarks typically involves the following steps:

1. Select the appropriate benchmark for your technology
2. Review the recommended settings and understand their impact
3. Test the settings in a non-production environment
4. Create a baseline of your current configuration
5. Implement the recommended settings, prioritizing based on:
   - Critical security settings
   - Operational impact
   - Compliance requirements
6. Document any deviations from the benchmark recommendations and the justification
7. Verify the implementation using CIS-CAT Pro or other compliance checking tools
8. Maintain the secure configuration through change management processes
9. Regularly review and update configurations as new benchmark versions are released" `
    -Benefits "• Consensus-developed security guidance from a global community of experts
• Protection against the most pervasive and dangerous threats
• Alignment with major compliance frameworks (NIST, PCI DSS, HIPAA, etc.)
• Reduction in security vulnerabilities and attack surface
• Clear, step-by-step guidance for implementation
• Regular updates to address emerging threats and technologies
• Vendor-neutral recommendations
• Different profile levels to balance security and usability
• Automated assessment tools available (CIS-CAT Pro)
• Free access to benchmarks for non-commercial use
• Improved security posture and reduced risk of successful attacks"

# 5. ISO 22301
Create-Better-PDF -FileName "ISO 22301" `
    -Title "ISO 22301" `
    -Description "ISO 22301 is an international standard for business continuity management systems (BCMS). It specifies requirements to plan, establish, implement, operate, monitor, review, maintain and continually improve a documented management system to protect against, reduce the likelihood of, prepare for, respond to, and recover from disruptive incidents." `
    -Overview "ISO 22301 was developed by the International Organization for Standardization (ISO) and was first published in 2012, with the latest version released in 2019. The standard provides a framework for organizations to identify potential threats and their impacts, develop strategies to minimize disruption, and ensure effective response and recovery. It is applicable to all organizations regardless of size, industry, or sector, and can be integrated with other management systems such as ISO 27001 for information security." `
    -KeyComponents "• Context of the Organization:
  - Understanding the organization and its context
  - Understanding the needs and expectations of interested parties
  - Determining the scope of the BCMS
  - Business continuity management system

• Leadership:
  - Leadership and commitment
  - Policy
  - Roles, responsibilities, and authorities

• Planning:
  - Actions to address risks and opportunities
  - Business continuity objectives and planning to achieve them

• Support:
  - Resources
  - Competence
  - Awareness
  - Communication
  - Documented information

• Operation:
  - Operational planning and control
  - Business impact analysis and risk assessment
  - Business continuity strategy
  - Business continuity procedures
  - Exercising and testing

• Performance Evaluation:
  - Monitoring, measurement, analysis and evaluation
  - Internal audit
  - Management review

• Improvement:
  - Nonconformity and corrective action
  - Continual improvement" `
    -Implementation "Implementing ISO 22301 typically involves the following steps:

1. Obtain management commitment and establish a project team
2. Define the scope of the BCMS
3. Develop a business continuity policy
4. Conduct a business impact analysis (BIA) to identify critical functions and processes
5. Perform risk assessment to identify potential threats and vulnerabilities
6. Develop business continuity strategies for critical functions
7. Establish business continuity plans and procedures
8. Implement the plans through training and awareness
9. Conduct exercises and tests to validate the effectiveness of plans
10. Review and improve the BCMS through internal audits and management reviews
11. Consider certification by an accredited certification body" `
    -Benefits "• Enhanced organizational resilience
• Improved ability to maintain critical operations during disruptions
• Reduced downtime and associated costs
• Protection of assets, revenue, and reputation
• Competitive advantage through demonstrated commitment to business continuity
• Compliance with legal, regulatory, and customer requirements
• Structured approach to risk management
• Improved stakeholder confidence
• Better understanding of the organization and its dependencies
• Enhanced decision-making during crisis situations
• Reduced insurance premiums in some cases
• Systematic approach to recovery from disruptive incidents"

# 6. SOC 2 (AICPA)
Create-Better-PDF -FileName "SOC 2 (AICPA)" `
    -Title "SOC 2 (AICPA)" `
    -Description "SOC 2 (Service Organization Control 2) is a framework developed by the American Institute of Certified Public Accountants (AICPA) that defines criteria for managing customer data based on five 'trust service principles': security, availability, processing integrity, confidentiality, and privacy." `
    -Overview "SOC 2 is specifically designed for service providers storing customer data in the cloud. It has become increasingly important for technology and cloud computing companies, particularly those handling sensitive information. SOC 2 reports are unique to each organization, as they are based on the specific systems and processes in place. There are two types of SOC 2 reports: Type I, which describes the systems and whether their design is suitable to meet relevant trust principles; and Type II, which also assesses the operational effectiveness of those systems over a period of time (usually 6-12 months)." `
    -KeyComponents "• Trust Service Principles:
  - Security: Protection against unauthorized access (both physical and logical)
  - Availability: System availability for operation and use as committed or agreed
  - Processing Integrity: System processing is complete, accurate, timely, and authorized
  - Confidentiality: Information designated as confidential is protected as committed or agreed
  - Privacy: Personal information is collected, used, retained, disclosed, and disposed of in conformity with the commitments in the entity's privacy notice

• Common Criteria (for the Security principle):
  - Organization and Management
  - Communications
  - Risk Management
  - Monitoring of Controls
  - Logical and Physical Access Controls
  - System Operations
  - Change Management

• Additional Criteria for other principles (Availability, Processing Integrity, Confidentiality, and Privacy)" `
    -Implementation "Implementing SOC 2 compliance typically involves the following steps:

1. Determine which Trust Service Principles are relevant to your organization
2. Perform a readiness assessment to identify gaps in controls
3. Develop and implement policies and procedures to address identified gaps
4. Document control activities and gather evidence of their operation
5. Conduct internal audits to ensure controls are operating effectively
6. Engage a CPA firm to perform the SOC 2 examination
7. Address any issues identified during the examination
8. Obtain the SOC 2 report from the CPA firm
9. Share the report with customers and prospects as needed
10. Maintain controls and prepare for annual re-certification (for Type II)" `
    -Benefits "• Enhanced trust and transparency with customers and partners
• Competitive advantage in the marketplace
• Standardized approach to security and compliance
• Reduced risk of data breaches and security incidents
• Streamlined customer due diligence processes
• Improved internal controls and security practices
• Single comprehensive assessment that can satisfy multiple customers
• Alignment with other frameworks and standards (e.g., ISO 27001, NIST)
• Demonstration of commitment to security and privacy
• Potential reduction in the number of customer security assessments
• Improved organizational security awareness
• Structured approach to managing third-party risks"

Write-Host "Created second batch of improved PDFs (4-6)"
