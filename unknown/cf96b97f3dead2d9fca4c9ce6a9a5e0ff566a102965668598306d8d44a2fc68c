.backdrop {
  padding: 20px;
  border-radius: 10px;
  background: #252531;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.spinner-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
}

.btn {
  background: #ffd700;
  color: #333;
  border: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  background: #ffcc00;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
  background: #cccccc;
  color: #666666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.main-container {
  padding: 0;
  background-size: cover;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Domain Upload Styles */
.domain-accordion .mat-expansion-panel {
  background-color: #2d2d3a;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.domain-accordion .mat-expansion-panel.has-files {
  border-left: 4px solid #28a745;
}

.domain-name {
  font-weight: 500;
  font-size: 16px;
}

.file-count {
  display: flex;
  align-items: center;
  color: #aaa;
}

.file-count.text-success {
  color: #28a745;
}

.upload-zone {
  border: 2px dashed #666;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #333340;
}

.upload-zone:hover {
  border-color: #ffd700;
  background-color: #3a3a4a;
}

.upload-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
  color: #ffd700;
}

.file-input {
  display: none;
}

.accepted-formats .badge {
  padding: 6px 12px;
  font-size: 12px;
  background-color: #444;
  color: #fff;
}

.no-files-message {
  background-color: #333340;
  border-radius: 8px;
}

.no-files-message mat-icon {
  font-size: 36px;
  height: 36px;
  width: 36px;
  margin-bottom: 8px;
}

.alert-info {
  background-color: #17a2b840;
  color: #d1ecf1;
  border: 1px solid #17a2b880;
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.domain-progress .progress {
  height: 10px;
  background-color: #333340;
  border-radius: 5px;
  overflow: hidden;
}

.table-dark {
  background-color: #333340;
  border-radius: 8px;
  overflow: hidden;
}

.table-dark thead th {
  background-color: #252531;
  border-bottom: 2px solid #444;
  font-weight: 500;
}

.table-hover tbody tr:hover {
  background-color: #3a3a4a;
}

.header h4 {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  color: #ffd700;
}

.header h4 mat-icon {
  margin-right: 8px;
}

/* Questions panel styles */
.questions-panel {
  background-color: #2a2a36;
  margin-bottom: 15px;
}

.questions-list {
  max-height: 300px;
  overflow-y: auto;
}

.list-group-item {
  background-color: #333340;
  color: #eee;
  border: 1px solid #444;
  padding: 10px 15px;
  margin-bottom: 5px;
}

.btn-outline-secondary {
  color: #aaa;
  border-color: #666;
}

.btn-outline-secondary:hover {
  background-color: #444;
  color: #fff;
}

.domain-questions {
  border-left: 3px solid #ffd700;
  padding-left: 10px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.align-middle {
  vertical-align: middle;
}

.text-success {
  color: #28a745 !important;
}

.text-muted {
  color: #aaa !important;
}