# imports from packages
from fastapi import APIRouter, UploadFile, Form, Depends
from typing import List, Optional, Dict
import json
import os
import pandas as pd

# custom imports
from app.util.evidence_main import save_evidence_files
from app.util.evidence_utils import write_to_excel, create_save_vectordb
from app.model.evidence_analyzer import evidence_download
from app.core.config import configs
from app.model.document_mappings import (
    get_documents_for_region_selection,
    get_documents_for_domain_selection,
    get_combined_documents
)

router = APIRouter(
    tags=["evidence_analyzer"],
)


@router.post("/analyze_evidences")
async def analyze_evidences(
    files: list[UploadFile],
    evidence_domains: Optional[List[str]] = Form(None),  # New: domain for each file, same order as files
    benchmarks: Optional[str] = Form(None),
    question_method: Optional[str] = Form(None),
    hardcoded_questions: Optional[str] = Form(None),
    region_selection: Optional[str] = Form(None),
    domain_selection: Optional[str] = Form(None),
    domain_evidence_mapping: Optional[str] = Form(None)
):
    """
    Upload 1 Control Document and multiple Evidences to analyze

    - files: List of files to upload (Excel questionnaire and evidence files)
    - benchmarks: JSON string of selected benchmark names (legacy)
    - question_method: Method used to select questions ('hardcoded', 'generate', or 'upload')
    - hardcoded_questions: JSON string of hardcoded questions if question_method is 'hardcoded'
    - region_selection: JSON string with region, industry, and sector selections
    - domain_selection: JSON string with domain and CSP selections
    - domain_evidence_mapping: JSON string mapping domains to evidence file names
    """
    try:
        # Parse the benchmarks JSON string if provided (legacy support)
        selected_benchmarks = None
        if benchmarks:
            try:
                selected_benchmarks = json.loads(benchmarks)
                print(f"Selected benchmarks (legacy): {selected_benchmarks}")
            except json.JSONDecodeError as e:
                print(f"Error parsing benchmarks JSON: {e}")
                selected_benchmarks = None

        # Parse the region selection JSON string if provided
        region_data = None
        if region_selection:
            try:
                region_data = json.loads(region_selection)
                print(f"Region selection: {region_data}")
            except json.JSONDecodeError as e:
                print(f"Error parsing region selection JSON: {e}")
                region_data = None

        # Parse the domain selection JSON string if provided
        domain_data = None
        if domain_selection:
            try:
                domain_data = json.loads(domain_selection)
                print(f"Domain selection: {domain_data}")
            except json.JSONDecodeError as e:
                print(f"Error parsing domain selection JSON: {e}")
                domain_data = None

        # Build domain_evidence_data from evidence_domains if provided
        domain_evidence_data = None
        if evidence_domains:
            # evidence_domains is a list of domain names, same order as files
            domain_evidence_data = {}
            for file, domain in zip(files, evidence_domains):
                if domain not in domain_evidence_data:
                    domain_evidence_data[domain] = []
                domain_evidence_data[domain].append(file.filename)
            print(f"Built domain_evidence_mapping from evidence_domains: {domain_evidence_data}")
        elif domain_evidence_mapping:
            try:
                domain_evidence_data = json.loads(domain_evidence_mapping)
                print(f"Domain evidence mapping: {domain_evidence_data}")
            except json.JSONDecodeError as e:
                print(f"Error parsing domain evidence mapping JSON: {e}")
                domain_evidence_data = None

        # Get the combined document list based on selections
        document_list = []
        if region_data or domain_data:
            # Extract region selection parameters
            region = region_data.get('region') if region_data else None
            industry = region_data.get('industry') if region_data else None
            sector = region_data.get('sector') if region_data else None

            # Extract domain selection parameters
            domain = domain_data.get('domain') if domain_data else None
            csp = domain_data.get('csp') if domain_data else None

            # Get the documents based on the selections
            if region and industry and sector and domain:
                # Both region and domain selections
                documents = get_combined_documents(region, industry, sector, domain, csp)
            elif region and industry and sector:
                # Only region selection
                documents = get_documents_for_region_selection(region, industry, sector)
            elif domain:
                # Only domain selection
                documents = get_documents_for_domain_selection(domain, csp)
            else:
                documents = {
                    "Standard": [],
                    "Guidelines": [],
                    "Framework": [],
                    "Regulation": []
                }

            # Flatten the document list
            for category, docs in documents.items():
                document_list.extend(docs)

            print(f"Selected documents based on region/domain: {document_list}")

            # Use the document list as the selected benchmarks
            selected_benchmarks = document_list

        # Parse the hardcoded questions JSON string if provided
        hardcoded_questions_data = None
        if hardcoded_questions:
            try:
                hardcoded_questions_data = json.loads(hardcoded_questions)
                print(f"Hardcoded questions count: {len(hardcoded_questions_data)}")
            except json.JSONDecodeError as e:
                print(f"Error parsing hardcoded questions JSON: {e}")
                hardcoded_questions_data = None

        # Print debug information
        print(f"Files count: {len(files)}")
        print(f"Question method: {question_method}")

        return save_evidence_files(
            files=files,
            selected_benchmarks=selected_benchmarks,
            question_method=question_method,
            hardcoded_questions=hardcoded_questions_data,
            domain_evidence_mapping=domain_evidence_data
        )
    except Exception as e:
        print(f"Error in analyze_evidences: {str(e)}")
        import traceback
        traceback.print_exc()
        # Return a more informative error response
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")


@router.post("/analyze_evidences_batch")
async def analyze_evidences_batch(
    files: list[UploadFile],
    start_row: int = 0,
    end_row: int = -1
):
    """
    Process a specific range of rows from the Excel file.

    - start_row: The index of the first row to process (0-based)
    - end_row: The index of the last row to process + 1 (-1 means process until the end)
    """
    return save_evidence_files(files=files, start_row=start_row, end_row=end_row)


@router.post("/download_data", status_code=200)
async def download_data(data: list[evidence_download], filename: str = "output.xlsx"):
    """
    Download the analyzed data
    """
    list = [i.model_dump() for i in data]

    return write_to_excel(list, filename)


@router.get("/create_vectordb", status_code=200)
async def create_vectordb():
    """
    Create a Vector DB
    """
    print("started to create vector db")

    return create_save_vectordb()


@router.get("/region_selection")
async def get_region_selection():
    """
    Get the available options for region-based selection.

    Returns:
        Dictionary with regions, industries, and sectors
    """
    from app.model.document_mappings import REGION_MAPPING

    regions = list(REGION_MAPPING.keys())

    # Create a nested structure of industries and sectors
    industries_by_region = {}
    sectors_by_industry = {}

    for region, industries in REGION_MAPPING.items():
        industries_by_region[region] = list(industries.keys())

        for industry, sectors in industries.items():
            key = f"{region}_{industry}"
            sectors_by_industry[key] = list(sectors.keys())

    return {
        "regions": regions,
        "industries_by_region": industries_by_region,
        "sectors_by_industry": sectors_by_industry
    }


@router.get("/domain_selection")
async def get_domain_selection():
    """
    Get the available options for domain-based selection.

    Returns:
        Dictionary with domains and CSPs
    """
    from app.model.document_mappings import DOMAIN_MAPPING

    domains = list(DOMAIN_MAPPING.keys())

    # Create a structure of CSPs by domain
    csps_by_domain = {}

    for domain, csps in DOMAIN_MAPPING.items():
        csps_by_domain[domain] = list(csps.keys())

    return {
        "domains": domains,
        "csps_by_domain": csps_by_domain
    }


@router.post("/extract_domains_from_excel")
async def extract_domains_from_excel(file: UploadFile):
    """
    Extract domains from an uploaded Excel file.

    Args:
        file: The uploaded Excel file

    Returns:
        Dictionary with extracted domains
    """
    try:
        # Save the file temporarily
        temp_dir = os.path.join(configs.curr_dir, configs.TEMP_CONSTANT, "temp_excel_extract")
        os.makedirs(temp_dir, exist_ok=True)

        temp_file_path = os.path.join(temp_dir, file.filename)
        with open(temp_file_path, "wb") as buffer:
            buffer.write(file.file.read())

        # Read the Excel file
        df = pd.read_excel(temp_file_path)

        print(f"extract_domains_from_excel: Columns found in Excel: {list(df.columns)}")

        # Define possible domain column names
        possible_domain_columns = [
            'Domain', 'Security Domain', 'Category', 'Section',
            'Area', 'Control Domain', 'Control Category', 'Topic'
        ]

        # Find the domain column
        domain_column = None
        for col in possible_domain_columns:
            if col in df.columns:
                domain_column = col
                break

        # If we couldn't find the column, try case-insensitive matching
        if not domain_column:
            for col in df.columns:
                if any(possible.lower() in col.lower() for possible in possible_domain_columns):
                    domain_column = col
                    break

        # If we still couldn't find a domain column, use the first string column
        if not domain_column:
            for col in df.columns:
                if df[col].dtype == 'object':
                    domain_column = col
                    break

        print(f"extract_domains_from_excel: Detected domain column: {domain_column}")

        # Extract unique domains
        domains = []
        if domain_column:
            # Get all non-null domain values, trim whitespace, and deduplicate
            raw_domains = df[domain_column].dropna().astype(str)
            print(f"extract_domains_from_excel: Raw domain values: {raw_domains.tolist()}")
            trimmed_domains = [d.strip() for d in raw_domains if d.strip()]
            print(f"extract_domains_from_excel: Trimmed domain values: {trimmed_domains}")
            domains = sorted(list(set(trimmed_domains)))
            print(f"extract_domains_from_excel: Final unique domains: {domains}")

            # If no domains found, add a default one
            if not domains:
                domains = ['General']
        else:
            domains = ['General']

        # Clean up
        os.remove(temp_file_path)

        print(f"extract_domains_from_excel: Returning domains: {domains}")

        return {
            "domains": domains,
            "csps_by_domain": {domain: [] for domain in domains}  # No CSPs for custom domains
        }

    except Exception as e:
        print(f"Error extracting domains from Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "domains": ["General"],
            "csps_by_domain": {"General": []}
        }


@router.get("/documents_for_selection")
async def get_documents_for_selection(
    region: Optional[str] = None,
    industry: Optional[str] = None,
    sector: Optional[str] = None,
    domain: Optional[str] = None,
    csp: Optional[str] = None
):
    """
    Get the list of documents for the given selection parameters.

    Args:
        region: The selected region
        industry: The selected industry
        sector: The selected sector
        domain: The selected domain
        csp: The selected CSP (only applicable for Cloud Security/SaaS)

    Returns:
        Dictionary with document categories and their document lists
    """
    # If both region and domain selections are provided, combine them
    if region and industry and sector and domain:
        return get_combined_documents(region, industry, sector, domain, csp)

    # If only region selection is provided
    elif region and industry and sector:
        return get_documents_for_region_selection(region, industry, sector)

    # If only domain selection is provided
    elif domain:
        return get_documents_for_domain_selection(domain, csp)

    # If no valid selection is provided
    else:
        return {
            "Standard": [],
            "Guidelines": [],
            "Framework": [],
            "Regulation": []
        }
