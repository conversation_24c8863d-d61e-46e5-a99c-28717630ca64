{"version": 3, "sources": ["../../../../../node_modules/@angular/material/fesm2022/card.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, Input, Directive, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"], [\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"], [\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"], [\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"], [\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]], \"*\"];\nconst _c2 = [\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\", \"*\"];\nconst _c3 = [[[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]], [[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], \"*\"];\nconst _c4 = [\"[mat-card-avatar], [matCardAvatar]\", \"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"*\"];\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n  constructor(config) {\n    this.appearance = config?.appearance || 'raised';\n  }\n  static {\n    this.ɵfac = function MatCard_Factory(t) {\n      return new (t || MatCard)(i0.ɵɵdirectiveInject(MAT_CARD_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCard,\n      selectors: [[\"mat-card\"]],\n      hostAttrs: [1, \"mat-mdc-card\", \"mdc-card\"],\n      hostVars: 4,\n      hostBindings: function MatCard_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-card-outlined\", ctx.appearance === \"outlined\")(\"mdc-card--outlined\", ctx.appearance === \"outlined\");\n        }\n      },\n      inputs: {\n        appearance: \"appearance\"\n      },\n      exportAs: [\"matCard\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatCard_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCard, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card',\n      host: {\n        'class': 'mat-mdc-card mdc-card',\n        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n        '[class.mdc-card--outlined]': 'appearance === \"outlined\"'\n      },\n      exportAs: 'matCard',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      template: \"<ng-content></ng-content>\\n\",\n      styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_CARD_CONFIG]\n    }, {\n      type: Optional\n    }]\n  }], {\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n  static {\n    this.ɵfac = function MatCardTitle_Factory(t) {\n      return new (t || MatCardTitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardTitle,\n      selectors: [[\"mat-card-title\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"matCardTitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-title\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n      host: {\n        'class': 'mat-mdc-card-title'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n  static {\n    this.ɵfac = function MatCardTitleGroup_Factory(t) {\n      return new (t || MatCardTitleGroup)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCardTitleGroup,\n      selectors: [[\"mat-card-title-group\"]],\n      hostAttrs: [1, \"mat-mdc-card-title-group\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 4,\n      vars: 0,\n      template: function MatCardTitleGroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitleGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-title-group',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-title-group'\n      },\n      standalone: true,\n      template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n  static {\n    this.ɵfac = function MatCardContent_Factory(t) {\n      return new (t || MatCardContent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardContent,\n      selectors: [[\"mat-card-content\"]],\n      hostAttrs: [1, \"mat-mdc-card-content\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardContent, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-content',\n      host: {\n        'class': 'mat-mdc-card-content'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n  static {\n    this.ɵfac = function MatCardSubtitle_Factory(t) {\n      return new (t || MatCardSubtitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardSubtitle,\n      selectors: [[\"mat-card-subtitle\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-subtitle\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSubtitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n      host: {\n        'class': 'mat-mdc-card-subtitle'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n  constructor() {\n    // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n    // as to not conflict with the native `align` attribute.\n    /** Position of the actions inside the card. */\n    this.align = 'start';\n  }\n  static {\n    this.ɵfac = function MatCardActions_Factory(t) {\n      return new (t || MatCardActions)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardActions,\n      selectors: [[\"mat-card-actions\"]],\n      hostAttrs: [1, \"mat-mdc-card-actions\", \"mdc-card__actions\"],\n      hostVars: 2,\n      hostBindings: function MatCardActions_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-card-actions-align-end\", ctx.align === \"end\");\n        }\n      },\n      inputs: {\n        align: \"align\"\n      },\n      exportAs: [\"matCardActions\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardActions, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-actions',\n      exportAs: 'matCardActions',\n      host: {\n        'class': 'mat-mdc-card-actions mdc-card__actions',\n        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"'\n      },\n      standalone: true\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n  static {\n    this.ɵfac = function MatCardHeader_Factory(t) {\n      return new (t || MatCardHeader)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCardHeader,\n      selectors: [[\"mat-card-header\"]],\n      hostAttrs: [1, \"mat-mdc-card-header\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"mat-mdc-card-header-text\"]],\n      template: function MatCardHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-header'\n      },\n      standalone: true,\n      template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n  static {\n    this.ɵfac = function MatCardFooter_Factory(t) {\n      return new (t || MatCardFooter)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardFooter,\n      selectors: [[\"mat-card-footer\"]],\n      hostAttrs: [1, \"mat-mdc-card-footer\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardFooter, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-footer',\n      host: {\n        'class': 'mat-mdc-card-footer'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n  static {\n    this.ɵfac = function MatCardImage_Factory(t) {\n      return new (t || MatCardImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardImage,\n      selectors: [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-image], [matCardImage]',\n      host: {\n        'class': 'mat-mdc-card-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n  static {\n    this.ɵfac = function MatCardSmImage_Factory(t) {\n      return new (t || MatCardSmImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardSmImage,\n      selectors: [[\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-sm-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSmImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-sm-image], [matCardImageSmall]',\n      host: {\n        'class': 'mat-mdc-card-sm-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n  static {\n    this.ɵfac = function MatCardMdImage_Factory(t) {\n      return new (t || MatCardMdImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardMdImage,\n      selectors: [[\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-md-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardMdImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-md-image], [matCardImageMedium]',\n      host: {\n        'class': 'mat-mdc-card-md-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n  static {\n    this.ɵfac = function MatCardLgImage_Factory(t) {\n      return new (t || MatCardLgImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardLgImage,\n      selectors: [[\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-lg-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardLgImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-lg-image], [matCardImageLarge]',\n      host: {\n        'class': 'mat-mdc-card-lg-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n  static {\n    this.ɵfac = function MatCardXlImage_Factory(t) {\n      return new (t || MatCardXlImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardXlImage,\n      selectors: [[\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-xl-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardXlImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-xl-image], [matCardImageXLarge]',\n      host: {\n        'class': 'mat-mdc-card-xl-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n  static {\n    this.ɵfac = function MatCardAvatar_Factory(t) {\n      return new (t || MatCardAvatar)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardAvatar,\n      selectors: [[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-avatar\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardAvatar, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-avatar], [matCardAvatar]',\n      host: {\n        'class': 'mat-mdc-card-avatar'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\nconst CARD_DIRECTIVES = [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage];\nclass MatCardModule {\n  static {\n    this.ɵfac = function MatCardModule_Factory(t) {\n      return new (t || MatCardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatCardModule,\n      imports: [MatCommonModule, CommonModule, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage],\n      exports: [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage, MatCommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule, ...CARD_DIRECTIVES],\n      exports: [CARD_DIRECTIVES, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,CAAC,CAAC,gBAAgB,GAAG,CAAC,mBAAmB,GAAG,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,sBAAsB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,sBAAsB,EAAE,CAAC,GAAG,GAAG;AACje,IAAM,MAAM,CAAC,6HAA6H,kSAAkS,GAAG;AAC/a,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,gBAAgB,GAAG,CAAC,mBAAmB,GAAG,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC,GAAG,GAAG;AACzO,IAAM,MAAM,CAAC,sCAAsC,6HAA6H,GAAG;AACnL,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAO5D,IAAM,WAAN,MAAM,SAAQ;AAAA,EACZ,YAAY,QAAQ;AAClB,SAAK,aAAa,QAAQ,cAAc;AAAA,EAC1C;AAqCF;AAnCI,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,SAAO,KAAK,KAAK,UAAY,kBAAkB,iBAAiB,CAAC,CAAC;AACpE;AAGA,SAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,EACxB,WAAW,CAAC,GAAG,gBAAgB,UAAU;AAAA,EACzC,UAAU;AAAA,EACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,yBAAyB,IAAI,eAAe,UAAU,EAAE,sBAAsB,IAAI,eAAe,UAAU;AAAA,IAC5H;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,YAAY;AAAA,EACd;AAAA,EACA,UAAU,CAAC,SAAS;AAAA,EACpB,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB;AAAA,EACjC,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,aAAa,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,QAAQ,CAAC,wpLAA4pL;AAAA,EACrqL,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAtCL,IAAM,UAAN;AAAA,CAyCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,iCAAiC;AAAA,QACjC,8BAA8B;AAAA,MAChC;AAAA,MACA,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ,CAAC,wpLAA4pL;AAAA,IACvqL,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AASH,IAAM,gBAAN,MAAM,cAAa;AAcnB;AAZI,cAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,SAAO,KAAK,KAAK,eAAc;AACjC;AAGA,cAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EACpF,WAAW,CAAC,GAAG,oBAAoB;AAAA,EACnC,YAAY;AACd,CAAC;AAZL,IAAM,eAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,qBAAN,MAAM,mBAAkB;AA8BxB;AA5BI,mBAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,SAAO,KAAK,KAAK,oBAAmB;AACtC;AAGA,mBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,EACpC,WAAW,CAAC,GAAG,0BAA0B;AAAA,EACzC,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB;AAAA,EACjC,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB,GAAG;AACtB,MAAG,eAAe,GAAG,KAAK;AAC1B,MAAG,aAAa,CAAC;AACjB,MAAG,aAAa;AAChB,MAAG,aAAa,GAAG,CAAC;AACpB,MAAG,aAAa,GAAG,CAAC;AAAA,IACtB;AAAA,EACF;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AA5BL,IAAM,oBAAN;AAAA,CA+BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,kBAAN,MAAM,gBAAe;AAcrB;AAZI,gBAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,SAAO,KAAK,KAAK,iBAAgB;AACnC;AAGA,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,EAChC,WAAW,CAAC,GAAG,sBAAsB;AAAA,EACrC,YAAY;AACd,CAAC;AAZL,IAAM,iBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,mBAAN,MAAM,iBAAgB;AActB;AAZI,iBAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,SAAO,KAAK,KAAK,kBAAiB;AACpC;AAGA,iBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,mBAAmB,GAAG,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EAC7F,WAAW,CAAC,GAAG,uBAAuB;AAAA,EACtC,YAAY;AACd,CAAC;AAZL,IAAM,kBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,kBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc;AAIZ,SAAK,QAAQ;AAAA,EACf;AAwBF;AAtBI,gBAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,SAAO,KAAK,KAAK,iBAAgB;AACnC;AAGA,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,EAChC,WAAW,CAAC,GAAG,wBAAwB,mBAAmB;AAAA,EAC1D,UAAU;AAAA,EACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,kCAAkC,IAAI,UAAU,KAAK;AAAA,IACtE;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU,CAAC,gBAAgB;AAAA,EAC3B,YAAY;AACd,CAAC;AA5BL,IAAM,iBAAN;AAAA,CA+BC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,0CAA0C;AAAA,MAC5C;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AASH,IAAM,iBAAN,MAAM,eAAc;AA+BpB;AA7BI,eAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,SAAO,KAAK,KAAK,gBAAe;AAClC;AAGA,eAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,EAC/B,WAAW,CAAC,GAAG,qBAAqB;AAAA,EACpC,YAAY;AAAA,EACZ,UAAU,CAAI,mBAAmB;AAAA,EACjC,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,GAAG,0BAA0B,CAAC;AAAA,EACxC,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB,GAAG;AACtB,MAAG,aAAa,CAAC;AACjB,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,aAAa,GAAG,CAAC;AACpB,MAAG,aAAa;AAChB,MAAG,aAAa,GAAG,CAAC;AAAA,IACtB;AAAA,EACF;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AA7BL,IAAM,gBAAN;AAAA,CAgCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,iBAAN,MAAM,eAAc;AAcpB;AAZI,eAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,SAAO,KAAK,KAAK,gBAAe;AAClC;AAGA,eAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,EAC/B,WAAW,CAAC,GAAG,qBAAqB;AAAA,EACpC,YAAY;AACd,CAAC;AAZL,IAAM,gBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAYH,IAAM,gBAAN,MAAM,cAAa;AAcnB;AAZI,cAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,SAAO,KAAK,KAAK,eAAc;AACjC;AAGA,cAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EAChE,WAAW,CAAC,GAAG,sBAAsB,iBAAiB;AAAA,EACtD,YAAY;AACd,CAAC;AAZL,IAAM,eAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,gBAAe;AAcrB;AAZI,gBAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,SAAO,KAAK,KAAK,iBAAgB;AACnC;AAGA,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,EACxE,WAAW,CAAC,GAAG,yBAAyB,iBAAiB;AAAA,EACzD,YAAY;AACd,CAAC;AAZL,IAAM,iBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,gBAAe;AAcrB;AAZI,gBAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,SAAO,KAAK,KAAK,iBAAgB;AACnC;AAGA,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,EACzE,WAAW,CAAC,GAAG,yBAAyB,iBAAiB;AAAA,EACzD,YAAY;AACd,CAAC;AAZL,IAAM,iBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,gBAAe;AAcrB;AAZI,gBAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,SAAO,KAAK,KAAK,iBAAgB;AACnC;AAGA,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,EACxE,WAAW,CAAC,GAAG,yBAAyB,iBAAiB;AAAA,EACzD,YAAY;AACd,CAAC;AAZL,IAAM,iBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,gBAAe;AAcrB;AAZI,gBAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,SAAO,KAAK,KAAK,iBAAgB;AACnC;AAGA,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,EACzE,WAAW,CAAC,GAAG,yBAAyB,iBAAiB;AAAA,EACzD,YAAY;AACd,CAAC;AAZL,IAAM,iBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,iBAAN,MAAM,eAAc;AAcpB;AAZI,eAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,SAAO,KAAK,KAAK,gBAAe;AAClC;AAGA,eAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,EAClE,WAAW,CAAC,GAAG,qBAAqB;AAAA,EACpC,YAAY;AACd,CAAC;AAZL,IAAM,gBAAN;AAAA,CAeC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAkB,CAAC,SAAS,gBAAgB,eAAe,gBAAgB,eAAe,eAAe,cAAc,gBAAgB,gBAAgB,gBAAgB,iBAAiB,cAAc,mBAAmB,cAAc;AAC7O,IAAM,iBAAN,MAAM,eAAc;AAkBpB;AAhBI,eAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,SAAO,KAAK,KAAK,gBAAe;AAClC;AAGA,eAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,iBAAiB,cAAc,SAAS,gBAAgB,eAAe,gBAAgB,eAAe,eAAe,cAAc,gBAAgB,gBAAgB,gBAAgB,iBAAiB,cAAc,mBAAmB,cAAc;AAAA,EAC7P,SAAS,CAAC,SAAS,gBAAgB,eAAe,gBAAgB,eAAe,eAAe,cAAc,gBAAgB,gBAAgB,gBAAgB,iBAAiB,cAAc,mBAAmB,gBAAgB,eAAe;AACjP,CAAC;AAGD,eAAK,OAAyB,iBAAiB;AAAA,EAC7C,SAAS,CAAC,iBAAiB,cAAc,eAAe;AAC1D,CAAC;AAhBL,IAAM,gBAAN;AAAA,CAmBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,cAAc,GAAG,eAAe;AAAA,MAC3D,SAAS,CAAC,iBAAiB,eAAe;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}