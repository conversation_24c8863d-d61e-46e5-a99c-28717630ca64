import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class SidebarViewService {
  showSidebar: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  show$ = this.showSidebar.asObservable();

  constructor() {}

  toggle() {
    this.showSidebar.next(!this.showSidebar.value);
  }

  setValue(val: boolean) {
    this.showSidebar.next(val);
  }

  openSidebar() {
    this.showSidebar.next(true);
  }

  closeSidebar() {
    this.showSidebar.next(false);
  }
}
