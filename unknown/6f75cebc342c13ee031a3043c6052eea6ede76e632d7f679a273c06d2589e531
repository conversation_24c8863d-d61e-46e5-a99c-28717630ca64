<div class="headerContainer">
  <mat-toolbar class="mat-toolbar">
    <span class="d-flex align-items-center">

      <mat-icon matRipple [matRippleColor]="color" class="hamburger-icon" aria-hidden="false"
        aria-label="Example home icon" (click)="toggle()" fontIcon="menu"></mat-icon>

      <!-- <button type="button" aria-label="Toggle sidenav" (click)="toggle()">
          <mat-icon class="mat-icon-button">menu</mat-icon>-->

      <!-- </button> -->
      <img class="main-logo" src="../../../../../assets/images/EY_Logo_Beam_RGB_White_Yellow.png" style="height: 40px"
        alt="logo" routerLink="/home" routerLinkActive="active-link" />
    </span>

    <!-- This fills the remaining space of the current row -->
    <span class="fillEmptySpace">
      <span class="title" routerLinkActive="router-link-active">
        <span class="mainTitle">Cyber ASSESSURE</span>
      </span>
    </span>

    <span class="userDetailsContainer">
      <div class="userDetails container p-0">
        <div class="row"></div>
      </div>
    </span>
  </mat-toolbar>
</div>