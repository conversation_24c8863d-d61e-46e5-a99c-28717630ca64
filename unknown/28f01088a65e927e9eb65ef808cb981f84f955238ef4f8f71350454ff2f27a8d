import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DocumentSelectionRoutingModule } from './document-selection-routing.module';
import { DocumentSelectionComponent } from './components/document-selection.component';
import { RegionSelectionModule } from '../region-selection/region-selection.module';
import { DomainBasedSelectionModule } from '../domain-based-selection/domain-based-selection.module';
import { DocumentCheckboxSelectionModule } from '../document-checkbox-selection/document-checkbox-selection.module';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatStepperModule } from '@angular/material/stepper';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    DocumentSelectionRoutingModule,
    RegionSelectionModule,
    DomainBasedSelectionModule,
    DocumentCheckboxSelectionModule,
    MatButtonModule,
    MatDividerModule,
    MatStepperModule,
    DocumentSelectionComponent
  ]
})
export class DocumentSelectionModule { }
