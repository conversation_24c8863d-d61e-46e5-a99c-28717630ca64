import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-isecmapper-home',
  standalone: true,
  imports: [],
  templateUrl: './isecmapper-home.component.html',
  styleUrl: './isecmapper-home.component.css'
})
export class IsecmapperHomeComponent {

  constructor(
    private router: Router,

  ) { }

  navigateUrl(){
    // Navigate to the new document selection page instead of domain selection
    this.router.navigateByUrl('/document-selection');
  }
}
