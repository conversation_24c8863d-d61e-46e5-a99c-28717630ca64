import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DomainSelectionComponent } from './components/domain-selection.component';

const routes: Routes = [
  {
    path: '',
    component: DomainSelectionComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DomainSelectionRoutingModule {}
