# imports from packages
from uuid import uuid4
import pathlib
from tiktoken import get_encoding


def get_unique_id() -> str:
    """
    Returns a unique id in string
    """
    return str(uuid4().hex)


def create_dir(dir_path: str) -> None:
    """
    Creates the directory if not present, ignores if already present
    """
    pathlib.Path(dir_path).mkdir(parents=True, exist_ok=True)


def striplines(s: str) -> str:
    """
    Remove indentation from each line, and leading/trailing whitespace.
    """
    return "\n".join([line.strip() for line in s.splitlines()]).strip()


def track_token_usage_from_gpt(prompt_content: str) -> int:
    """
    - Returns the total number of tokens consumed in input prompt/prompt response from GPT
    """

    TOKEN_ENCODING = get_encoding("cl100k_base")
    return len(TOKEN_ENCODING.encode(prompt_content))
