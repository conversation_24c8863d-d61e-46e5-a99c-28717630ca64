"""
Test script for the file_tracker module.

This script tests the enhanced file tracking functionality with structured file references.
"""

import os
import sys
from typing import List, Dict, Any

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.util.file_tracker import extract_referenced_files, track_files_for_control, find_best_matching_file

# Mock data
mock_files = [
    "security_policy.pdf",
    "network_config.docx",
    "user_guide.pdf",
    "system_logs.txt",
    "screenshot1.jpg",
    "screenshot2.png",
    "data_backup.pdf"
]

# Test cases for extract_referenced_files
def test_extract_referenced_files():
    print("\n=== Testing extract_referenced_files ===")
    
    # Test case 1: Observation with structured format
    observation1 = """
    Based on the evidence provided, the organization has implemented proper access controls.
    The security policy document outlines the requirements for password complexity and rotation.
    
    RELEVANT_FILES: security_policy.pdf, network_config.docx
    """
    
    result1 = extract_referenced_files(observation1, mock_files)
    print(f"Test 1 (Structured format) - Expected: ['security_policy.pdf', 'network_config.docx'], Got: {result1}")
    
    # Test case 2: Observation with file mentions but no structured format
    observation2 = """
    Based on the evidence provided, the organization has implemented proper access controls.
    The security_policy.pdf document outlines the requirements for password complexity and rotation.
    Additional network configuration details can be found in network_config.docx.
    """
    
    result2 = extract_referenced_files(observation2, mock_files)
    print(f"Test 2 (File mentions) - Expected files mentioned in text, Got: {result2}")
    
    # Test case 3: Observation with structured format but file names need fuzzy matching
    observation3 = """
    Based on the evidence provided, the organization has implemented proper access controls.
    
    RELEVANT_FILES: security policy, network config
    """
    
    result3 = extract_referenced_files(observation3, mock_files)
    print(f"Test 3 (Fuzzy matching) - Expected similar to ['security_policy.pdf', 'network_config.docx'], Got: {result3}")
    
    # Test case 4: Observation with "None" in structured format
    observation4 = """
    No relevant evidence was found for this control.
    
    RELEVANT_FILES: None
    """
    
    result4 = extract_referenced_files(observation4, mock_files)
    print(f"Test 4 (None in structured format) - Expected: [], Got: {result4}")

# Test cases for track_files_for_control
def test_track_files_for_control():
    print("\n=== Testing track_files_for_control ===")
    
    # Mock file groups
    mock_file_groups = {
        "all": {
            "text_files": ["security_policy.pdf", "network_config.docx", "user_guide.pdf", "system_logs.txt"],
            "image_files": ["screenshot1.jpg", "screenshot2.png"]
        },
        "1": {
            "text_files": ["security_policy.pdf", "network_config.docx"],
            "image_files": ["screenshot1.jpg"]
        }
    }
    
    # Test case 1: Control with structured format in observation
    observation1 = """
    Based on the evidence provided, the organization has implemented proper access controls.
    
    RELEVANT_FILES: security_policy.pdf, screenshot1.jpg
    """
    
    result1 = track_files_for_control("1", observation1, mock_file_groups, "")
    print(f"Test 1 (Structured format) - Expected: ['security_policy.pdf', 'screenshot1.jpg'], Got: {result1}")
    
    # Test case 2: Control with no files mentioned - should use fallback
    observation2 = """
    Based on the evidence provided, the organization has implemented proper access controls.
    """
    
    result2 = track_files_for_control("1", observation2, mock_file_groups, "")
    print(f"Test 2 (Fallback) - Expected: limited list of files, Got: {result2}")
    
    # Test case 3: Control with too many files mentioned - should limit to 5
    observation3 = """
    Based on the evidence provided, the organization has implemented proper access controls.
    
    RELEVANT_FILES: security_policy.pdf, network_config.docx, user_guide.pdf, system_logs.txt, screenshot1.jpg, screenshot2.png, data_backup.pdf
    """
    
    result3 = track_files_for_control("all", observation3, mock_file_groups, "")
    print(f"Test 3 (Limit to 5) - Expected: 5 files max, Got: {len(result3)} files - {result3}")

# Test cases for find_best_matching_file
def test_find_best_matching_file():
    print("\n=== Testing find_best_matching_file ===")
    
    # Test case 1: Exact match
    result1 = find_best_matching_file("security_policy.pdf", mock_files)
    print(f"Test 1 (Exact match) - Expected: 'security_policy.pdf', Got: {result1}")
    
    # Test case 2: Match without extension
    result2 = find_best_matching_file("security_policy", mock_files)
    print(f"Test 2 (No extension) - Expected: 'security_policy.pdf', Got: {result2}")
    
    # Test case 3: Fuzzy match
    result3 = find_best_matching_file("security policy", mock_files)
    print(f"Test 3 (Fuzzy match) - Expected: 'security_policy.pdf', Got: {result3}")
    
    # Test case 4: No good match
    result4 = find_best_matching_file("completely_different_file", mock_files)
    print(f"Test 4 (No match) - Expected: None, Got: {result4}")

if __name__ == "__main__":
    print("Testing file_tracker module...")
    test_find_best_matching_file()
    test_extract_referenced_files()
    test_track_files_for_control()
    print("\nAll tests completed!")
