$fileName = "ISO-IEC 27001"
$title = "ISO/IEC 27001"

$content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1000 >>
stream
BT
/F1 24 Tf
100 700 Td
($title) Tj
/F2 12 Tf
0 -40 Td
(ISO/IEC 27001 is an international standard for information security management.) Tj
0 -20 Td
(It specifies the requirements for establishing, implementing, maintaining, and) Tj
0 -20 Td
(continually improving an information security management system (ISMS).) Tj
0 -30 Td
/F1 14 Tf
(Key Components:) Tj
/F2 12 Tf
0 -20 Td
(• Information Security Policies) Tj
0 -20 Td
(• Organization of Information Security) Tj
0 -20 Td
(• Human Resource Security) Tj
0 -20 Td
(• Asset Management) Tj
0 -20 Td
(• Access Control) Tj
0 -20 Td
(• Cryptography) Tj
0 -20 Td
(• Physical and Environmental Security) Tj
0 -20 Td
(• Operations Security) Tj
0 -20 Td
(• Communications Security) Tj
0 -20 Td
(• System Acquisition, Development and Maintenance) Tj
0 -20 Td
(• Supplier Relationships) Tj
0 -20 Td
(• Information Security Incident Management) Tj
0 -20 Td
(• Business Continuity Management) Tj
0 -20 Td
(• Compliance) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1425
%%EOF
"@

# Write the PDF file
[System.IO.File]::WriteAllText("$fileName.pdf", $content)

Write-Host "Created PDF with content: $fileName.pdf"
