@if (showSpinner) {
<div class="spinner-container">
  <mat-spinner></mat-spinner>
</div>
}

<!-- <div class="container p-4">
<h2>ChatBot</h2>
</div> -->


@if (file_id.length==0) {
<div>
  <div class="row justify-content-md-center align-items-center ">
    <div class="container p-4">
      <h2>QnA Chatbot</h2>
    </div>
    <div class="col-md-4 mx-2 justify-content-md-center backdrop">
      <div class="header">
        <h4>Upload Document to Chat</h4>
      </div>
      <mat-divider></mat-divider>
      <div class="body py-4">
        <div class="form-group">
          <input type="file" class="form-control-file" id="evidence" (change)="onChatBotFileChange($event)" multiple>
        </div>
        <span class="input-subtitle">Accepted Formats: .pdf, .docx</span>
      </div>
    </div>
  </div>
  <div class="row justify-content-md-center align-items-center ">
    <div class="col-md-2 my-2 justify-content-md-center ">
      <div class="body">
        <div class="form-group">
          <button mat-button-raised class="btn btn-primary" (click)="uploadFile()">Upload</button>
        </div>
      </div>
    </div>
  </div>
</div>
}


@if (file_id.length>0) {
<div class="main-chat-container container">
  <div class="chats-container">
    <div class="chat-header">
      <h2>QnA Chatbot</h2>
    </div>
    @for (item of messageOrder | keyvalue; track item; let i = $index) {
    <div class="chat-body px-5 py-2  gap-3 " id="chatContainer">
      <!-- Messages will be dynamically added here -->
      <div class="message-container user-container d-flex py-4  mx-auto ">
        <div class="avatar user-avatar">
        </div>
        <div class="message-body">
          <div class="name user-name">
            <span>You</span>
          </div>
          <div class="message user-message">
            {{messageList[messageOrder[i]]["user"]}}
          </div>
        </div>
      </div>
      <div class="message-container bot-container d-flex flex-1 py-2  mx-auto ">
        <div class="avatar bot-avatar">
        </div>
        <div class="message-body">
          <div class="name bot-name">
            <span>Assistant</span>
          </div>
          <div class="message bot-message">
            {{messageList[messageOrder[i]]["chatbot"]}}
          </div>
        </div>
      </div>
    </div>
    }
  </div>
  <div class="custom-input-container pt-2">
    <div class="custom-input">
      <!-- <textarea name="userInput" class="prompt-textarea" autocorrect="off" autocomplete="off" autocapitalize="off"
        spellcheck="false" rows="1" class="svelte-rm3h0e" placeholder="Ask a question..."
      [(ngModel)]="input_message" (keyup.enter)="sendMessage(input_message)" #inputmessage></textarea> -->
      <!-- <input type="text" placeholder="Ask a question..." [(ngModel)]="input_message" (keyup.enter)="sendMessage(input_message)" #inputmessage>   -->
      <textarea rows="3" placeholder="Ask a question..." [(ngModel)]="input_message"
        (keyup.enter)="sendMessage(input_message)" #inputmessage></textarea>
      <mat-icon class="search-icon" matRipple [matRippleColor]="color" aria-hidden="false"
        aria-label="Example home icon" fontIcon="arrow_upward" (click)="sendMessage(input_message)"></mat-icon>
    </div>
  </div>
</div>
}