"""
<PERSON><PERSON><PERSON> to generate dummy PDF documents for all the documents in the mappings.
"""
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the app modules
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.util.generate_dummy_docs import generate_all_dummy_documents

if __name__ == "__main__":
    print("Generating dummy PDF documents...")
    generate_all_dummy_documents()
    print("Done!")
