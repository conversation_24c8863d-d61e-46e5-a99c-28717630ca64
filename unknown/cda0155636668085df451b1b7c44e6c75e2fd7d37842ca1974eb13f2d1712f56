import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../../environments/environment';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-domain-based-selection',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDividerModule
  ],
  templateUrl: './domain-based-selection.component.html',
  styleUrls: ['./domain-based-selection.component.css']
})
export class DomainBasedSelectionComponent implements OnInit {
  @Output() selectionChanged = new EventEmitter<any>();

  domains: string[] = [];
  csps: string[] = [];

  selectedDomain: string = '';
  selectedCsp: string = '';

  cspsByDomain: any = {};

  documents: any = {
    Standard: [],
    Guidelines: [],
    Framework: [],
    Regulation: []
  };

  // Document selection tracking
  selectedDocuments: string[] = [];
  documentSelections: { [key: string]: boolean } = {};

  loading: boolean = false;

  constructor(
    private httpClient: HttpClient,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadDomainOptions();
  }

  loadDomainOptions(): void {
    this.loading = true;
    this.httpClient.get(`${environment.apiBaseUrl}/domain_selection`)
      .subscribe(
        (response: any) => {
          this.domains = response.domains;
          this.cspsByDomain = response.csps_by_domain;
          this.loading = false;
        },
        (error) => {
          console.error('Error loading domain options:', error);
          this.snackBar.open('Error loading domain options. Please try again.', 'Close', {
            duration: 5000
          });
          this.loading = false;
        }
      );
  }

  onDomainChange(): void {
    // Reset CSP selection
    this.selectedCsp = '';
    this.documents = {
      Standard: [],
      Guidelines: [],
      Framework: [],
      Regulation: []
    };

    // Update available CSPs for the selected domain
    this.csps = this.cspsByDomain[this.selectedDomain] || [];

    // If the domain is not Cloud Security/SaaS, load documents directly
    if (this.selectedDomain !== 'Cloud Security/SaaS') {
      this.loadDocuments();
    }

    this.updateSelection();
  }

  onCspChange(): void {
    this.loadDocuments();
    this.updateSelection();
  }

  loadDocuments(): void {
    if (!this.selectedDomain) {
      return;
    }

    // For Cloud Security/SaaS, we need a CSP
    if (this.selectedDomain === 'Cloud Security/SaaS' && !this.selectedCsp) {
      return;
    }

    this.loading = true;
    this.httpClient.get(`${environment.apiBaseUrl}/documents_for_selection`, {
      params: {
        domain: this.selectedDomain,
        csp: this.selectedCsp
      }
    }).subscribe(
      (response: any) => {
        this.documents = response;
        this.initializeDocumentSelections();
        this.loading = false;
      },
      (error) => {
        console.error('Error loading documents:', error);
        this.snackBar.open('Error loading documents. Please try again.', 'Close', {
          duration: 5000
        });
        this.loading = false;
      }
    );
  }

  // Document selection methods
  initializeDocumentSelections(): void {
    this.documentSelections = {};
    this.selectedDocuments = [];

    // Select all documents by default
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.documentSelections[doc] = true;
        this.selectedDocuments.push(doc);
      }
    }

    // Update the selection
    this.updateSelection();
  }

  isDocumentSelected(doc: string): boolean {
    return this.documentSelections[doc] === true;
  }

  toggleDocument(doc: string): void {
    this.documentSelections[doc] = !this.documentSelections[doc];
    this.updateSelectedDocuments();
  }

  isCategorySelected(category: string): boolean {
    if (!this.documents[category] || this.documents[category].length === 0) {
      return false;
    }

    return this.documents[category].every((doc: string) =>
      this.documentSelections[doc] === true
    );
  }

  toggleCategory(category: string): void {
    if (!this.documents[category]) {
      return;
    }

    const newState = !this.isCategorySelected(category);

    for (const doc of this.documents[category]) {
      this.documentSelections[doc] = newState;
    }

    this.updateSelectedDocuments();
  }

  selectAllDocuments(): void {
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.documentSelections[doc] = true;
      }
    }

    this.updateSelectedDocuments();
  }

  deselectAllDocuments(): void {
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.documentSelections[doc] = false;
      }
    }

    this.updateSelectedDocuments();
  }

  updateSelectedDocuments(): void {
    this.selectedDocuments = [];

    for (const doc in this.documentSelections) {
      if (this.documentSelections[doc]) {
        this.selectedDocuments.push(doc);
      }
    }

    // Update the selection
    this.updateSelection();
  }

  updateSelection(): void {
    const selection = {
      domain: this.selectedDomain,
      csp: this.selectedCsp,
      documents: this.documents,
      selectedDocuments: this.selectedDocuments
    };

    this.selectionChanged.emit(selection);
  }

  getDocumentCount(): number {
    return this.documents.Standard.length +
           this.documents.Guidelines.length +
           this.documents.Framework.length +
           this.documents.Regulation.length;
  }

  showCspSelection(): boolean {
    return this.selectedDomain === 'Cloud Security/SaaS';
  }
}
