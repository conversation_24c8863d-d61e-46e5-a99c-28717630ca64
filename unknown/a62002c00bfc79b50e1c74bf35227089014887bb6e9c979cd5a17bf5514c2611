.main-container {
  padding: 0;
  background-size: cover;
  min-height: 100vh;
}

.description {
  color: #ccc;
  margin-bottom: 20px;
}

.selection-container {
  padding: 20px;
  background-color: #252531;
  border-radius: 10px;
}

.selection-section {
  padding-bottom: 30px;
  border-bottom: 1px dashed #333340;
  margin-bottom: 30px;
}

.section-title {
  color: #fff;
  margin-bottom: 15px;
  border-bottom: 1px solid #333340;
  padding-bottom: 10px;
}

/* Document Selection Styles */
.document-selection-panel {
  background-color: #333340;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.document-selection-header {
  color: #ffff00;
  margin-bottom: 15px;
  font-size: 18px;
}

.document-selection-actions {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.document-selection-count {
  margin-left: auto;
  color: #ccc;
  font-weight: bold;
}

.document-category {
  background-color: #252531;
  border-radius: 5px;
  margin-bottom: 15px;
  overflow: hidden;
}

.category-header {
  padding: 10px;
  background-color: #3a3a4a;
  border-bottom: 1px solid #444;
}

.category-label {
  display: flex;
  align-items: center;
  color: white;
  font-weight: bold;
  cursor: pointer;
  margin: 0;
}

.category-label input {
  margin-right: 10px;
  width: 18px;
  height: 18px;
}

.document-items {
  max-height: 200px;
  overflow-y: auto;
  padding: 5px;
}

.document-item {
  padding: 8px 10px;
  border-bottom: 1px solid #333340;
}

.document-item:hover {
  background-color: #3a3a4a;
}

.document-item label {
  display: flex;
  align-items: center;
  color: #ccc;
  cursor: pointer;
  margin: 0;
  width: 100%;
}

.document-item input {
  margin-right: 10px;
  width: 16px;
  height: 16px;
}

/* Summary Panel Styles */
.summary-panel {
  padding: 20px;
  background-color: #333340;
  border-radius: 10px;
  margin-top: 20px;
}

.selection-summary {
  margin-top: 15px;
  padding: 15px;
  background-color: #252531;
  border-radius: 5px;
}

.selection-summary h5 {
  color: #fff;
  margin-bottom: 10px;
}

.selection-summary p {
  color: #ccc;
  margin-bottom: 5px;
}

/* Navigation Buttons */
.navigation-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn {
  background: yellow;
  color: #333;
  border: none;
  padding: 8px 20px;
  font-weight: 500;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 14px;
}

.btn-success {
  background-color: #4CAF50;
  color: white;
}

.btn-danger {
  background-color: #f44336;
  color: white;
}

.btn-secondary {
  background: #333340;
  color: #fff;
  margin-right: 10px;
}

.btn:disabled {
  background: #888;
  color: #333;
}

/* Scrollbar styling */
.document-items::-webkit-scrollbar {
  width: 8px;
}

.document-items::-webkit-scrollbar-track {
  background: #252531;
}

.document-items::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 4px;
}

.document-items::-webkit-scrollbar-thumb:hover {
  background: #555;
}
