{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2fdca3eb", "metadata": {}, "outputs": [], "source": ["#import libraries\n", "import os\n", "import json\n", "import pandas as pd\n", "import openai\n", "from dotenv import load_dotenv,find_dotenv\n", "import base64\n", "from mimetypes import guess_type\n", "\n", "from langchain.embeddings.openai import OpenAIEmbeddings\n", "#from langchain.vectorstores import FAISS\n", "from langchain.chains.question_answering import load_qa_chain\n", "#from langchain.llms import AzureOpenAI\n", "#from langchain.chat_models import AzureChatOpenAI\n", "from langchain_openai import AzureChatOpenAI\n", "\n", "load_dotenv(find_dotenv())\n", "#openai.api_type = os.environ[\"OPENAI_API_TYPE\"]\n", "#openai.api_key = os.environ[\"OPENAI_API_KEY\"]\n", "#openai.api_base = os.environ[\"OPENAI_API_BASE\"]\n", "#openai.api_version = os.environ[\"OPENAI_API_VERSION\"]\n", "turbo_endpoint = os.environ[\"TURBO_API_BASE\"]\n", "vision_endpoint = os.environ[\"VISION_API_BASE\"]\n", "turbo_api_key = os.environ[\"TURBO_API_KEY\"]\n", "vision_api_key = os.environ[\"VISION_API_KEY\"]\n", "turbo_api_version = os.environ[\"TURBO_API_VERSION\"]\n", "vision_api_version = os.environ[\"VISION_API_VERSION\"]\n", "\n", "#Model and deployment configs\n", "model_name='gpt-35-turbo'\n", "model_deployment = 'gpt35turbo0301Deployment'\n", "model_deployment_GPT4 = 'edwardJonesBot'\n", "model_name_GPT4 = 'gpt-4-32k'\n", "model_deployment_GPT4_vision = 'dellimagevisiongpt'\n", "model_name_GPT4_vision = 'gpt-4'\n", "model_name_gpt4_turbo = 'gpt-4'\n", "model_deployment_gpt4_turbo = 'GPT4-Turbo'\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c93efb40", "metadata": {}, "outputs": [], "source": ["#File and folder paths\n", "questoinnaire_file_path = r\"C:\\Users\\<USER>\\OneDrive - EY\\Documents\\Projects\\Cyber Security - IsecMapper\\Questionnare Template.xlsx\"\n", "evidence_folder_path = r\"C:\\Users\\<USER>\\OneDrive - EY\\Documents\\Projects\\Cyber Security - IsecMapper\\AIA evidences\""]}, {"cell_type": "code", "execution_count": 4, "id": "2de0537e", "metadata": {}, "outputs": [], "source": ["#read excel\n", "questionnare = pd.read_excel(questoinnaire_file_path)"]}, {"cell_type": "code", "execution_count": 5, "id": "2a318faa", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'10': {'text_files': ['10_ACP - 1.docx'], 'image_files': ['10_SS - 2.png']},\n", " '1': {'text_files': ['1_IS.pdf'], 'image_files': []},\n", " '2': {'text_files': ['2_ISRP.pdf'], 'image_files': []},\n", " '3': {'text_files': ['3_ISAT.pdf'], 'image_files': []},\n", " '4': {'text_files': ['4_EBV.pdf'], 'image_files': []},\n", " '5': {'text_files': ['5_NDA.pdf'], 'image_files': []},\n", " '6': {'text_files': ['6_ICHP - 2.pdf', '6_IT_Asset Management - 1.pdf'],\n", "  'image_files': []},\n", " '7': {'text_files': ['7_DCPS.pdf'], 'image_files': []},\n", " '8': {'text_files': [],\n", "  'image_files': ['8_settings - 1.png', '8_settings- 2.png']},\n", " '9': {'text_files': ['9_MDMP.pdf'], 'image_files': []}}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get evidence details\n", "\n", "# Get a list of all files in the folder\n", "all_files = os.listdir(evidence_folder_path)\n", "\n", "# Group files based on the common prefix and type (text or image)\n", "file_groups = {}\n", "\n", "text_file_types = ['pdf', 'docx', 'doc', 'txt']\n", "image_file_types = ['jpg', 'jpeg', 'png']\n", "\n", "for file_name in all_files:\n", "    # Extract the common prefix (number and underscore)\n", "    common_prefix = file_name.split('_')[0]\n", "\n", "    # Determine if the file is a text or image file based on its extension\n", "    file_extension = file_name.split('.')[-1].lower()\n", "\n", "    if file_extension in text_file_types:\n", "        file_type = 'text_files'\n", "    elif file_extension in image_file_types:\n", "        file_type = 'image_files'\n", "    else:\n", "        continue  # Skip files with unsupported extensions\n", "\n", "    # Create a dictionary for the common prefix if it doesn't exist\n", "    if common_prefix not in file_groups:\n", "        file_groups[common_prefix] = {'text_files': [], 'image_files': []}\n", "\n", "    # Append the file to the corresponding type within the common prefix group\n", "    file_groups[common_prefix][file_type].append(file_name)\n", "file_groups"]}, {"cell_type": "code", "execution_count": 6, "id": "db2f5ea8", "metadata": {}, "outputs": [], "source": ["# loading PDF, DOCX and TXT files as LangChain Documents\n", "def load_document(file):\n", "\n", "    name, extension = os.path.splitext(file)\n", "\n", "    if extension == '.pdf':\n", "        from langchain.document_loaders import PyPDFLoader\n", "        print(f'Loading {file}')\n", "        loader = PyPDFLoader(file)\n", "    elif extension == '.docx':\n", "        from langchain.document_loaders import Docx2txtLoader\n", "        print(f'Loading {file}')\n", "        loader = Docx2txtLoader(file)\n", "    elif extension == '.txt':\n", "        from langchain.document_loaders import TextLoader\n", "        loader = TextLoader(file)\n", "    else:\n", "        print('Document format is not supported!')\n", "        return None\n", "\n", "    pages = loader.load_and_split()\n", "    \n", "    return pages"]}, {"cell_type": "code", "execution_count": 7, "id": "b9a243aa", "metadata": {}, "outputs": [], "source": ["def get_response_text_evidence(domain,control,reference,data):\n", "    context = f'''you are a Cyber Security Risk Analyst. you need to create an assessment report. \n", "            In the assessment report there is an observation section for which you need to provide the details.\n", "            you will be provided with a control and reference information regarding control delimited by *** from {domain} domain.\n", "            You will also be provided with proofs which will be in the form of input documents.Proofs will be many documents which will related to a control or not.\n", "            your job is to understand the control and provided reference information with it to analyse the proofs provided. \n", "            you need to provide elaborated observations on your analysis of the control and the proof that if the proofs comply with the control mentioned.\n", "            \n", "            ***\n", "            Control: {control}\n", "            Reference information : {reference}\n", "            \n", "            ***\n", "            \n", "            Few points to consider while answering:\n", "            - Understand the control and its refernce in the cyber security domain. Reference will help you undersatnd how to validate the control and the proofs.\n", "            Do not mention your understaning of the control or the task to be perfomed in the response. Just mention your observation.\n", "            - Provide elaborate and accurate observations regarding control and the proofs provided.\n", "            - You have to provide the document names and content from where the control comply with the proofs. \n", "            - If none of the proof mentions about the control. Mention about it in the observation.\n", "            - If proofs are not provided then mention proof is not provided for the control.\n", "            - Only provide your observations and do not explicitly mention the control in the response.\n", "            '''\n", "    \n", "    chain = load_qa_chain(llm=AzureChatOpenAI(\n", "                        base_url=f\"{turbo_endpoint}/openai/deployments/{model_deployment_gpt4_turbo}\",\n", "                        api_key=turbo_api_key, \n", "                        model_name=model_name_gpt4_turbo,\n", "                        api_version=turbo_api_version,\n", "                        temperature=0), chain_type=\"stuff\")\n", "    response = chain.invoke({'input_documents': data, 'question':context},return_only_outputs=True)\n", "    \n", "    return response['output_text']"]}, {"cell_type": "code", "execution_count": 8, "id": "c53bd376", "metadata": {}, "outputs": [], "source": ["# Function to encode a local image into data URL \n", "def local_image_to_data_url(image_path):\n", "    # Guess the MIME type of the image based on the file extension\n", "    mime_type, _ = guess_type(image_path)\n", "    if mime_type is None:\n", "        mime_type = 'application/octet-stream'  # Default MIME type if none is found\n", "\n", "    # Read and encode the image file\n", "    with open(image_path, \"rb\") as image_file:\n", "        base64_encoded_data = base64.b64encode(image_file.read()).decode('utf-8')\n", "\n", "    # Construct the data URL\n", "    return f\"data:{mime_type};base64,{base64_encoded_data}\""]}, {"cell_type": "code", "execution_count": 9, "id": "499a2f6a", "metadata": {}, "outputs": [], "source": ["def get_response_image_evidence(domain,control,reference,data_urls):\n", "    from openai import AzureOpenAI\n", "    api_base = vision_endpoint\n", "    api_key= vision_api_key\n", "    deployment_name = model_deployment_GPT4_vision\n", "    api_version = vision_api_version # this might change in the future\n", "    \n", "    context = f'''you are a Cyber Security Risk Analyst. you need to create an assessment report. \n", "            In the assessment report there is an observation section for which you need to provide the details.\n", "            you will be provided with a control and reference information regarding control delimited by *** from {domain} domain.\n", "            You will also be provided with proofs which will be in the form of image. Image can be related to the control or not.\n", "            your job is to understand the control and provided reference information with it to analyse the proofs provided. \n", "            you need to provide elaborated observations on your analysis of the control and the proof that if the proofs comply with the control mentioned.\n", "            \n", "            ***\n", "            Control: {control}\n", "            Reference information : {reference}\n", "            \n", "            ***\n", "            \n", "            Few points to consider while answering:\n", "            - Understand the control and its refernce in the cyber security domain. Reference will help you undersatnd how to validate the control and the proofs.\n", "            Do not mention your understaning of the control or the task to be perfomed in the response. Just mention your observation\n", "            - Provide elaborate and accurate observations regarding control and the proofs provided.\n", "            - If none of the proof mentions about the control. Mention about it in the observation.\n", "            - If proofs are not provided then mention proof is not provided for the control.\n", "            - Only provide your observations and do not explicitly mention the control in the response.\n", "            '''\n", "    \n", "    client = AzureOpenAI(\n", "        api_key=api_key,  \n", "        api_version=api_version,\n", "        base_url=f\"{api_base}/openai/deployments/{deployment_name}\"\n", "    )\n", "    \n", "    message = [\n", "    {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "    {\"role\": \"user\", \"content\": [{ \n", "                \"type\": \"text\", \n", "                \"text\": context\n", "            }]}\n", "        ]\n", "\n", "    for data_url in data_urls:\n", "        message[1][\"content\"].append(\n", "            {\"type\": \"image_url\", \"image_url\": {\"url\": data_url}})\n", "        \n", "    response = client.chat.completions.create(\n", "        model=deployment_name,\n", "     messages = message,\n", "    max_tokens=2000 \n", "    )\n", "\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 12, "id": "e3c6ef51", "metadata": {}, "outputs": [], "source": ["def summarize_observations(domain,control,reference,observation_1,observation_2):\n", "    from openai import AzureOpenAI\n", "    context = f'''you are a Cyber Security Risk Analyst. \n", "            you will be provided with a control and reference information regarding control delimited by *** from {domain} domain.\n", "            You will also be provided with 2 observations which where observation 1 will be from proof submitted in pdf,docx format \n", "            and observation 2 will be from proof submitted in image format.\n", "            your job is to understand the control and provided reference information with it to summarise the 2 observations \n", "            and provide a final observation for the summary report. \n", "            \n", "            ***\n", "            Control: {control}\n", "            Reference information : {reference}\n", "            \n", "            ***\n", "            \n", "            Few points to consider while answering:\n", "            - Understand the control and its refernce in the cyber security domain. Reference will help you undersatnd more context on the control.\n", "            - Provide accurate summary from both observations.\n", "            - Do not skip any specific information from proofs. \n", "            - Do not try to make up anything, strictly provide summary from both observations.\n", "            '''\n", "    \n", "    user_input = f'''Observation 1 : {observation_1}\n", "                 Observation 2 : {observation_2}'''\n", "    \n", "    client = AzureOpenAI(\n", "      azure_endpoint = turbo_endpoint, \n", "      api_key=turbo_api_key,  \n", "      api_version=turbo_api_version\n", "        )\n", "    response = client.chat.completions.create(\n", "    model=model_deployment_gpt4_turbo, # engine = \"deployment_name\".\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": context},\n", "        {\"role\": \"user\", \"content\": user_input}])\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 15, "id": "dfcb8514", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Control : Do you have an information security awareness program which include mandatory trainings to employees for below security topics? \n", "- Ransomware/emerging threats\n", "- Clean Desk and Laptop Security Guidelines\n", "\n", "                Evidence files: ['3_ISAT.pdf'] \n", "                ===============================================================================\n", "Loading C:\\Users\\<USER>\\OneDrive - EY\\Documents\\Projects\\Cyber Security - IsecMapper\\AIA evidences\\3_ISAT.pdf\n", "observation_text:  {\"observation\": \"Based on the information provided in the Information Security Awareness Training Policy documents, it is observed that the organization has implemented a comprehensive information security awareness program. This program is designed to educate all personnel, including employees across various categories, on their responsibilities towards protecting the confidentiality, availability, and integrity of the organization's information assets. The policy mandates annual Information Security Awareness Training for all personnel, which is administered through the KnowBe4 web-based learning platform.\\n\\nThe training content, as outlined in the policy, covers a range of cybersecurity topics. However, the specific topics of ransomware/emerging threats and clean desk and laptop security guidelines are not explicitly mentioned in the provided documents. The training includes education on virus or malicious software (Malware), phishing attempts, social engineering, and application/operating system vulnerabilities. Additionally, the program incorporates periodic phishing training and remedial education as necessary, which suggests a commitment to addressing emerging threats.\\n\\nThe policy also mandates new hire security awareness training, ensuring that newly hired personnel are educated on information security best practices within 30 days of their orientation. This indicates an ongoing effort to maintain a high level of security awareness among all employees from the start of their employment.\\n\\nWhile the policy demonstrates a robust approach to information security awareness and training, the absence of explicit mention of ransomware/emerging threats and clean desk and laptop security guidelines in the provided documents means that it cannot be confirmed if the current training program fully addresses the control in question. The policy does indicate that the awareness program is updated regularly to align with organizational policies and procedures, which could potentially include training on these specific topics, but this is not directly evidenced in the provided documentation.\\n\\nIn conclusion, the provided Information Security Awareness Training Policy documents show a structured and comprehensive approach to security awareness training within the organization. However, without explicit mention or proof of training material covering ransomware/emerging threats and clean desk and laptop security guidelines, it cannot be definitively stated that the training program fully complies with the specified control. Further documentation or evidence of training content covering these specific areas would be required to fully assess compliance.\", \"control_id\": \"3\", \"control\": \"Do you have an information security awareness program which include mandatory trainings to employees for below security topics? \\n- Ransomware/emerging threats\\n- Clean Desk and Laptop Security Guidelines\\n\", \"evidence_files\": [\"3_ISAT.pdf\"]}\n", "=====================================================================\n", "Control : Do you restrict user from installing unauthorized or unlicensed software?\n", "                Evidence files: ['8_settings - 1.png', '8_settings- 2.png'] \n", "                ==========================================================\n", "observation_image:  {\"observation\": \"The provided proofs indicate that measures are in place to restrict the use of Windows Installer for non-managed applications only, as shown in the Local Group Policy Editor screenshots. The 'Turn off Windows Installer' setting is enabled, which is consistent with a policy that aims to limit users' ability to install software, in line with preventing the installation of unauthorized or unlicensed software. The setting specifically restricts the installation to programs offered or permitted by a system administrator, which reduces the risk of unauthorized software installations. This suggests adherence to good practices in asset and data management regarding software installation restrictions.\", \"control_id\": \"8\", \"control\": \"Do you restrict user from installing unauthorized or unlicensed software?\", \"evidence_files\": [\"8_settings - 1.png\", \"8_settings- 2.png\"]}\n", "=====================================================================\n", "Control : Do you have an access control policy that covers below requirements?\n", "1. Users are uniquely identified during authentication process\n", "2. Principles of least privilege, segregation of duties (SoD) and need-to-know followed during user authorization\n", "3. Policies and procedures for removal of access for employees who are terminated or transferred. \n", "4. The validity of user account is performed periodically.\n", "                Evidence files: ['10_ACP - 1.docx'] and ['10_SS - 2.png']\n", "                =================================================================\n", "Loading C:\\Users\\<USER>\\OneDrive - EY\\Documents\\Projects\\Cyber Security - IsecMapper\\AIA evidences\\10_ACP - 1.docx\n", "observation_text:  I'm sorry, but without specific proof documents or further details provided, I cannot generate an observation analysis. Please provide the necessary documents or information related to the control for analysis.\n", "=====================================================================\n", "observation_image:  The provided image appears to be a matrix mapping of various roles against permissions or access levels within the organization or system, indicating whether certain roles have specific access rights with markers such as 'x' or 'xs'. \n", "\n", "This matrix could serve as evidence that the organization has a structured role-based access control system in place, which is an element of enforcing the principle of least privilege and segregation of duties. The presence of different roles, e.g., Full Administrators, Packagers, Application Administrators, etc., suggests that access is potentially being differentiated based on the roles within the system, hopefully in alignment with the 'need to know' principle. However, without explicit context or detailed explanations within the image itself, it is unclear whether the access is limited strictly to what each role requires.\n", "\n", "Furthermore, the image does not indicate whether these access privileges are reviewed and validated periodically nor does it demonstrate the policies for removal of access upon employee termination or transfer. Details about unique identification during the authentication process are also not evident from this image. \n", "\n", "To validate the access control policy fully, more detailed documentation and evidence would be required, including logs or records of periodic access reviews, the authentication mechanisms in place for unique user identification, and the processes for modifying access rights when users are terminated or transferred. \n", "\n", "In summary, while the image serves as an indicator of role-based access control, it is not sufficient to conclude full adherence to the mentioned access control policy without additional corroborative evidence. Observations are limited to the roles and access mapping presented, and critical components of the control requirements remain unverified.\n", "=====================================================================\n", "Summarization:  {\"observation\": \"Based on the provided observations and the control requirements outlined, we can deduce the following final observation for the summary report:\\n\\nThe organization has implemented elements of an access control policy as evidenced by the role-based access control matrix observed in the provided image. This matrix suggests that the organization attempts to adhere to principles of least privilege and segregation of duties by differentiating access based on roles such as Full Administrators, Packagers, Application Administrators, etc. However, this evidence alone does not fully satisfy the control requirements, as it lacks detailed validation of several critical aspects:\\n\\n1. Unique identification during the authentication process is not addressed in the provided proof. Essential for ensuring that each user's actions can be individually accounted for, this is a fundamental requirement that remains unverified.\\n\\n2. While the role-based access matrix implies an attempt to follow the least privilege and need-to-know principles, there is insufficient detail to confirm that access rights are strictly limited to what each role requires. Moreover, the matrix does not provide information on the periodic review of user account validity, which is crucial for maintaining secure and appropriate access over time.\\n\\n3. Policies and procedures for the removal of access for employees who are terminated or transferred are not evidenced in the provided documentation. This is a vital control for preventing unauthorized access by former employees or those changing roles.\\n\\n4. The absence of explicit evidence of periodic reviews or validations of access privileges raises concerns about whether the organization periodically verifies that access rights remain aligned with current roles, responsibilities, and the principle of least privilege.\\n\\nIn conclusion, while the organization shows a foundational structure for role-based access control, the evidence provided is insufficient to conclusively demonstrate compliance with all aspects of the specified access control policy. Additional documentation and evidence\\u2014such as access review logs, authentication process descriptions, and policies for updating or revoking access\\u2014are required to fully validate adherence to the control requirements.\", \"control_id\": \"10\", \"control\": \"Do you have an access control policy that covers below requirements?\\n1. Users are uniquely identified during authentication process\\n2. Principles of least privilege, segregation of duties (SoD) and need-to-know followed during user authorization\\n3. Policies and procedures for removal of access for employees who are terminated or transferred. \\n4. The validity of user account is performed periodically.\", \"evidence_files\": [\"10_ACP - 1.docx\", \"10_SS - 2.png\"]}\n", "=====================================================================\n"]}], "source": ["for index, row in questionnare.iterrows():\n", "    control_id = str(row['#'])\n", "    control = row['Control']\n", "    domain = row['Security Domain']\n", "    reference = row['Reference']\n", "    \n", "    if file_groups[control_id]['text_files'] and not file_groups[control_id]['image_files']:\n", "        print(f'''Control : {control}\n", "                Evidence files: {file_groups[control_id]['text_files']} \n", "                ===============================================================================''')\n", "        data = []\n", "        for file in file_groups[control_id]['text_files']:\n", "            data.extend(load_document(evidence_folder_path+'\\\\'+file))\n", "            \n", "        observation_text = get_response_text_evidence(domain,control,reference,data)\n", "        #print('observation_text:', observation_text)\n", "        \n", "        \n", "        json_dict = {\"observation\":observation_text,\"control_id\":control_id,\"control\":control, \"evidence_files\":file_groups[control_id]['text_files']}\n", "        json_string= json.dumps(json_dict)\n", "        print('observation_text: ',json_string)\n", "        print('=====================================================================')\n", "        \n", "    elif not file_groups[control_id]['text_files'] and file_groups[control_id]['image_files']:\n", "        print(f'''Control : {control}\n", "                Evidence files: {file_groups[control_id]['image_files']} \n", "                ==========================================================''')\n", "        \n", "        image_count = len(file_groups[control_id]['image_files'])\n", "        data_urls = []\n", "        for file in file_groups[control_id]['image_files']:\n", "            data_urls.append(local_image_to_data_url(evidence_folder_path+'\\\\'+file))\n", "        \n", "        observation_image = get_response_image_evidence(domain,control,reference,data_urls)\n", "        #print('observation_image:',observation_image)\n", "        \n", "        json_dict = {\"observation\":observation_image,\"control_id\":control_id,\"control\":control, \"evidence_files\":file_groups[control_id]['image_files']}\n", "        json_string= json.dumps(json_dict)\n", "        \n", "        print('observation_image: ',json_string)\n", "        print('=====================================================================')\n", "    \n", "    elif file_groups[control_id]['text_files'] and file_groups[control_id]['image_files']:\n", "        \n", "        print(f'''Control : {control}\n", "                Evidence files: {file_groups[control_id]['text_files']} and {file_groups[control_id]['image_files']}\n", "                =================================================================''')\n", "        data = []\n", "        for file in file_groups[control_id]['text_files']:\n", "            data.extend(load_document(evidence_folder_path+'\\\\'+file))\n", "            \n", "        observation_text = get_response_text_evidence(domain,control,reference,data)\n", "        print('observation_text: ',observation_text)\n", "        print('=====================================================================')\n", "        \n", "        \n", "        image_count = len(file_groups[control_id]['image_files'])\n", "        data_urls = []\n", "        for file in file_groups[control_id]['image_files']:\n", "            data_urls.append(local_image_to_data_url(evidence_folder_path+'\\\\'+file))\n", "        \n", "        observation_image = get_response_image_evidence(domain,control,reference,data_urls)\n", "        print('observation_image: ',observation_image)\n", "        print('=====================================================================')\n", "        \n", "        summarized_observation = summarize_observations(domain,control,reference,observation_text,observation_image)\n", "        #print('Summarization: ',summarized_observation)\n", "        evidence_files =  file_groups[control_id]['text_files'] + file_groups[control_id]['image_files']\n", "        \n", "        json_dict = {\"observation\":summarized_observation,\"control_id\":control_id,\"control\":control, \"evidence_files\": evidence_files}\n", "        json_string= json.dumps(json_dict)\n", "        \n", "        print('Summarization: ',json_string)\n", "        print('=====================================================================')\n", "        \n", "        \n", "        \n", "        \n", "    else:\n", "        print(f'for control ID: {control_id}, No evidence file present')"]}, {"cell_type": "code", "execution_count": null, "id": "9e4b827e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}