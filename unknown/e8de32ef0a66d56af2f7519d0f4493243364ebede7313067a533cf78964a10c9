import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MaterialModule } from '../../material/material.module';
import { DomainSelectionComponent } from './components/domain-selection.component';
import { DomainSelectionRoutingModule } from './domain-selection-routing.module';

@NgModule({
  declarations: [],
  providers: [],
  imports: [
    CommonModule,
    MaterialModule,
    DomainSelectionRoutingModule,
    DomainSelectionComponent,
  ],
})
export class DomainSelectionModule {}
