%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R /F3 6 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 7 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Oblique
>>
endobj
7 0 obj
<< /Length 5000 >>
stream
BT
/F1 24 Tf
50 750 Td
(FedRAMP) Tj

/F2 12 Tf
0 -30 Td
(Federal Risk and Authorization Management Program) Tj
0 -15 Td
(Document Version: 1.0) Tj
0 -15 Td
(Last Updated: April 2025) Tj

/F1 18 Tf
-10 -40 Td
(1. Introduction) Tj
/F2 12 Tf
10 -20 Td
(The Federal Risk and Authorization Management Program 
(FedRAMP) is a government-wide program that provides 
a standardized approach to security assessment, authorization, 
and continuous monitoring for cloud products and 
services.

FedRAMP was established to support the U.S. 
government's 'Cloud First' policy by providing a 
cost-effective, risk-based approach for the adoption and 
use of cloud services by federal agencies. 
It enables agencies to rapidly leverage cloud 
solutions with adequate security controls, reducing the 
duplication of effort and inconsistency in security 
assessments.

The program creates transparent standards and processes 
for security authorizations and provides a repository 
of authorizations that can be leveraged government-wide.) Tj

/F1 18 Tf
-10 -30 Td
(2. History and Development) Tj
/F2 12 Tf
10 -20 Td
(FedRAMP was established in December 2011 by 
a memorandum from the U.S. Office of 
Management and Budget (OMB). It was developed 
in collaboration with the National Institute of 
Standards and Technology (NIST), the General Services 
Administration (GSA), the Department of Defense (DoD), 
and the Department of Homeland Security (DHS).

The program became operational in 2012 with 
the establishment of the FedRAMP Program Management 
Office (PMO) within the GSA. The first 
provisional authorizations were granted in 2013.

FedRAMP has evolved over time to improve 
efficiency and effectiveness. Major updates include the 
introduction of FedRAMP Accelerated in 2016 to 
streamline the authorization process, and FedRAMP Tailored 
in 2017 to provide a lighter-weight process 
for low-risk cloud services.

In December 2022, the FedRAMP Authorization Act 
was signed into law, codifying the program 
and establishing a Federal Secure Cloud Advisory 
Committee to support its continued evolution and 
improvement.) Tj

/F1 18 Tf
-10 -30 Td
(3. Key Components) Tj
/F2 12 Tf
10 -20 Td
(FedRAMP encompasses several key components:

â€¢ Security Controls Based on NIST SP 800-53
  - Baseline security requirements for Low, Moderate, and High impact levels
  - Controls addressing confidentiality, integrity, and availability
  - Additional FedRAMP-specific requirements
  - Continuous monitoring controls
  - Privacy controls

â€¢ Standardized Security Assessment
  - Security Assessment Framework (SAF)
  - Standardized templates and documentation
  - Defined assessment procedures
  - Evidence collection requirements
  - Vulnerability scanning standards

â€¢ Authorization Process
  - Initial authorization assessment
  - Security Authorization Package
  - System Security Plan (SSP)
  - Security Assessment Report (SAR)
  - Plan of Action and Milestones (POA&M)
  - Authorization decision

â€¢ Continuous Monitoring
  - Monthly vulnerability scanning
  - Plan of Action and Milestones (POA&M) management
  - Significant change requests
  - Annual assessment
  - Incident response and reporting

â€¢ Risk Management
  - Risk-based approach to security
  - Tailored security requirements based on impact level
  - Ongoing risk assessment
  - Mitigation strategies
  - Acceptance of residual risk

â€¢ Documentation Requirements
  - System Security Plan (SSP)
  - Security Assessment Plan (SAP)
  - Security Assessment Report (SAR)
  - Plan of Action and Milestones (POA&M)
  - Continuous Monitoring Plan
  - Incident Response Plan

â€¢ Third-Party Assessment Organizations (3PAOs)
  - Accredited independent assessors
  - Standardized assessment methodology
  - Quality assurance
  - Ongoing monitoring of 3PAO performance
  - Training and certification requirements

â€¢ Agency Authorization
  - Agency-specific security requirements
  - Authority to Operate (ATO) process
  - Agency-specific risk acceptance
  - Integration with agency security programs
  - Ongoing oversight by authorizing official

â€¢ Joint Authorization Board (JAB) Provisional Authorization
  - Review by DoD, DHS, and GSA representatives
  - Provisional Authority to Operate (P-ATO)
  - Government-wide risk acceptance
  - Prioritization of high-impact cloud services
  - Continuous monitoring oversight

â€¢ FedRAMP Marketplace
  - Repository of authorized cloud services
  - Status tracking of authorization process
  - Reuse of authorizations across agencies
  - Transparency for cloud service providers
  - Resource for agency cloud adoption) Tj

/F1 18 Tf
-10 -30 Td
(4. Implementation Process) Tj
/F2 12 Tf
10 -20 Td
(Implementing FedRAMP authorization typically involves the following 
steps:

1. Determine FedRAMP applicability
   - Assess if your service meets the definition of a cloud service
   - Determine if federal agencies are target customers
   - Identify which data impact level applies (Low, Moderate, High)
   - Choose authorization path (Agency or JAB)
   - Evaluate readiness for the FedRAMP process

2. Prepare for authorization
   - Conduct a readiness assessment
   - Implement required security controls
   - Develop system security documentation
   - Select a 3PAO for assessment
   - Engage with the FedRAMP PMO
   - Identify a sponsoring agency (for Agency path)

3. Develop security documentation
   - Create System Security Plan (SSP)
   - Document control implementations
   - Develop supporting documentation
   - Prepare customer responsibility matrix
   - Create incident response plan
   - Develop continuous monitoring strategy

4. Conduct security assessment
   - Engage accredited 3PAO
   - Develop Security Assessment Plan (SAP)
   - Perform testing of security controls
   - Conduct vulnerability scans
   - Document findings and recommendations
   - Prepare Security Assessment Report (SAR)

5. Address assessment findings
   - Review identified vulnerabilities
   - Remediate high and critical findings
   - Develop Plan of Action and Milestones (POA&M)
   - Implement mitigating controls
   - Document risk acceptance rationale
   - Obtain 3PAO validation of remediation

6. Obtain authorization
   - Submit authorization package
   - Respond to reviewer questions
   - Make required adjustments
   - Receive provisional authorization (JAB P-ATO) or
     agency authorization (Agency ATO)
   - List service in FedRAMP Marketplace

7. Implement continuous monitoring
   - Conduct monthly vulnerability scans
   - Submit monthly continuous monitoring deliverables
   - Manage and update POA&M items
   - Report security incidents
   - Perform annual assessment
   - Process significant change requests) Tj

/F1 18 Tf
-10 -30 Td
(5. Benefits of Implementation) Tj
/F2 12 Tf
10 -20 Td
(Implementing FedRAMP provides numerous benefits to cloud 
service providers and federal agencies:

â€¢ For Cloud Service Providers
  - Access to federal market
  - Competitive advantage in government contracting
  - Standardized security requirements
  - Reusable security authorization
  - Enhanced security posture
  - Reduced time to market for government customers
  - Demonstration of security commitment

â€¢ For Federal Agencies
  - Cost savings through reuse of authorizations
  - Consistent security assessments
  - Risk-based approach to cloud adoption
  - Time savings in authorization process
  - Access to secure cloud solutions
  - Standardized continuous monitoring
  - Improved security oversight

â€¢ Security Benefits
  - Comprehensive security controls
  - Independent security validation
  - Ongoing security monitoring
  - Standardized incident response
  - Vulnerability management
  - Supply chain risk management
  - Security documentation and transparency

â€¢ Operational Benefits
  - Standardized processes
  - Clear documentation requirements
  - Defined roles and responsibilities
  - Consistent assessment methodology
  - Structured continuous monitoring
  - Established change management
  - Incident response procedures

â€¢ Compliance Benefits
  - Alignment with federal security requirements
  - Support for FISMA compliance
  - Standardized approach to NIST controls
  - Documentation for audit purposes
  - Evidence of due diligence
  - Demonstration of security capability
  - Regulatory alignment) Tj

/F3 10 Tf
-20 -30 Td
(This document provides an overview of FedRAMP. For complete and authoritative information,) Tj
0 -15 Td
(please refer to the official documentation and standards publications.) Tj
ET
endstream
endobj
xref
0 8
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
0000000444 00000 n
trailer
<< /Size 8
/Root 1 0 R
>>
startxref
5496
%%EOF