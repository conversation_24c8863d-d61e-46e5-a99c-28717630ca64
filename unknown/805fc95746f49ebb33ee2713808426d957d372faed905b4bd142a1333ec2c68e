import os
from openai import AzureOpenAI
from dotenv import load_dotenv

# Load environment variables
env_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), ".env")
print(f"Loading .env file from: {env_path}")
print(f"File exists: {os.path.exists(env_path)}")

load_dotenv(
    override=True,
    verbose=True,
    dotenv_path=env_path,
)

# Get environment variables
MSP_AZURE_OPENAI_VERSION = os.getenv("MSP_AZURE_OPENAI_VERSION")
MSP_AZURE_OPENAI_ENDPOINT = os.getenv("MSP_AZURE_OPENAI_ENDPOINT")
MSP_AZURE_OPENAI_KEY = os.getenv("MSP_AZURE_OPENAI_KEY")
MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME = os.getenv("MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME")
MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME = os.getenv("MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME")

print(f"\nAzure OpenAI Configuration:")
print(f"MSP_AZURE_OPENAI_VERSION: {MSP_AZURE_OPENAI_VERSION}")
print(f"MSP_AZURE_OPENAI_ENDPOINT: {MSP_AZURE_OPENAI_ENDPOINT}")
print(f"MSP_AZURE_OPENAI_KEY: {MSP_AZURE_OPENAI_KEY}")
print(f"MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME: {MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME}")
print(f"MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME: {MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME}")

# Test GPT-4 Turbo deployment
print("\nTesting GPT-4 Turbo deployment...")
try:
    client = AzureOpenAI(
        api_key=MSP_AZURE_OPENAI_KEY,
        api_version=MSP_AZURE_OPENAI_VERSION,
        azure_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
        azure_deployment=MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME
    )
    
    # List available models to verify connection
    models = client.models.list()
    print("Available models:")
    for model in models:
        print(f" - {model.id}")
    
    print("GPT-4 Turbo deployment test successful!")
except Exception as e:
    print(f"Error testing GPT-4 Turbo deployment: {e}")

# Test Embedding deployment
print("\nTesting Embedding deployment...")
try:
    client = AzureOpenAI(
        api_key=MSP_AZURE_OPENAI_KEY,
        api_version=MSP_AZURE_OPENAI_VERSION,
        azure_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
        azure_deployment=MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME
    )
    
    # Try to get embeddings for a simple text
    response = client.embeddings.create(
        input="Hello, world!",
        model=MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME
    )
    print("Embedding test successful!")
except Exception as e:
    print(f"Error testing Embedding deployment: {e}")

print("\nTest completed.")
