import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from './layout.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'home',
      },

      {
        path: 'home',
        children: [
          {
            path: '',
            loadChildren: () =>
              import(
                '../components/home/<USER>'
              ).then((m) => m.HomeModule),
          },
        ],
      },
      {
        path: 'domain-selection',
        children: [
          {
            path: '',
            loadChildren: () =>
              import(
                '../components/domain-selection/domain-selection.module'
              ).then((m) => m.DomainSelectionModule),
          },
        ],
      },
      {
        path: 'document-selection',
        children: [
          {
            path: '',
            loadChildren: () =>
              import(
                '../components/document-selection/document-selection.module'
              ).then((m) => m.DocumentSelectionModule),
          },
        ],
      },
      {
        path: 'question-selection',
        children: [
          {
            path: '',
            loadChildren: () =>
              import(
                '../components/question-selection/question-selection.module'
              ).then((m) => m.QuestionSelectionModule),
          },
        ],
      },
      {
        path: 'automated-control-validation',
        children: [
          {
            path: '',
            loadChildren: () =>
              import(
                '../components/evidence-analyzer/evidence-analyzer.module'
              ).then((m) => m.EvidenceAnalyzerModule),
          },
        ],
      },
      {
        path: 'chatbot',
        children: [
          {
            path: '',
            loadChildren: () =>
              import('../components/chatbot/chatbot.module').then(
                (m) => m.ChatbotModule
              ),
          },
        ],
      },
      {
        path: 'control-result',
        children: [
          {
            path: '',
            loadChildren: () =>
              import('../components/evidence-result/evidence-result.module').then(
                (m) => m.EvidenceResultModule
              ),
          },
        ],
      },
      {
        path: 'all-result',
        children: [
          {
            path: '',
            loadChildren: () =>
              import('../components/evidence-all/evidence-all.module').then(
                (m) => m.EvidenceAllModule
              ),
          },
        ],
      },
      {
        path: 'isecmapper',
        children: [
          {
            path: '',
            loadChildren: () =>
              import('../components/isecmapper-home/isecmapper-home.module').then(
                (m) => m.IsecmapperHomeModule
              ),
          },
        ],
      },
      {
        path: 'pia',
        children: [
          {
            path: '',
            loadChildren: () =>
              import('../components/pia-home/pia-home.module').then(
                (m) => m.PiaHomeModule
              ),
          },
        ],
      },
      {
        path: 'cyber-pioneer',
        children: [
          {
            path: '',
            loadChildren: () =>
              import('../components/pinoeer-home/pinoeer-home.module').then(
                (m) => m.PinoeerHomeModule
              ),
          },
        ],
      },
      {
        path: '**',
        children: [
          {
            path: '',
            loadChildren: () =>
              import(
                '../components/home/<USER>'
              ).then((m) => m.HomeModule),
          },
        ],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LayoutRoutingModule {}
