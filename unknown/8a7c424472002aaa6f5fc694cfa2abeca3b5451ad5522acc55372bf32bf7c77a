import os
import sys
from dotenv import load_dotenv
from openai import AzureOpenAI

# Print current working directory
print(f"Current working directory: {os.getcwd()}")

# Print .env file path
env_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), ".env")
print(f"Env file path: {env_path}")
print(f"Env file exists: {os.path.exists(env_path)}")

# Load environment variables
load_dotenv(
    override=True,
    verbose=True,
    dotenv_path=env_path,
)

# Get environment variables
MSP_AZURE_OPENAI_VERSION = os.getenv("MSP_AZURE_OPENAI_VERSION")
MSP_AZURE_OPENAI_ENDPOINT = os.getenv("MSP_AZURE_OPENAI_ENDPOINT")
MSP_AZURE_OPENAI_KEY = os.getenv("MSP_AZURE_OPENAI_KEY")
MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME = os.getenv("MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME")

# Print all environment variables
print("\nAll environment variables:")
for key, value in os.environ.items():
    if "AZURE" in key or "MSP" in key:
        print(f"{key}: {value}")

# Set environment variable that OpenAI library looks for
os.environ["AZURE_OPENAI_API_KEY"] = MSP_AZURE_OPENAI_KEY

print(f"MSP_AZURE_OPENAI_VERSION: {MSP_AZURE_OPENAI_VERSION}")
print(f"MSP_AZURE_OPENAI_ENDPOINT: {MSP_AZURE_OPENAI_ENDPOINT}")
print(f"MSP_AZURE_OPENAI_KEY: {MSP_AZURE_OPENAI_KEY}")
print(f"MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME: {MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME}")
print(f"AZURE_OPENAI_API_KEY: {os.environ.get('AZURE_OPENAI_API_KEY')}")

try:
    # Initialize Azure OpenAI client
    client = AzureOpenAI(
        api_key=MSP_AZURE_OPENAI_KEY,
        api_version=MSP_AZURE_OPENAI_VERSION,
        azure_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
        azure_deployment=MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME
    )
    print("Azure OpenAI client initialized successfully!")
except Exception as e:
    print(f"Error initializing Azure OpenAI client: {e}")

print("Test completed.")
