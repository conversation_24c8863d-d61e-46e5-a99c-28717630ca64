$fileNames = @(
    "ISO/IEC 27001.pdf",
    "NIST CSF 2.0.pdf",
    "NIST SP 800-53.pdf",
    "CIS Benchmarks.pdf",
    "ISO 22301.pdf",
    "SOC 2 (AICPA).pdf",
    "MITRE <EMAIL>",
    "AWS Security Practice.pdf",
    "Azure Security Practice.pdf",
    "GCP Security Practice.pdf",
    "ISO 42001.pdf",
    "PCI DSS.pdf",
    "FFIEC CAT.pdf",
    "NIS2.pdf",
    "DORA.pdf",
    "HIPAA Security Rule.pdf",
    "HITECH.pdf",
    "ISO/IEC 27799.pdf",
    "NIST SP 800-171.pdf",
    "FedRAMP.pdf",
    "CMMC.pdf",
    "ITAR/EAR.pdf",
    "IEC 62443.pdf",
    "DOE C2M2.pdf",
    "NIST SP 800-82.pdf",
    "ISO/IEC 27011.pdf",
    "ETSI EN 303 645.pdf",
    "ISO/IEC 29100.pdf",
    "NIST Privacy Framework.pdf",
    "ISO/IEC 27017.pdf",
    "ISO/IEC 27018.pdf",
    "CSA STAR.pdf",
    "SOC 2 Type II.pdf",
    "ISO 28000.pdf"
)

foreach ($fileName in $fileNames) {
    # Replace invalid characters in filename
    $safeFileName = $fileName -replace '/', '-' -replace '@', 'at'

    # Create empty PDF file
    $null = New-Item -ItemType File -Path $safeFileName -Force
    Write-Host "Created: $safeFileName (Original: $fileName)"
}

Write-Host "All 34 PDF files have been created."
