import time
import random
from functools import wraps
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def rate_limited_api_call(max_retries=5, initial_delay=1, backoff_factor=2, jitter=0.1):
    """
    Decorator for rate-limited API calls with exponential backoff.
    
    Args:
        max_retries: Maximum number of retries
        initial_delay: Initial delay in seconds
        backoff_factor: Factor to increase delay on each retry
        jitter: Random jitter factor to add to delay
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            delay = initial_delay
            
            while True:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # Check if it's a rate limit error (429)
                    if hasattr(e, 'status_code') and e.status_code == 429:
                        if retries >= max_retries:
                            logger.error(f"Rate limit exceeded after {max_retries} retries. Giving up.")
                            raise
                        
                        # Calculate delay with jitter
                        jitter_amount = random.uniform(-jitter, jitter)
                        actual_delay = delay * (1 + jitter_amount)
                        
                        logger.info(f"Rate limit hit. Retrying in {actual_delay:.2f} seconds (retry {retries+1}/{max_retries})")
                        time.sleep(actual_delay)
                        
                        # Increase delay for next retry
                        delay *= backoff_factor
                        retries += 1
                    else:
                        # If it's not a rate limit error, re-raise
                        raise
        
        return wrapper
    
    return decorator

def add_delay(seconds=1):
    """Simple function to add a delay between API calls"""
    time.sleep(seconds)
