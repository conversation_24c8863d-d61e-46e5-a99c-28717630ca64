<div class="region-selection-container">
  <div class="header">
    <h4>Region-Based Selection</h4>
  </div>
  <mat-divider></mat-divider>

  <div class="selection-form">
    <!-- Region Selection -->
    <div class="form-group">
      <label for="region">Region:</label>
      <mat-form-field appearance="fill" class="full-width">
        <mat-select
          id="region"
          [(ngModel)]="selectedRegion"
          (selectionChange)="onRegionChange()"
          placeholder="Select a region">
          @for (region of regions; track region) {
            <mat-option [value]="region">{{ region }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>

    <!-- Industry Selection -->
    <div class="form-group">
      <label for="industry">Industry:</label>
      <mat-form-field appearance="fill" class="full-width">
        <mat-select
          id="industry"
          [(ngModel)]="selectedIndustry"
          (selectionChange)="onIndustryChange()"
          placeholder="Select an industry"
          [disabled]="!selectedRegion">
          @for (industry of industries; track industry) {
            <mat-option [value]="industry">{{ industry }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>

    <!-- Sector Selection -->
    <div class="form-group">
      <label for="sector">Sector:</label>
      <mat-form-field appearance="fill" class="full-width">
        <mat-select
          id="sector"
          [(ngModel)]="selectedSector"
          (selectionChange)="onSectorChange()"
          placeholder="Select a sector"
          [disabled]="!selectedIndustry">
          @for (sector of sectors; track sector) {
            <mat-option [value]="sector">{{ sector }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <!-- Document List -->
  @if (getDocumentCount() > 0) {
    <div class="document-list">
      <h5>Selected Documents ({{ getDocumentCount() }})</h5>

      @if (documents.Standard.length > 0) {
        <div class="document-category">
          <h6>Standards</h6>
          <ul>
            @for (doc of documents.Standard; track doc) {
              <li>{{ doc }}</li>
            }
          </ul>
        </div>
      }

      @if (documents.Guidelines.length > 0) {
        <div class="document-category">
          <h6>Guidelines</h6>
          <ul>
            @for (doc of documents.Guidelines; track doc) {
              <li>{{ doc }}</li>
            }
          </ul>
        </div>
      }

      @if (documents.Framework.length > 0) {
        <div class="document-category">
          <h6>Frameworks</h6>
          <ul>
            @for (doc of documents.Framework; track doc) {
              <li>{{ doc }}</li>
            }
          </ul>
        </div>
      }

      @if (documents.Regulation.length > 0) {
        <div class="document-category">
          <h6>Regulations</h6>
          <ul>
            @for (doc of documents.Regulation; track doc) {
              <li>{{ doc }}</li>
            }
          </ul>
        </div>
      }
    </div>

    <!-- Document Selection -->
    <div class="document-selection-container">
      <h5>Select Region Documents</h5>

      <div class="selection-actions">
        <button class="btn-select" (click)="selectAllDocuments()">Select All</button>
        <button class="btn-deselect" (click)="deselectAllDocuments()">Deselect All</button>
        <span class="selection-count">Selected: {{ selectedDocuments.length }} of {{ getDocumentCount() }}</span>
      </div>

      <!-- Standards -->
      @if (documents.Standard.length > 0) {
        <div class="document-category-selection">
          <div class="category-header">
            <label>
              <input
                type="checkbox"
                [checked]="isCategorySelected('Standard')"
                (change)="toggleCategory('Standard')"
              >
              <span>Standards ({{ documents.Standard.length }})</span>
            </label>
          </div>
          <div class="document-items">
            @for (doc of documents.Standard; track doc) {
              <div class="document-item">
                <label>
                  <input
                    type="checkbox"
                    [checked]="isDocumentSelected(doc)"
                    (change)="toggleDocument(doc)"
                  >
                  <span>{{ doc }}</span>
                </label>
              </div>
            }
          </div>
        </div>
      }

      <!-- Guidelines -->
      @if (documents.Guidelines.length > 0) {
        <div class="document-category-selection">
          <div class="category-header">
            <label>
              <input
                type="checkbox"
                [checked]="isCategorySelected('Guidelines')"
                (change)="toggleCategory('Guidelines')"
              >
              <span>Guidelines ({{ documents.Guidelines.length }})</span>
            </label>
          </div>
          <div class="document-items">
            @for (doc of documents.Guidelines; track doc) {
              <div class="document-item">
                <label>
                  <input
                    type="checkbox"
                    [checked]="isDocumentSelected(doc)"
                    (change)="toggleDocument(doc)"
                  >
                  <span>{{ doc }}</span>
                </label>
              </div>
            }
          </div>
        </div>
      }

      <!-- Frameworks -->
      @if (documents.Framework.length > 0) {
        <div class="document-category-selection">
          <div class="category-header">
            <label>
              <input
                type="checkbox"
                [checked]="isCategorySelected('Framework')"
                (change)="toggleCategory('Framework')"
              >
              <span>Frameworks ({{ documents.Framework.length }})</span>
            </label>
          </div>
          <div class="document-items">
            @for (doc of documents.Framework; track doc) {
              <div class="document-item">
                <label>
                  <input
                    type="checkbox"
                    [checked]="isDocumentSelected(doc)"
                    (change)="toggleDocument(doc)"
                  >
                  <span>{{ doc }}</span>
                </label>
              </div>
            }
          </div>
        </div>
      }

      <!-- Regulations -->
      @if (documents.Regulation.length > 0) {
        <div class="document-category-selection">
          <div class="category-header">
            <label>
              <input
                type="checkbox"
                [checked]="isCategorySelected('Regulation')"
                (change)="toggleCategory('Regulation')"
              >
              <span>Regulations ({{ documents.Regulation.length }})</span>
            </label>
          </div>
          <div class="document-items">
            @for (doc of documents.Regulation; track doc) {
              <div class="document-item">
                <label>
                  <input
                    type="checkbox"
                    [checked]="isDocumentSelected(doc)"
                    (change)="toggleDocument(doc)"
                  >
                  <span>{{ doc }}</span>
                </label>
              </div>
            }
          </div>
        </div>
      }
    </div>
  }

  @if (loading) {
    <div class="loading-indicator">
      <p>Loading...</p>
    </div>
  }
</div>
