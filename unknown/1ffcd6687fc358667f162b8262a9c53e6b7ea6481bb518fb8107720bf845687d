# Base image
FROM python:3.12-slim

# update image
RUN apt-get update && apt-get upgrade -y

# Set working directory
WORKDIR /home

# Copy the requirements file
COPY ./requirements_lock.txt /home/<USER>

# Install dependencies
RUN pip install -r /home/<USER>

# clean the install.
RUN apt-get autoremove -y && apt-get clean -y && rm -rf /var/lib/apt/lists/*

# Copy the application code
COPY ./app /home/<USER>

# Create non-root user with no password
RUN adduser --disabled-password --gecos '' appuser

# Change the ownership of /home directory to the new user
RUN chown -R appuser:appuser /home

# Switch to the new user
USER appuser

# Expose the port the FastAPI application will run on
EXPOSE 8000

# Start the FastAPI application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "8"]