import { NgModule, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MaterialModule } from '../../material/material.module';
import { IsecmapperHomeComponent } from './components/isecmapper-home.component';
import { IsecmapperHomeRoutingModule } from './isecmapper-home-routing.module';

@NgModule({
  declarations: [],
  providers: [],
  imports: [
    CommonModule,
    MaterialModule,
    IsecmapperHomeRoutingModule,
    IsecmapperHomeComponent,
  ],
})
export class IsecmapperHomeModule implements OnInit {

  ngOnInit(): void {
    console.log('IsecmapperHomeModule initialized');
  }
}
