import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDividerModule } from '@angular/material/divider';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../../environments/environment';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-region-selection',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDividerModule
  ],
  templateUrl: './region-selection.component.html',
  styleUrls: ['./region-selection.component.css']
})
export class RegionSelectionComponent implements OnInit {
  @Output() selectionChanged = new EventEmitter<any>();

  regions: string[] = [];
  industries: string[] = [];
  sectors: string[] = [];

  selectedRegion: string = '';
  selectedIndustry: string = '';
  selectedSector: string = '';

  industriesByRegion: any = {};
  sectorsByIndustry: any = {};

  documents: any = {
    Standard: [],
    Guidelines: [],
    Framework: [],
    Regulation: []
  };

  // Document selection tracking
  selectedDocuments: string[] = [];
  documentSelections: { [key: string]: boolean } = {};

  loading: boolean = false;

  constructor(
    private httpClient: HttpClient,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadRegionOptions();
  }

  loadRegionOptions(): void {
    this.loading = true;
    this.httpClient.get(`${environment.apiBaseUrl}/region_selection`)
      .subscribe(
        (response: any) => {
          this.regions = response.regions;
          this.industriesByRegion = response.industries_by_region;
          this.sectorsByIndustry = response.sectors_by_industry;
          this.loading = false;
        },
        (error) => {
          console.error('Error loading region options:', error);
          this.snackBar.open('Error loading region options. Please try again.', 'Close', {
            duration: 5000
          });
          this.loading = false;
        }
      );
  }

  onRegionChange(): void {
    // Reset industry and sector selections
    this.selectedIndustry = '';
    this.selectedSector = '';
    this.documents = {
      Standard: [],
      Guidelines: [],
      Framework: [],
      Regulation: []
    };

    // Update available industries for the selected region
    this.industries = this.industriesByRegion[this.selectedRegion] || [];

    this.updateSelection();
  }

  onIndustryChange(): void {
    // Reset sector selection
    this.selectedSector = '';
    this.documents = {
      Standard: [],
      Guidelines: [],
      Framework: [],
      Regulation: []
    };

    // Update available sectors for the selected industry
    const key = `${this.selectedRegion}_${this.selectedIndustry}`;
    this.sectors = this.sectorsByIndustry[key] || [];

    this.updateSelection();
  }

  onSectorChange(): void {
    this.loadDocuments();
    this.updateSelection();
  }

  loadDocuments(): void {
    if (!this.selectedRegion || !this.selectedIndustry || !this.selectedSector) {
      return;
    }

    this.loading = true;
    this.httpClient.get(`${environment.apiBaseUrl}/documents_for_selection`, {
      params: {
        region: this.selectedRegion,
        industry: this.selectedIndustry,
        sector: this.selectedSector
      }
    }).subscribe(
      (response: any) => {
        this.documents = response;
        this.initializeDocumentSelections();
        this.loading = false;
      },
      (error) => {
        console.error('Error loading documents:', error);
        this.snackBar.open('Error loading documents. Please try again.', 'Close', {
          duration: 5000
        });
        this.loading = false;
      }
    );
  }

  // Document selection methods
  initializeDocumentSelections(): void {
    this.documentSelections = {};
    this.selectedDocuments = [];

    // Select all documents by default
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.documentSelections[doc] = true;
        this.selectedDocuments.push(doc);
      }
    }

    // Update the selection
    this.updateSelection();
  }

  isDocumentSelected(doc: string): boolean {
    return this.documentSelections[doc] === true;
  }

  toggleDocument(doc: string): void {
    this.documentSelections[doc] = !this.documentSelections[doc];
    this.updateSelectedDocuments();
  }

  isCategorySelected(category: string): boolean {
    if (!this.documents[category] || this.documents[category].length === 0) {
      return false;
    }

    return this.documents[category].every((doc: string) =>
      this.documentSelections[doc] === true
    );
  }

  toggleCategory(category: string): void {
    if (!this.documents[category]) {
      return;
    }

    const newState = !this.isCategorySelected(category);

    for (const doc of this.documents[category]) {
      this.documentSelections[doc] = newState;
    }

    this.updateSelectedDocuments();
  }

  selectAllDocuments(): void {
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.documentSelections[doc] = true;
      }
    }

    this.updateSelectedDocuments();
  }

  deselectAllDocuments(): void {
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.documentSelections[doc] = false;
      }
    }

    this.updateSelectedDocuments();
  }

  updateSelectedDocuments(): void {
    this.selectedDocuments = [];

    for (const doc in this.documentSelections) {
      if (this.documentSelections[doc]) {
        this.selectedDocuments.push(doc);
      }
    }

    // Update the selection
    this.updateSelection();
  }

  updateSelection(): void {
    const selection = {
      region: this.selectedRegion,
      industry: this.selectedIndustry,
      sector: this.selectedSector,
      documents: this.documents,
      selectedDocuments: this.selectedDocuments
    };

    this.selectionChanged.emit(selection);
  }

  getDocumentCount(): number {
    return this.documents.Standard.length +
           this.documents.Guidelines.length +
           this.documents.Framework.length +
           this.documents.Regulation.length;
  }
}
