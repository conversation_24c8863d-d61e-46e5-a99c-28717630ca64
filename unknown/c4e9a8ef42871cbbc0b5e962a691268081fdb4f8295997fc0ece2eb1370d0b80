map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;
    text/css                   max;
    application/json           max;
    application/javascript     max;
    ~image/                    max;
}
server {
  listen 80;
  location / {
      root /usr/share/nginx/html;
      index index.html index.htm;
      try_files $uri $uri/ /index.html = 404;
    }
  expires $expires;
  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
  gzip_comp_level 9;
}
