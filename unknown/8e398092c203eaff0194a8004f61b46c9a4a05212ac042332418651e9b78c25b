import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SidebarViewService } from '../../services/sidebar.service';
import { MaterialModule } from 'src/app/modules/material/material.module';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [MaterialModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css',
})
export class HeaderComponent implements OnInit {
  showLogo: boolean = true;
  color: string = 'yellow';

  constructor(
    private sidebarService: SidebarViewService,
    private router: Router
  ) { }

  ngOnInit(): void { }

  toggle() {
    this.sidebarService.toggle();
  }
}
