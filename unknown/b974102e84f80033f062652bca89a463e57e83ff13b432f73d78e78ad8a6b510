import os
from openai import AzureOpenAI
from dotenv import load_dotenv

# Load environment variables
env_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), ".env")
print(f"Loading .env file from: {env_path}")
print(f"File exists: {os.path.exists(env_path)}")

load_dotenv(
    override=True,
    verbose=True,
    dotenv_path=env_path,
)

# Get environment variables
MSP_AZURE_OPENAI_VERSION = os.getenv("MSP_AZURE_OPENAI_VERSION")
MSP_AZURE_OPENAI_ENDPOINT = os.getenv("MSP_AZURE_OPENAI_ENDPOINT")
MSP_AZURE_OPENAI_KEY = os.getenv("MSP_AZURE_OPENAI_KEY")
MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME = os.getenv("MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME")

print(f"\nAzure OpenAI Configuration:")
print(f"MSP_AZURE_OPENAI_VERSION: {MSP_AZURE_OPENAI_VERSION}")
print(f"MSP_AZURE_OPENAI_ENDPOINT: {MSP_AZURE_OPENAI_ENDPOINT}")
print(f"MSP_AZURE_OPENAI_KEY: {MSP_AZURE_OPENAI_KEY}")

# First, let's use the embedding model which we know works
client = AzureOpenAI(
    api_key=MSP_AZURE_OPENAI_KEY,
    api_version=MSP_AZURE_OPENAI_VERSION,
    azure_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
    azure_deployment=MSP_AZURE_EMBEDDING_DEPLOYMENT_NAME
)

# Get a list of all models
try:
    models = client.models.list()
    print("\nAvailable models:")
    model_ids = []
    for model in models:
        print(f" - {model.id}")
        model_ids.append(model.id)
    
    # Now try to use each model as a deployment
    print("\nTesting each model as a deployment:")
    for model_id in model_ids:
        if "gpt" in model_id.lower() and not "instruct" in model_id.lower():
            print(f"\nTesting model as deployment: {model_id}")
            try:
                test_client = AzureOpenAI(
                    api_key=MSP_AZURE_OPENAI_KEY,
                    api_version=MSP_AZURE_OPENAI_VERSION,
                    azure_endpoint=MSP_AZURE_OPENAI_ENDPOINT,
                    azure_deployment=model_id
                )
                
                # Test completion
                response = test_client.chat.completions.create(
                    model=model_id,
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant."},
                        {"role": "user", "content": "Hello, how are you?"}
                    ],
                    max_tokens=10
                )
                print(f"Response: {response.choices[0].message.content}")
                print(f"Model {model_id} works as a deployment!")
            except Exception as e:
                print(f"Error using model {model_id} as deployment: {e}")
except Exception as e:
    print(f"Error listing models: {e}")

print("\nScript completed.")
