# Define a function to create a PDF with content
function Create-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$KeyPoints
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1500 >>
stream
BT
/F1 24 Tf
100 700 Td
($Title) Tj
/F2 12 Tf
0 -40 Td
($Description) Tj
0 -30 Td
/F1 14 Tf
(Key Components:) Tj
/F2 12 Tf
0 -20 Td
($KeyPoints) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1925
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created PDF with content: $safeFileName.pdf"
}

# 11. ISO 42001
Create-PDF -FileName "ISO 42001" `
    -Title "ISO 42001" `
    -Description "ISO 42001 is an international standard for artificial intelligence management systems. It provides a framework for organizations to develop, implement, and maintain responsible AI systems." `
    -KeyPoints "• AI Governance
• Risk Management
• Transparency and Explainability
• Fairness and Non-discrimination
• Safety and Security
• Privacy and Data Protection
• Accountability
• Human Oversight
• Continuous Monitoring and Improvement
• Ethical Considerations"

# 12. PCI DSS
Create-PDF -FileName "PCI DSS" `
    -Title "PCI DSS" `
    -Description "The Payment Card Industry Data Security Standard (PCI DSS) is a set of security standards designed to ensure that all companies that accept, process, store or transmit credit card information maintain a secure environment." `
    -KeyPoints "• Build and Maintain a Secure Network and Systems
• Protect Cardholder Data
• Maintain a Vulnerability Management Program
• Implement Strong Access Control Measures
• Regularly Monitor and Test Networks
• Maintain an Information Security Policy
• Encryption of Cardholder Data
• Secure Systems and Applications
• Restrict Physical Access
• Track and Monitor Access
• Regular Testing of Security Systems and Processes"

# 13. FFIEC CAT
Create-PDF -FileName "FFIEC CAT" `
    -Title "FFIEC CAT" `
    -Description "The Federal Financial Institutions Examination Council (FFIEC) Cybersecurity Assessment Tool (CAT) is designed to help financial institutions identify their risks and determine their cybersecurity preparedness." `
    -KeyPoints "• Cyber Risk Management and Oversight
• Threat Intelligence and Collaboration
• Cybersecurity Controls
• External Dependency Management
• Cyber Incident Management and Resilience
• Inherent Risk Profile Assessment
• Cybersecurity Maturity Assessment
• Risk Management
• Governance
• Resources
• Training and Culture"

# 14. NIS2
Create-PDF -FileName "NIS2" `
    -Title "NIS2" `
    -Description "The Network and Information Systems Directive 2 (NIS2) is a European Union directive that aims to achieve a high common level of cybersecurity across the EU by improving the resilience and incident response capabilities of both public and private entities." `
    -KeyPoints "• Risk Management Measures
• Incident Handling
• Business Continuity
• Supply Chain Security
• Network and Information Systems Security
• Policies and Procedures
• Crisis Management
• Use of Cryptography and Encryption
• Security in Acquisition, Development and Maintenance
• Reporting Obligations
• Cooperation and Information Exchange
• Compliance and Enforcement"

# 15. DORA
Create-PDF -FileName "DORA" `
    -Title "DORA" `
    -Description "The Digital Operational Resilience Act (DORA) is a European Union regulation that aims to ensure that the financial sector in Europe can maintain resilient operations through a severe operational disruption." `
    -KeyPoints "• ICT Risk Management
• ICT-Related Incident Reporting
• Digital Operational Resilience Testing
• ICT Third-Party Risk Management
• Information Sharing Arrangements
• Threat-Led Penetration Testing
• ICT Risk Management Framework
• Business Continuity Planning
• Incident Response and Recovery
• Communication During Incidents
• Oversight Framework for Critical ICT Third-Party Service Providers"

Write-Host "Created third batch of PDFs (11-15)"
