import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Observable, forkJoin, of } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { GlobalDataService } from './global-data.service';

@Injectable({
  providedIn: 'root'
})
export class BatchProcessorService {
  // Event emitter for batch progress updates
  public batchProgress = new EventEmitter<{current: number, total: number}>();

  constructor(
    private httpClient: HttpClient,
    private globalDataService: GlobalDataService
  ) {}

  /**
   * Process a large Excel file in batches
   * @param files The files to upload (Excel + evidence files)
   * @param batchSize The number of rows to process in each batch
   * @returns Observable with the combined results
   */
  processBatches(files: File[], batchSize: number = 5): Observable<any> {
    // Create a FormData object with all files
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file, file.name);
    });

    // Process the first batch to get total row count
    return this.httpClient.post(environment.apiBaseUrl + '/analyze_evidences_batch', formData, {
      params: {
        start_row: '0',
        end_row: batchSize.toString()
      }
    }).pipe(
      map((firstBatchResults: any) => {
        // Store the first batch results
        let allResults = [...firstBatchResults];

        // Estimate total rows based on the Excel file (this is an approximation)
        // In a real implementation, you might want to add an endpoint to get the exact row count
        const estimatedTotalRows = 50; // This is a placeholder - adjust based on your needs

        // Calculate remaining batches
        const remainingBatches = Math.ceil((estimatedTotalRows - batchSize) / batchSize);

        if (remainingBatches <= 0) {
          // If only one batch was needed, return the results
          this.globalDataService.setAnalysisData(allResults);
          return allResults;
        }

        // Create observables for each remaining batch
        const batchObservables = [];

        for (let i = 1; i <= remainingBatches; i++) {
          const startRow = i * batchSize;
          const endRow = (i + 1) * batchSize;

          // Create a new FormData for each batch (files need to be re-added)
          const batchFormData = new FormData();
          files.forEach(file => {
            batchFormData.append('files', file, file.name);
          });

          // Add this batch request to our array of observables
          batchObservables.push(
            this.httpClient.post(environment.apiBaseUrl + '/analyze_evidences_batch', batchFormData, {
              params: {
                start_row: startRow.toString(),
                end_row: endRow.toString()
              }
            }).pipe(
              catchError(error => {
                console.error(`Error processing batch ${i}:`, error);
                return of([]); // Return empty array on error
              })
            )
          );
        }

        // Process batches sequentially to avoid overwhelming the API
        if (batchObservables.length > 0) {
          // Emit the total number of batches
          this.batchProgress.emit({ current: 0, total: batchObservables.length + 1 }); // +1 for the first batch

          // Create a function to process batches one at a time
          const processSequentially = (index: number, results: any[]): Observable<any[]> => {
            if (index >= batchObservables.length) {
              // All batches processed, return final results
              this.globalDataService.setAnalysisData(results);
              // Emit final progress
              this.batchProgress.emit({ current: batchObservables.length + 1, total: batchObservables.length + 1 });
              return of(results);
            }

            // Emit current progress
            this.batchProgress.emit({ current: index + 1, total: batchObservables.length + 1 });

            // Process the current batch
            return batchObservables[index].pipe(
              map(batchResult => {
                // Add batch results to the accumulated results
                if (batchResult && batchResult.length > 0) {
                  results = [...results, ...batchResult];
                }

                // Update progress (optional)
                const progress = Math.round(((index + 1) / batchObservables.length) * 100);
                console.log(`Batch ${index + 1}/${batchObservables.length} complete (${progress}%)`);

                // Add a small delay before processing the next batch to avoid overwhelming the API
                return new Observable<any[]>(observer => {
                  setTimeout(() => {
                    observer.next(processSequentially(index + 1, results));
                    observer.complete();
                  }, 1000); // 1 second delay between batches
                });
              }),
              // Flatten the observable
              mergeMap(obs => obs)
            );
          };

          // Start processing from the first batch
          return processSequentially(0, allResults);
        } else {
          return of(allResults);
        }
      })
    );
  }
}
