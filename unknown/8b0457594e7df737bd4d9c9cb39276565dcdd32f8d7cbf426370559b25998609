from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.routes import routers as v1_routers
from app.core.config import configs


class AppCreator:
    def __init__(self):
        # set app default
        self.app = FastAPI(
            title=configs.PROJECT_NAME,
            openapi_url=f"{configs.API}/openapi.json",
            version=configs.PROJECT_VERSION,
        )

        self.app.include_router(v1_routers, prefix=configs.API_V1_STR)

        if configs.BACKEND_CORS_ORIGINS:
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=[str(origin) for origin in configs.BACKEND_CORS_ORIGINS],
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
                expose_headers=["Content-Disposition"],
            )


app_creator = AppCreator()
app = app_creator.app


# Display root startup message
@app.get("/")
async def startup():
    message = "Welcome to IsecMapper API, check documentation at /docs or /redoc"
    return message
