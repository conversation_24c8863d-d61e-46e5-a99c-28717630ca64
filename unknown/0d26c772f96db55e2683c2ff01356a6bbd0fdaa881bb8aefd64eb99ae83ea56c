# Function to create a better formatted PDF
function Create-Better-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$Overview,
        [string]$KeyComponents,
        [string]$Implementation,
        [string]$Benefits
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    # Create a more complex PDF with better formatting
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R /F3 6 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 7 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Oblique
>>
endobj
7 0 obj
<< /Length 4000 >>
stream
BT
/F1 28 Tf
50 750 Td
($Title) Tj

/F2 12 Tf
0 -30 Td
(Document Version: 1.0) Tj
0 -15 Td
(Last Updated: April 2025) Tj

/F1 18 Tf
-10 -40 Td
(Description) Tj
/F2 12 Tf
10 -20 Td
($Description) Tj

/F1 18 Tf
-10 -30 Td
(Overview) Tj
/F2 12 Tf
10 -20 Td
($Overview) Tj

/F1 18 Tf
-10 -30 Td
(Key Components) Tj
/F2 12 Tf
10 -20 Td
($KeyComponents) Tj

/F1 18 Tf
-10 -30 Td
(Implementation Guidelines) Tj
/F2 12 Tf
10 -20 Td
($Implementation) Tj

/F1 18 Tf
-10 -30 Td
(Benefits) Tj
/F2 12 Tf
10 -20 Td
($Benefits) Tj

/F3 10 Tf
-20 -40 Td
(This document provides an overview of $Title. For complete and authoritative information,) Tj
0 -15 Td
(please refer to the official documentation and standards publications.) Tj
ET
endstream
endobj
xref
0 8
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
0000000444 00000 n
trailer
<< /Size 8
/Root 1 0 R
>>
startxref
4496
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created improved PDF: $safeFileName.pdf"
}

# 1. ISO/IEC 27001
Create-Better-PDF -FileName "ISO/IEC 27001" `
    -Title "ISO/IEC 27001" `
    -Description "ISO/IEC 27001 is an international standard for information security management systems (ISMS). It specifies the requirements for establishing, implementing, maintaining, and continually improving an information security management system within the context of the organization's overall business risks." `
    -Overview "ISO/IEC 27001 was developed by the International Organization for Standardization (ISO) and the International Electrotechnical Commission (IEC). The current version was published in 2013, with minor updates in 2017 and 2022. It provides a systematic approach to managing sensitive company information, including financial data, intellectual property, employee details, and information entrusted by third parties. The standard is designed to be flexible enough to be applied to organizations of all types and sizes." `
    -KeyComponents "• Information Security Policies: Documented policies for information security
• Organization of Information Security: Assignment of security responsibilities
• Human Resource Security: Security aspects for employees joining, moving, and leaving
• Asset Management: Identification of information assets and defining protection responsibilities
• Access Control: Restriction of access rights to networks, systems, applications, functions, and data
• Cryptography: Encryption and key management
• Physical and Environmental Security: Protection of computer facilities
• Operations Security: Procedures and responsibilities for managing and operating information processing facilities
• Communications Security: Network security management and information transfer
• System Acquisition, Development and Maintenance: Security requirements of information systems
• Supplier Relationships: Information security in supplier relationships
• Information Security Incident Management: Reporting and management of security incidents
• Business Continuity Management: Protecting, maintaining and recovering business-critical processes and systems
• Compliance: Compliance with legal and contractual requirements" `
    -Implementation "Implementing ISO/IEC 27001 typically involves the following steps:

1. Define the scope of the ISMS
2. Develop an information security policy
3. Define a risk assessment methodology
4. Conduct a risk assessment
5. Manage identified risks
6. Select control objectives and controls to be implemented
7. Prepare a Statement of Applicability
8. Implement the selected controls
9. Measure the effectiveness of controls
10. Conduct internal ISMS audits
11. Management review of the ISMS
12. Implement corrective actions
13. Seek certification from an accredited certification body" `
    -Benefits "• Enhanced information security posture
• Structured approach to managing information security risks
• Increased reliability and security of systems and information
• Improved customer and business partner confidence
• Competitive advantage through demonstrated commitment to information security
• Legal and regulatory compliance
• Better integration of information security with business objectives
• Reduced costs associated with security incidents
• Improved organizational culture and security awareness
• Systematic approach to risk management"

# 2. NIST CSF 2.0
Create-Better-PDF -FileName "NIST CSF 2.0" `
    -Title "NIST Cybersecurity Framework 2.0" `
    -Description "The NIST Cybersecurity Framework (CSF) 2.0 is a voluntary framework consisting of standards, guidelines, and best practices to manage cybersecurity risk. Developed by the National Institute of Standards and Technology, it provides organizations with a flexible and cost-effective approach to enhancing cybersecurity and managing risk." `
    -Overview "The NIST CSF was initially developed in response to Executive Order 13636, 'Improving Critical Infrastructure Cybersecurity,' issued in February 2013. Version 2.0, released in 2023, represents a significant update that incorporates feedback from users, evolving threats, and new technologies. The framework is designed to be adaptable to organizations of all sizes, across all sectors, and can complement existing cybersecurity programs or serve as a foundation for new ones." `
    -KeyComponents "• Core Functions:
  - IDENTIFY: Develop organizational understanding to manage cybersecurity risk
  - PROTECT: Develop and implement appropriate safeguards to ensure delivery of critical services
  - DETECT: Develop and implement appropriate activities to identify the occurrence of a cybersecurity event
  - RESPOND: Develop and implement appropriate activities to take action regarding a detected cybersecurity incident
  - RECOVER: Develop and implement appropriate activities to maintain plans for resilience and to restore any capabilities or services impaired due to a cybersecurity incident

• Implementation Tiers:
  - Tier 1: Partial
  - Tier 2: Risk Informed
  - Tier 3: Repeatable
  - Tier 4: Adaptive

• Profiles:
  - Current Profile: The current state of the organization's cybersecurity activities
  - Target Profile: The desired state of cybersecurity activities" `
    -Implementation "Implementing the NIST CSF typically involves the following steps:

1. Prioritize and Scope: Identify business objectives and organizational priorities
2. Orient: Identify related systems and assets, regulatory requirements, and overall risk approach
3. Create a Current Profile: Develop a Current Profile by indicating which Category and Subcategory outcomes are currently being achieved
4. Conduct a Risk Assessment: Analyze the operational environment to discern the likelihood of a cybersecurity event and the impact it would have on the organization
5. Create a Target Profile: Create a Target Profile focused on the assessment of the Framework Categories and Subcategories describing the organization's desired cybersecurity outcomes
6. Determine, Analyze, and Prioritize Gaps: Compare the Current Profile and the Target Profile to determine gaps
7. Implement Action Plan: Develop an action plan to address the gaps and adjust current cybersecurity practices to achieve the Target Profile" `
    -Benefits "• Common language for understanding, managing, and expressing cybersecurity risk
• Identification of security gaps and opportunities for improvement
• Effective communication of cybersecurity requirements to stakeholders
• Helps organizations prioritize investments in cybersecurity
• Alignment with industry standards and best practices
• Scalable approach that works for organizations of all sizes
• Facilitates regulatory compliance
• Enhances organizational resilience
• Supports continuous improvement in cybersecurity posture
• Enables better integration of cybersecurity risk into overall enterprise risk management"

# 3. NIST SP 800-53
Create-Better-PDF -FileName "NIST SP 800-53" `
    -Title "NIST Special Publication 800-53" `
    -Description "NIST Special Publication 800-53 provides a catalog of security and privacy controls for federal information systems and organizations. It offers guidelines for selecting and specifying security controls for systems supporting the executive agencies of the federal government." `
    -Overview "NIST SP 800-53, 'Security and Privacy Controls for Federal Information Systems and Organizations,' is developed by the National Institute of Standards and Technology. The publication is regularly updated to address emerging threats and technological changes. Revision 5, released in 2020, represents a significant update that integrates privacy controls with security controls, emphasizes secure system design, and addresses new threats including supply chain risks and cyber-physical systems." `
    -KeyComponents "• Control Families:
  - AC: Access Control
  - AT: Awareness and Training
  - AU: Audit and Accountability
  - CA: Assessment, Authorization, and Monitoring
  - CM: Configuration Management
  - CP: Contingency Planning
  - IA: Identification and Authentication
  - IR: Incident Response
  - MA: Maintenance
  - MP: Media Protection
  - PE: Physical and Environmental Protection
  - PL: Planning
  - PM: Program Management
  - PS: Personnel Security
  - RA: Risk Assessment
  - SA: System and Services Acquisition
  - SC: System and Communications Protection
  - SI: System and Information Integrity

• Control Baselines:
  - Low-Impact Systems
  - Moderate-Impact Systems
  - High-Impact Systems

• Control Enhancements: Extensions of basic controls that provide additional functionality or address specific threats" `
    -Implementation "Implementing NIST SP 800-53 controls typically involves the following steps:

1. Categorize the information system based on impact levels (FIPS 199)
2. Select the appropriate security control baseline based on the categorization
3. Implement security controls and document how the controls are deployed
4. Assess the security controls to determine if they are implemented correctly
5. Authorize the information system for operation based on a risk determination
6. Monitor the security controls on an ongoing basis, including:
   - Assessing control effectiveness
   - Documenting changes to the system or environment
   - Conducting security impact analyses of the changes
   - Reporting the security state of the system to appropriate officials" `
    -Benefits "• Comprehensive security and privacy control catalog
• Flexible framework that can be tailored to specific organizational needs
• Supports compliance with federal requirements
• Promotes integration of security and privacy
• Facilitates continuous monitoring and assessment
• Addresses emerging threats and technologies
• Provides a structured approach to security implementation
• Enables consistent security implementation across systems
• Supports risk management processes
• Facilitates communication about security requirements"

Write-Host "Created first batch of improved PDFs (1-3)"
