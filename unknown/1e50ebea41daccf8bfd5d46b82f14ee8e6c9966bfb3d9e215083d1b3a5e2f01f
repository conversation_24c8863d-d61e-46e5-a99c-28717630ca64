import { NgModule, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MaterialModule } from '../../material/material.module';
import { EvidenceResultComponent } from './components/evidence-result.component';
import { EvidenceResultRoutingModule } from './evidence-result-routing.module';

@NgModule({
  declarations: [],
  providers: [],
  imports: [
    CommonModule,
    MaterialModule,
    EvidenceResultRoutingModule,
    EvidenceResultComponent,
  ],
})
export class EvidenceResultModule implements OnInit {

  ngOnInit(): void {
    console.log('EvidenceResultModule initialized');
  }
}
