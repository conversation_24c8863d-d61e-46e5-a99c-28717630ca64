import os
import requests
from dotenv import load_dotenv

# Load environment variables
env_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), ".env")
print(f"Loading .env file from: {env_path}")
print(f"File exists: {os.path.exists(env_path)}")

load_dotenv(
    override=True,
    verbose=True,
    dotenv_path=env_path,
)

# Get environment variables
MSP_AZURE_OPENAI_VERSION = os.getenv("MSP_AZURE_OPENAI_VERSION")
MSP_AZURE_OPENAI_ENDPOINT = os.getenv("MSP_AZURE_OPENAI_ENDPOINT")
MSP_AZURE_OPENAI_KEY = os.getenv("MSP_AZURE_OPENAI_KEY")

print(f"\nAzure OpenAI Configuration:")
print(f"MSP_AZURE_OPENAI_VERSION: {MSP_AZURE_OPENAI_VERSION}")
print(f"MSP_AZURE_OPENAI_ENDPOINT: {MSP_AZURE_OPENAI_ENDPOINT}")
print(f"MSP_AZURE_OPENAI_KEY: {MSP_AZURE_OPENAI_KEY}")

# List deployments
url = f"{MSP_AZURE_OPENAI_ENDPOINT}/openai/deployments?api-version={MSP_AZURE_OPENAI_VERSION}"
headers = {
    "api-key": MSP_AZURE_OPENAI_KEY
}

try:
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    deployments = response.json()
    
    print("\nAvailable deployments:")
    for deployment in deployments.get("data", []):
        print(f"ID: {deployment.get('id')}, Model: {deployment.get('model')}")
    
except Exception as e:
    print(f"Error listing deployments: {e}")

print("\nScript completed.")
