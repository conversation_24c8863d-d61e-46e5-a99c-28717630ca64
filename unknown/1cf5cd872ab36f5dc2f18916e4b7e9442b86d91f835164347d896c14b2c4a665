mat-sidenav {
  width: 200px;
  background-color: #252531;
  overflow: hidden;
  /* box-shadow: -5px 5px 10px rgba(229, 233, 240, 0.2), 5px -5px 10px rgba(229, 233, 240, 0.2), -5px -5px 10px rgba(255, 255, 255, 0.9), 5px 5px 13px rgba(229, 233, 240, 0.9), inset 1px 1px 2px rgba(255, 255, 255, 0.3), inset -1px -1px 2px rgba(229, 233, 240, 0.5); */
  border-bottom-right-radius: 10px;
  border: 0px;
}

mat-sidenav-content {
  /* // height: calc(100vh - 103px);//calc(100vh - 104px); */
  height: calc(100vh - 65px);
  overflow: auto;
  background-color: #1b1b27;
}

.active-link {
  font-weight: bold;
  color: yellow !important;
  border-bottom-color: yellow;
  border-left: 6px solid yellow;
  background-color: #1f1f2d;
}

.external-link{
  color:white;
  text-decoration: none;
}

.external-link::after,
.external-link::after {
  content: "";
  width: 11px;
  height: 11px;
  margin-left: 4px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='currentColor' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-1 0V14.5a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h6.636a.5.5 0 0 0 .5-.5z'/%3E%3Cpath fill-rule='evenodd' d='M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0v-5z'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  display: inline-block;
}
