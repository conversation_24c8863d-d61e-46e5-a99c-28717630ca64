<div class="main-container">
  <div class="container p-4">
    <h2>IsecMapper - Domain Selection</h2>
  </div>

  <div class="container">
    <div class="row justify-content-md-center align-items-center">
      <div class="col-md-10 mx-2 justify-content-md-center backdrop">
        <div class="header">
          <h4>Select Industry Domains</h4>
        </div>
        <mat-divider></mat-divider>
        <div class="body py-4">
          <p class="description">Select one or more industry domains relevant to your organization:</p>
          
          <div class="domain-grid">
            @for (domain of domains; track domain.id) {
              <div class="domain-item">
                <mat-checkbox 
                  [checked]="domain.selected" 
                  (change)="toggleDomainSelection(domain)"
                  color="primary">
                  {{ domain.name }}
                </mat-checkbox>
              </div>
            }
          </div>
        </div>
      </div>
    </div>

    <div class="row justify-content-md-center align-items-center mt-4">
      <div class="col-md-10 mx-2 justify-content-md-center backdrop">
        <div class="header">
          <h4>Select Benchmarks</h4>
        </div>
        <mat-divider></mat-divider>
        <div class="body py-4">
          <p class="description">Select one or more benchmarks to evaluate against:</p>
          
          <div class="benchmark-grid">
            @for (benchmark of availableBenchmarks; track benchmark.id) {
              <div class="benchmark-item">
                <mat-checkbox 
                  [checked]="benchmark.selected" 
                  (change)="toggleBenchmarkSelection(benchmark)"
                  color="primary">
                  {{ benchmark.name }}
                </mat-checkbox>
              </div>
            }
          </div>
        </div>
      </div>
    </div>

    <div class="row justify-content-md-center align-items-center mt-4">
      <div class="col-md-10 mx-2 justify-content-md-center">
        <div class="d-flex justify-content-end">
          <button mat-button-raised class="btn btn-primary" (click)="navigateToQuestionSelection()" [disabled]="selectedBenchmarks.length === 0">
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
