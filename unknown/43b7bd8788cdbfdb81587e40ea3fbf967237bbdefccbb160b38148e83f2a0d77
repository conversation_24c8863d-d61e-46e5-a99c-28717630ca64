.main-container {
  padding: 0;
  background-size: cover;
  min-height: 100vh;
}

.backdrop {
  padding: 10px;
  border-radius: 10px;
  background: #252531;
}

.header {
  margin-bottom: 10px;
}

.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.selected-item {
  background-color: #333340;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  color: #fff;
}

.option-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.option-card {
  background-color: #333340;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.option-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.option-card.selected {
  border: 2px solid yellow;
}

.option-header {
  background-color: #444450;
  padding: 15px;
  border-bottom: 1px solid #555;
}

.option-header h5 {
  margin: 0;
  color: #fff;
}

.option-body {
  padding: 15px;
  color: #ccc;
}

.question-preview {
  margin-top: 15px;
  background-color: #222230;
  padding: 10px;
  border-radius: 5px;
}

.question-preview h6 {
  margin-top: 0;
  color: #fff;
}

.question-preview ul {
  padding-left: 20px;
  margin-bottom: 0;
}

.question-preview li {
  margin-bottom: 8px;
}

.coming-soon {
  margin-top: 15px;
  font-style: italic;
  color: #888;
}

.btn {
  background: yellow;
  color: #333;
  border: none;
  padding: 8px 20px;
  font-weight: 500;
}

.btn:disabled {
  background: #888;
  color: #333;
}
