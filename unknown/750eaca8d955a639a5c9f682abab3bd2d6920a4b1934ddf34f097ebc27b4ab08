# Define a function to create a PDF with content
function Create-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$KeyPoints
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1500 >>
stream
BT
/F1 24 Tf
100 700 Td
($Title) Tj
/F2 12 Tf
0 -40 Td
($Description) Tj
0 -30 Td
/F1 14 Tf
(Key Components:) Tj
/F2 12 Tf
0 -20 Td
($KeyPoints) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1925
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created PDF with content: $safeFileName.pdf"
}

# 21. CMMC
Create-PDF -FileName "CMMC" `
    -Title "CMMC" `
    -Description "The Cybersecurity Maturity Model Certification (CMMC) is a unified standard for implementing cybersecurity across the Defense Industrial Base (DIB), which includes over 300,000 companies in the supply chain." `
    -KeyPoints "• Level 1: Foundational Cybersecurity Practices
• Level 2: Advanced Cybersecurity Practices
• Level 3: Expert Cybersecurity Practices
• 17 Capability Domains
• Access Control
• Asset Management
• Audit and Accountability
• Awareness and Training
• Configuration Management
• Identification and Authentication
• Incident Response
• Maintenance
• Media Protection
• Personnel Security
• Physical Protection
• Recovery
• Risk Management
• Security Assessment
• Situational Awareness
• System and Communications Protection
• System and Information Integrity"

# 22. ITAR/EAR
Create-PDF -FileName "ITAR/EAR" `
    -Title "ITAR/EAR" `
    -Description "The International Traffic in Arms Regulations (ITAR) and Export Administration Regulations (EAR) are U.S. export control laws that restrict the export of defense and dual-use items, technologies, and services to protect national security interests." `
    -KeyPoints "• ITAR: Controls defense articles and services
• EAR: Controls dual-use items and technologies
• U.S. Munitions List (USML)
• Commerce Control List (CCL)
• Technical Data Protection
• Deemed Exports
• Registration Requirements
• Licensing Requirements
• End-User Screening
• Recordkeeping Requirements
• Compliance Programs
• Penalties for Violations
• Voluntary Disclosures"

# 23. IEC 62443
Create-PDF -FileName "IEC 62443" `
    -Title "IEC 62443" `
    -Description "IEC 62443 is a series of international standards that address cybersecurity for operational technology in industrial automation and control systems (IACS). It provides a flexible framework to address and mitigate security vulnerabilities in industrial systems." `
    -KeyPoints "• General Security Requirements
• Security Program Requirements
• System Security Requirements
• Component Security Requirements
• Security Lifecycle
• Patch Management
• Defense in Depth
• Zone and Conduit Model
• Security Levels (SL 1-4)
• Risk Assessment
• System Design
• Implementation
• Maintenance
• Security Management System"

# 24. DOE C2M2
Create-PDF -FileName "DOE C2M2" `
    -Title "DOE C2M2" `
    -Description "The Department of Energy Cybersecurity Capability Maturity Model (DOE C2M2) is a model designed to help organizations evaluate and improve their cybersecurity programs, particularly in the energy sector." `
    -KeyPoints "• 10 Domains of Cybersecurity Capabilities
• Risk Management
• Asset, Change, and Configuration Management
• Identity and Access Management
• Threat and Vulnerability Management
• Situational Awareness
• Information Sharing and Communications
• Event and Incident Response
• Supply Chain and External Dependencies Management
• Workforce Management
• Cybersecurity Program Management
• Maturity Indicator Levels (MILs 0-3)
• Practices and Objectives"

# 25. NIST SP 800-82
Create-PDF -FileName "NIST SP 800-82" `
    -Title "NIST SP 800-82" `
    -Description "NIST Special Publication 800-82 provides guidance on securing Industrial Control Systems (ICS), including Supervisory Control and Data Acquisition (SCADA) systems, Distributed Control Systems (DCS), and other control system configurations." `
    -KeyPoints "• ICS Security Program Development
• ICS Risk Management
• ICS Security Architecture
• Network Segmentation and Segregation
• Boundary Protection
• Firewalls and DMZs
• Access Control
• Physical Security
• System Hardening
• Monitoring and Incident Response
• Secure Remote Access
• Secure Configuration
• Vulnerability Management
• Backup and Recovery
• Security Assessments"

Write-Host "Created fifth batch of PDFs (21-25)"
