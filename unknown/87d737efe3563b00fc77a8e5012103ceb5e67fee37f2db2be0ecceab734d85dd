import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-basic-document-selection',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="basic-selection-container">
      <div class="selection-header">
        <h4>{{ title }}</h4>
        <div class="selection-actions">
          <button (click)="selectAll()" class="btn-select">Select All</button>
          <button (click)="deselectAll()" class="btn-deselect">Deselect All</button>
        </div>
        <div class="selection-count">
          Selected: {{ getSelectedCount() }} of {{ getTotalCount() }}
        </div>
      </div>

      <div class="document-sections">
        <!-- Standards -->
        <div *ngIf="documents.Standard && documents.Standard.length > 0" class="document-section">
          <div class="section-header">
            <label class="section-label">
              <input
                type="checkbox"
                [checked]="isCategorySelected('Standard')"
                (change)="toggleCategory('Standard')"
              >
              <span>Standards ({{ documents.Standard.length }})</span>
            </label>
          </div>
          <div class="document-list">
            <div *ngFor="let doc of documents.Standard" class="document-item">
              <label>
                <input
                  type="checkbox"
                  [(ngModel)]="selectedDocs[doc]"
                  (change)="updateSelection()"
                >
                <span>{{ doc }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Guidelines -->
        <div *ngIf="documents.Guidelines && documents.Guidelines.length > 0" class="document-section">
          <div class="section-header">
            <label class="section-label">
              <input
                type="checkbox"
                [checked]="isCategorySelected('Guidelines')"
                (change)="toggleCategory('Guidelines')"
              >
              <span>Guidelines ({{ documents.Guidelines.length }})</span>
            </label>
          </div>
          <div class="document-list">
            <div *ngFor="let doc of documents.Guidelines" class="document-item">
              <label>
                <input
                  type="checkbox"
                  [(ngModel)]="selectedDocs[doc]"
                  (change)="updateSelection()"
                >
                <span>{{ doc }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Frameworks -->
        <div *ngIf="documents.Framework && documents.Framework.length > 0" class="document-section">
          <div class="section-header">
            <label class="section-label">
              <input
                type="checkbox"
                [checked]="isCategorySelected('Framework')"
                (change)="toggleCategory('Framework')"
              >
              <span>Frameworks ({{ documents.Framework.length }})</span>
            </label>
          </div>
          <div class="document-list">
            <div *ngFor="let doc of documents.Framework" class="document-item">
              <label>
                <input
                  type="checkbox"
                  [(ngModel)]="selectedDocs[doc]"
                  (change)="updateSelection()"
                >
                <span>{{ doc }}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Regulations -->
        <div *ngIf="documents.Regulation && documents.Regulation.length > 0" class="document-section">
          <div class="section-header">
            <label class="section-label">
              <input
                type="checkbox"
                [checked]="isCategorySelected('Regulation')"
                (change)="toggleCategory('Regulation')"
              >
              <span>Regulations ({{ documents.Regulation.length }})</span>
            </label>
          </div>
          <div class="document-list">
            <div *ngFor="let doc of documents.Regulation" class="document-item">
              <label>
                <input
                  type="checkbox"
                  [(ngModel)]="selectedDocs[doc]"
                  (change)="updateSelection()"
                >
                <span>{{ doc }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .basic-selection-container {
      background-color: #252531;
      border-radius: 5px;
      padding: 15px;
      margin-top: 10px;
      border: 1px solid #444;
    }

    .selection-header {
      margin-bottom: 15px;
    }

    .selection-header h4 {
      color: yellow;
      margin-bottom: 10px;
    }

    .selection-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
    }

    .btn-select, .btn-deselect {
      padding: 5px 10px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 14px;
    }

    .btn-select {
      background-color: #4CAF50;
      color: white;
    }

    .btn-deselect {
      background-color: #f44336;
      color: white;
    }

    .selection-count {
      color: #ccc;
      font-size: 14px;
      margin-top: 5px;
    }

    .document-sections {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .document-section {
      background-color: #333340;
      border-radius: 5px;
      padding: 10px;
    }

    .section-header {
      padding: 5px 0;
      margin-bottom: 10px;
      border-bottom: 1px solid #444;
    }

    .section-label {
      display: flex;
      align-items: center;
      color: white;
      font-weight: bold;
      cursor: pointer;
    }

    .section-label span {
      margin-left: 8px;
    }

    .document-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      max-height: 200px;
      overflow-y: auto;
      padding: 5px;
    }

    .document-item {
      padding: 5px;
      border-bottom: 1px solid #444;
    }

    .document-item:hover {
      background-color: #252531;
    }

    .document-item label {
      display: flex;
      align-items: center;
      color: #ccc;
      cursor: pointer;
      width: 100%;
    }

    .document-item span {
      margin-left: 8px;
    }

    input[type="checkbox"] {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }

    /* Scrollbar styling */
    .document-list::-webkit-scrollbar {
      width: 8px;
    }

    .document-list::-webkit-scrollbar-track {
      background: #252531;
    }

    .document-list::-webkit-scrollbar-thumb {
      background-color: #444;
      border-radius: 4px;
    }

    .document-list::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  `]
})
export class BasicDocumentSelectionComponent implements OnChanges {
  @Input() title: string = 'Select Documents';
  @Input() documents: any = {
    Standard: [],
    Guidelines: [],
    Framework: [],
    Regulation: []
  };

  @Output() selectionChanged = new EventEmitter<string[]>();

  selectedDocs: { [key: string]: boolean } = {};

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['documents']) {
      this.initializeSelection();
    }
  }

  initializeSelection(): void {
    // Initialize all documents as selected by default
    this.selectedDocs = {};

    for (const category in this.documents) {
      if (this.documents[category] && this.documents[category].length > 0) {
        for (const doc of this.documents[category]) {
          this.selectedDocs[doc] = true;
        }
      }
    }

    this.updateSelection();
  }

  updateSelection(): void {
    const selected: string[] = [];

    for (const doc in this.selectedDocs) {
      if (this.selectedDocs[doc]) {
        selected.push(doc);
      }
    }

    this.selectionChanged.emit(selected);
  }

  selectAll(): void {
    for (const category in this.documents) {
      if (this.documents[category] && this.documents[category].length > 0) {
        for (const doc of this.documents[category]) {
          this.selectedDocs[doc] = true;
        }
      }
    }

    this.updateSelection();
  }

  deselectAll(): void {
    for (const category in this.documents) {
      if (this.documents[category] && this.documents[category].length > 0) {
        for (const doc of this.documents[category]) {
          this.selectedDocs[doc] = false;
        }
      }
    }

    this.updateSelection();
  }

  toggleCategory(category: string): void {
    const isSelected = this.isCategorySelected(category);

    if (this.documents[category] && this.documents[category].length > 0) {
      for (const doc of this.documents[category]) {
        this.selectedDocs[doc] = !isSelected;
      }
    }

    this.updateSelection();
  }

  isCategorySelected(category: string): boolean {
    if (!this.documents[category] || this.documents[category].length === 0) {
      return false;
    }

    return this.documents[category].every((doc: string) => this.selectedDocs[doc]);
  }

  getSelectedCount(): number {
    let count = 0;
    for (const doc in this.selectedDocs) {
      if (this.selectedDocs[doc]) {
        count++;
      }
    }
    return count;
  }

  getTotalCount(): number {
    let count = 0;
    for (const category in this.documents) {
      if (this.documents[category]) {
        count += this.documents[category].length;
      }
    }
    return count;
  }
}
