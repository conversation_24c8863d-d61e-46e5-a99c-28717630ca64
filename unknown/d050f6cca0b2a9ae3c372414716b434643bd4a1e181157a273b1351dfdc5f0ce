import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { MaterialModule } from '../../../material/material.module';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GlobalDataService } from '../../../models/gloabl_data.model';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-domain-selection',
  standalone: true,
  imports: [
    MaterialModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatButtonModule,
    MatDividerModule
  ],
  templateUrl: './domain-selection.component.html',
  styleUrl: './domain-selection.component.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DomainSelectionComponent implements OnInit {
  domains: any[] = [
    { id: 1, name: 'Cross-Industry', selected: false },
    { id: 2, name: 'Cloud Security', selected: false },
    { id: 3, name: 'AI', selected: false },
    { id: 4, name: 'Financial Services', selected: false },
    { id: 5, name: 'Healthcare', selected: false },
    { id: 6, name: 'Government / Defense', selected: false },
    { id: 7, name: 'Energy / Utilities', selected: false },
    { id: 8, name: 'Telecom', selected: false },
    { id: 9, name: 'Retail / eCommerce', selected: false },
    { id: 10, name: 'Cloud / SaaS', selected: false },
    { id: 11, name: 'Manufacturing / Industrial', selected: false },
    // Add the four domains from the screenshot
    { id: 12, name: 'Organizational', selected: false },
    { id: 13, name: 'People', selected: false },
    { id: 14, name: 'Physical', selected: false },
    { id: 15, name: 'Technological', selected: false }
  ];

  benchmarks: any = {
    'Cross-Industry': [
      { id: 1, name: 'ISO/IEC 27001', selected: false },
      { id: 2, name: 'NIST CSF 2.0', selected: false },
      { id: 3, name: 'NIST SP 800-53', selected: false },
      { id: 4, name: 'CIS Benchmarks', selected: false },
      { id: 5, name: 'ISO 22301', selected: false },
      { id: 6, name: 'SOC 2 (AICPA)', selected: false },
      { id: 7, name: 'MITRE ATT@CK', selected: false }
    ],
    'Cloud Security': [
      { id: 8, name: 'AWS Security Practice', selected: false },
      { id: 9, name: 'Azure Security Practice', selected: false },
      { id: 10, name: 'GCP Security Practice', selected: false }
    ],
    'AI': [
      { id: 11, name: 'ISO 42001', selected: false }
    ],
    'Financial Services': [
      { id: 12, name: 'PCI DSS', selected: false },
      { id: 13, name: 'FFIEC CAT', selected: false },
      { id: 14, name: 'NIS2 & DORA', selected: false }
    ],
    'Healthcare': [
      { id: 15, name: 'HIPAA Security Rule', selected: false },
      { id: 16, name: 'HITECH', selected: false },
      { id: 17, name: 'ISO/IEC 27799', selected: false }
    ],
    'Government / Defense': [
      { id: 18, name: 'NIST SP 800-171', selected: false },
      { id: 19, name: 'FedRAMP', selected: false },
      { id: 20, name: 'CMMC', selected: false },
      { id: 21, name: 'ITAR/EAR', selected: false }
    ],
    'Energy / Utilities': [
      { id: 22, name: 'IEC 62443', selected: false },
      { id: 23, name: 'DOE C2M2', selected: false },
      { id: 24, name: 'NIST SP 800-82', selected: false }
    ],
    'Telecom': [
      { id: 25, name: 'ISO/IEC 27011', selected: false },
      { id: 26, name: 'ETSI EN 303 645', selected: false }
    ],
    'Retail / eCommerce': [
      { id: 27, name: 'PCI DSS', selected: false },
      { id: 28, name: 'ISO/IEC 29100', selected: false },
      { id: 29, name: 'NIST Privacy Framework', selected: false }
    ],
    'Cloud / SaaS': [
      { id: 30, name: 'ISO/IEC 27017', selected: false },
      { id: 31, name: 'ISO/IEC 27018', selected: false },
      { id: 32, name: 'CSA STAR', selected: false },
      { id: 33, name: 'SOC 2 Type II', selected: false }
    ],
    'Manufacturing / Industrial': [
      { id: 34, name: 'IEC 62443', selected: false },
      { id: 35, name: 'NIST SP 800-82', selected: false },
      { id: 36, name: 'ISO 28000', selected: false }
    ],
    // Add benchmarks for the four new domains
    'Organizational': [
      { id: 37, name: 'ISO/IEC 27001', selected: false },
      { id: 38, name: 'NIST CSF 2.0', selected: false },
      { id: 39, name: 'COBIT', selected: false }
    ],
    'People': [
      { id: 40, name: 'ISO/IEC 27001', selected: false },
      { id: 41, name: 'NIST SP 800-53', selected: false },
      { id: 42, name: 'CIS Controls', selected: false }
    ],
    'Physical': [
      { id: 43, name: 'ISO/IEC 27001', selected: false },
      { id: 44, name: 'NIST SP 800-53', selected: false },
      { id: 45, name: 'CIS Controls', selected: false }
    ],
    'Technological': [
      { id: 46, name: 'ISO/IEC 27001', selected: false },
      { id: 47, name: 'NIST SP 800-53', selected: false },
      { id: 48, name: 'CIS Controls', selected: false },
      { id: 49, name: 'OWASP Top 10', selected: false }
    ]
  };

  commonBenchmarks: any[] = [
    { id: 1, name: 'ISO/IEC 27001', selected: false },
    { id: 2, name: 'NIST CSF 2.0', selected: false },
    { id: 3, name: 'NIST SP 800-53', selected: false },
    { id: 4, name: 'CIS Benchmarks', selected: false },
    { id: 5, name: 'ISO 22301', selected: false },
    { id: 7, name: 'MITRE ATT@CK', selected: false }
  ];

  selectedDomains: any[] = [];
  selectedBenchmarks: any[] = [];
  availableBenchmarks: any[] = [];

  constructor(
    private router: Router,
    private globalDataService: GlobalDataService
  ) { }

  ngOnInit(): void {
    this.updateAvailableBenchmarks();
  }

  toggleDomainSelection(domain: any): void {
    domain.selected = !domain.selected;
    this.updateSelectedDomains();
    this.updateAvailableBenchmarks();
  }

  toggleBenchmarkSelection(benchmark: any): void {
    benchmark.selected = !benchmark.selected;
    this.updateSelectedBenchmarks();
  }

  updateSelectedDomains(): void {
    this.selectedDomains = this.domains.filter(domain => domain.selected);
  }

  updateSelectedBenchmarks(): void {
    this.selectedBenchmarks = this.availableBenchmarks.filter(benchmark => benchmark.selected);
  }

  updateAvailableBenchmarks(): void {
    this.availableBenchmarks = [];

    // Add common benchmarks
    this.availableBenchmarks = [...this.commonBenchmarks];

    // Add domain-specific benchmarks
    this.selectedDomains.forEach(domain => {
      const domainBenchmarks = this.benchmarks[domain.name];
      if (domainBenchmarks) {
        // Add benchmarks that aren't already in the list
        domainBenchmarks.forEach((benchmark: any) => {
          if (!this.availableBenchmarks.some(b => b.name === benchmark.name)) {
            this.availableBenchmarks.push(benchmark);
          }
        });
      }
    });
  }

  navigateToQuestionSelection(): void {
    // Store selected domains and benchmarks in the global service
    this.globalDataService.setSelectedDomains(this.selectedDomains);
    this.globalDataService.setSelectedBenchmarks(this.selectedBenchmarks);

    // Navigate to the question selection page
    this.router.navigateByUrl('/question-selection');
  }
}
