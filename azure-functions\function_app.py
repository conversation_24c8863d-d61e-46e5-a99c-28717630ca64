import azure.functions as func
import logging
import json
import os
from typing import List, Optional

# Import your existing modules (you'll need to copy them to the functions directory)
from shared.config import configs
from shared.evidence_main import save_evidence_files
from shared.evidence_utils import write_to_excel
from shared.document_mappings import (
    get_documents_for_region_selection,
    get_documents_for_domain_selection,
    get_combined_documents
)

app = func.FunctionApp(http_auth_level=func.AuthLevel.ANONYMOUS)

@app.route(route="health", methods=["GET"])
def health_check(req: func.HttpRequest) -> func.HttpResponse:
    """Health check endpoint"""
    logging.info('Health check endpoint called.')
    return func.HttpResponse(
        json.dumps({"status": "healthy", "service": "IsecMapper Functions"}),
        status_code=200,
        mimetype="application/json"
    )

@app.route(route="region_selection", methods=["GET"])
def get_region_selection(req: func.HttpRequest) -> func.HttpResponse:
    """Get the available options for region-based selection."""
    try:
        from shared.document_mappings import REGION_MAPPING

        regions = list(REGION_MAPPING.keys())
        industries_by_region = {}
        sectors_by_industry = {}

        for region, industries in REGION_MAPPING.items():
            industries_by_region[region] = list(industries.keys())
            for industry, sectors in industries.items():
                key = f"{region}_{industry}"
                sectors_by_industry[key] = list(sectors.keys())

        result = {
            "regions": regions,
            "industries_by_region": industries_by_region,
            "sectors_by_industry": sectors_by_industry
        }

        return func.HttpResponse(
            json.dumps(result),
            status_code=200,
            mimetype="application/json"
        )
    except Exception as e:
        logging.error(f"Error in get_region_selection: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

@app.route(route="domain_selection", methods=["GET"])
def get_domain_selection(req: func.HttpRequest) -> func.HttpResponse:
    """Get the available options for domain-based selection."""
    try:
        from shared.document_mappings import DOMAIN_MAPPING

        domains = list(DOMAIN_MAPPING.keys())
        csps_by_domain = {}

        for domain, csps in DOMAIN_MAPPING.items():
            csps_by_domain[domain] = list(csps.keys())

        result = {
            "domains": domains,
            "csps_by_domain": csps_by_domain
        }

        return func.HttpResponse(
            json.dumps(result),
            status_code=200,
            mimetype="application/json"
        )
    except Exception as e:
        logging.error(f"Error in get_domain_selection: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

@app.route(route="documents_for_selection", methods=["GET"])
def get_documents_for_selection(req: func.HttpRequest) -> func.HttpResponse:
    """Get the list of documents for the given selection parameters."""
    try:
        region = req.params.get('region')
        industry = req.params.get('industry')
        sector = req.params.get('sector')
        domain = req.params.get('domain')
        csp = req.params.get('csp')

        # If both region and domain selections are provided, combine them
        if region and industry and sector and domain:
            result = get_combined_documents(region, industry, sector, domain, csp)
        elif region and industry and sector:
            result = get_documents_for_region_selection(region, industry, sector)
        elif domain:
            result = get_documents_for_domain_selection(domain, csp)
        else:
            result = {
                "Standard": [],
                "Guidelines": [],
                "Framework": [],
                "Regulation": []
            }

        return func.HttpResponse(
            json.dumps(result),
            status_code=200,
            mimetype="application/json"
        )
    except Exception as e:
        logging.error(f"Error in get_documents_for_selection: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

# For file upload and analysis, we'll use Service Bus to handle long-running tasks
@app.route(route="analyze_evidences", methods=["POST"])
def analyze_evidences_trigger(req: func.HttpRequest) -> func.HttpResponse:
    """Trigger evidence analysis - queues the job for processing"""
    try:
        # Parse multipart form data
        files_data = []
        form_data = {}

        # Note: Azure Functions handle multipart differently
        # You might need to use a different approach for file uploads

        # For now, return a job ID and queue the actual processing
        import uuid
        job_id = str(uuid.uuid4())

        # Queue the job for processing (implement Service Bus integration)
        # queue_analysis_job(job_id, files_data, form_data)

        return func.HttpResponse(
            json.dumps({
                "job_id": job_id,
                "status": "queued",
                "message": "Analysis job has been queued for processing"
            }),
            status_code=202,
            mimetype="application/json"
        )
    except Exception as e:
        logging.error(f"Error in analyze_evidences_trigger: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

@app.route(route="job_status/{job_id}", methods=["GET"])
def get_job_status(req: func.HttpRequest) -> func.HttpResponse:
    """Get the status of an analysis job"""
    try:
        job_id = req.route_params.get('job_id')

        # Check job status from storage/database
        # status = get_job_status_from_storage(job_id)

        # Placeholder response
        status = {
            "job_id": job_id,
            "status": "processing",  # queued, processing, completed, failed
            "progress": 50,
            "message": "Analysis in progress..."
        }

        return func.HttpResponse(
            json.dumps(status),
            status_code=200,
            mimetype="application/json"
        )
    except Exception as e:
        logging.error(f"Error in get_job_status: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            mimetype="application/json"
        )

# Service Bus triggered function for processing analysis jobs
@app.service_bus_queue_trigger(arg_name="msg",
                               connection="AZURE_SERVICEBUS_CONNECTION_STRING",
                               queue_name="analysis-jobs")
def process_analysis_job(msg: func.ServiceBusMessage):
    """Process analysis jobs from Service Bus queue"""
    try:
        logging.info(f"Processing analysis job: {msg.get_body().decode('utf-8')}")

        # Parse the message
        job_data = json.loads(msg.get_body().decode('utf-8'))
        job_id = job_data.get('job_id')

        # Import the job processing service
        from shared.job_processor import JobProcessor
        processor = JobProcessor()

        # Update job status to processing
        processor.update_job_status(job_id, "processing")

        # Process the analysis
        result = processor.process_evidence_analysis(job_data)

        # Update job status to completed
        processor.update_job_status(job_id, "completed", result)

        logging.info(f"Analysis job {job_id} completed successfully")

    except Exception as e:
        logging.error(f"Error processing analysis job: {str(e)}")
        # Update job status to failed
        from shared.job_processor import JobProcessor
        processor = JobProcessor()
        processor.update_job_status(job_id, "failed", error=str(e))

@app.route(route="extract_domains_from_excel", methods=["POST"])
def extract_domains_from_excel(req: func.HttpRequest) -> func.HttpResponse:
    """Extract domains from an uploaded Excel file."""
    try:
        # Handle file upload for Azure Functions
        # Note: This is a simplified version - you may need to handle multipart differently

        import pandas as pd
        import tempfile
        import uuid

        # Get file from request (this needs proper multipart handling)
        # For now, assume file content is in request body

        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')

        # In a real implementation, you'd extract the file from multipart form data
        # temp_file.write(file_content)
        # temp_file.close()

        # Read the Excel file
        # df = pd.read_excel(temp_file.name)

        # For now, return a placeholder response
        domains = ["General", "Security", "Compliance"]

        result = {
            "domains": domains,
            "csps_by_domain": {domain: [] for domain in domains}
        }

        return func.HttpResponse(
            json.dumps(result),
            status_code=200,
            mimetype="application/json"
        )

    except Exception as e:
        logging.error(f"Error extracting domains from Excel: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "domains": ["General"],
                "csps_by_domain": {"General": []}
            }),
            status_code=200,
            mimetype="application/json"
        )
