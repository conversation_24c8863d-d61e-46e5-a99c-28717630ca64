# imports from packages
import json
from fastapi import APIRouter, WebSocket
from fastapi import UploadFile
import asyncio

# custom imports
from app.util.utils import get_unique_id
from app.util.chatbot_main import (
    save_file_create_embeddings,
    get_llama_chatengine,
    stream_chat_response,
    delete_llama_index,
)
from app.util.chatbot_manager import manager


router = APIRouter(
    tags=["chatbot"],
)


@router.get("/get_chatbot_status", status_code=200)
async def get_chatbot_status():
    return "Chatbot is running..."


@router.post("/upload_chatbot_file")
async def upload_chatbot_file(file: list[UploadFile]):
    return save_file_create_embeddings(file)


# as this is router, this is {base url}/api/{name}
@router.websocket("/chatbot/{file_id}")
async def websocket_chat(websocket: WebSocket, file_id: str):
    chat_engine = get_llama_chatengine(file_id)

    await manager.connect(file_id, websocket)
    try:
        while True:
            unique_id = get_unique_id()
            msg = await websocket.receive_text()

            if msg:
                content = {"status": "generate-start", "id": unique_id}
                content = json.dumps(content)
                await manager.send_personal_message(content, websocket)

            for stream_msg in stream_chat_response(
                chat_engine=chat_engine, message=msg
            ):
                content = {
                    "status": "generate-stream",
                    "id": unique_id,
                    "data": stream_msg,
                }
                content = json.dumps(content)
                await manager.send_personal_message(content, websocket)
                await asyncio.sleep(0.02)

            content = {"status": "generate-end", "id": unique_id}
            content = json.dumps(content)
            await manager.send_personal_message(content, websocket)
            # await manager.broadcast(f"A client says: {data}", file_id, websocket)
    except Exception as e:
        print("Got an exception ", e)
        await manager.disconnect(file_id, websocket)
        delete_llama_index(file_id)
