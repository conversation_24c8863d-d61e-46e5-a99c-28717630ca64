{"version": 3, "sources": ["../../../../../node_modules/file-saver/dist/FileSaver.min.js"], "sourcesContent": ["(function(a,b){if(\"function\"==typeof define&&define.amd)define([],b);else if(\"undefined\"!=typeof exports)b();else{b(),a.FileSaver={exports:{}}.exports}})(this,function(){\"use strict\";function b(a,b){return\"undefined\"==typeof b?b={autoBom:!1}:\"object\"!=typeof b&&(console.warn(\"Deprecated: Expected third argument to be a object\"),b={autoBom:!b}),b.autoBom&&/^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(a.type)?new Blob([\"\\uFEFF\",a],{type:a.type}):a}function c(a,b,c){var d=new XMLHttpRequest;d.open(\"GET\",a),d.responseType=\"blob\",d.onload=function(){g(d.response,b,c)},d.onerror=function(){console.error(\"could not download file\")},d.send()}function d(a){var b=new XMLHttpRequest;b.open(\"HEAD\",a,!1);try{b.send()}catch(a){}return 200<=b.status&&299>=b.status}function e(a){try{a.dispatchEvent(new MouseEvent(\"click\"))}catch(c){var b=document.createEvent(\"MouseEvents\");b.initMouseEvent(\"click\",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),a.dispatchEvent(b)}}var f=\"object\"==typeof window&&window.window===window?window:\"object\"==typeof self&&self.self===self?self:\"object\"==typeof global&&global.global===global?global:void 0,a=f.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),g=f.saveAs||(\"object\"!=typeof window||window!==f?function(){}:\"download\"in HTMLAnchorElement.prototype&&!a?function(b,g,h){var i=f.URL||f.webkitURL,j=document.createElement(\"a\");g=g||b.name||\"download\",j.download=g,j.rel=\"noopener\",\"string\"==typeof b?(j.href=b,j.origin===location.origin?e(j):d(j.href)?c(b,g,h):e(j,j.target=\"_blank\")):(j.href=i.createObjectURL(b),setTimeout(function(){i.revokeObjectURL(j.href)},4E4),setTimeout(function(){e(j)},0))}:\"msSaveOrOpenBlob\"in navigator?function(f,g,h){if(g=g||f.name||\"download\",\"string\"!=typeof f)navigator.msSaveOrOpenBlob(b(f,h),g);else if(d(f))c(f,g,h);else{var i=document.createElement(\"a\");i.href=f,i.target=\"_blank\",setTimeout(function(){e(i)})}}:function(b,d,e,g){if(g=g||open(\"\",\"_blank\"),g&&(g.document.title=g.document.body.innerText=\"downloading...\"),\"string\"==typeof b)return c(b,d,e);var h=\"application/octet-stream\"===b.type,i=/constructor/i.test(f.HTMLElement)||f.safari,j=/CriOS\\/[\\d]+/.test(navigator.userAgent);if((j||h&&i||a)&&\"undefined\"!=typeof FileReader){var k=new FileReader;k.onloadend=function(){var a=k.result;a=j?a:a.replace(/^data:[^;]*;/,\"data:attachment/file;\"),g?g.location.href=a:location=a,g=null},k.readAsDataURL(b)}else{var l=f.URL||f.webkitURL,m=l.createObjectURL(b);g?g.location=m:location.href=m,g=null,setTimeout(function(){l.revokeObjectURL(m)},4E4)}});f.saveAs=g.saveAs=g,\"undefined\"!=typeof module&&(module.exports=g)});\n\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,UAAG,cAAY,OAAO,UAAQ,OAAO;AAAI,eAAO,CAAC,GAAE,CAAC;AAAA,eAAU,eAAa,OAAO;AAAQ,UAAE;AAAA,WAAM;AAAC,UAAE,GAAE,EAAE,YAAU,EAAC,SAAQ,CAAC,EAAC,EAAE;AAAA,MAAO;AAAA,IAAC,GAAG,SAAK,WAAU;AAAC;AAAa,eAAS,EAAEA,IAAEC,IAAE;AAAC,eAAM,eAAa,OAAOA,KAAEA,KAAE,EAAC,SAAQ,MAAE,IAAE,YAAU,OAAOA,OAAI,QAAQ,KAAK,oDAAoD,GAAEA,KAAE,EAAC,SAAQ,CAACA,GAAC,IAAGA,GAAE,WAAS,6EAA6E,KAAKD,GAAE,IAAI,IAAE,IAAI,KAAK,CAAC,UAASA,EAAC,GAAE,EAAC,MAAKA,GAAE,KAAI,CAAC,IAAEA;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,YAAIC,KAAE,IAAI;AAAe,QAAAA,GAAE,KAAK,OAAMH,EAAC,GAAEG,GAAE,eAAa,QAAOA,GAAE,SAAO,WAAU;AAAC,YAAEA,GAAE,UAASF,IAAEC,EAAC;AAAA,QAAC,GAAEC,GAAE,UAAQ,WAAU;AAAC,kBAAQ,MAAM,yBAAyB;AAAA,QAAC,GAAEA,GAAE,KAAK;AAAA,MAAC;AAAC,eAAS,EAAEH,IAAE;AAAC,YAAIC,KAAE,IAAI;AAAe,QAAAA,GAAE,KAAK,QAAOD,IAAE,KAAE;AAAE,YAAG;AAAC,UAAAC,GAAE,KAAK;AAAA,QAAC,SAAOD,IAAE;AAAA,QAAC;AAAC,eAAO,OAAKC,GAAE,UAAQ,OAAKA,GAAE;AAAA,MAAM;AAAC,eAAS,EAAED,IAAE;AAAC,YAAG;AAAC,UAAAA,GAAE,cAAc,IAAI,WAAW,OAAO,CAAC;AAAA,QAAC,SAAOE,IAAE;AAAC,cAAID,KAAE,SAAS,YAAY,aAAa;AAAE,UAAAA,GAAE,eAAe,SAAQ,MAAG,MAAG,QAAO,GAAE,GAAE,GAAE,IAAG,IAAG,OAAG,OAAG,OAAG,OAAG,GAAE,IAAI,GAAED,GAAE,cAAcC,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI,IAAE,YAAU,OAAO,UAAQ,OAAO,WAAS,SAAO,SAAO,YAAU,OAAO,QAAM,KAAK,SAAO,OAAK,OAAK,YAAU,OAAO,UAAQ,OAAO,WAAS,SAAO,SAAO,QAAO,IAAE,EAAE,aAAW,YAAY,KAAK,UAAU,SAAS,KAAG,cAAc,KAAK,UAAU,SAAS,KAAG,CAAC,SAAS,KAAK,UAAU,SAAS,GAAE,IAAE,EAAE,WAAS,YAAU,OAAO,UAAQ,WAAS,IAAE,WAAU;AAAA,MAAC,IAAE,cAAa,kBAAkB,aAAW,CAAC,IAAE,SAASA,IAAEG,IAAE,GAAE;AAAC,YAAI,IAAE,EAAE,OAAK,EAAE,WAAU,IAAE,SAAS,cAAc,GAAG;AAAE,QAAAA,KAAEA,MAAGH,GAAE,QAAM,YAAW,EAAE,WAASG,IAAE,EAAE,MAAI,YAAW,YAAU,OAAOH,MAAG,EAAE,OAAKA,IAAE,EAAE,WAAS,SAAS,SAAO,EAAE,CAAC,IAAE,EAAE,EAAE,IAAI,IAAE,EAAEA,IAAEG,IAAE,CAAC,IAAE,EAAE,GAAE,EAAE,SAAO,QAAQ,MAAI,EAAE,OAAK,EAAE,gBAAgBH,EAAC,GAAE,WAAW,WAAU;AAAC,YAAE,gBAAgB,EAAE,IAAI;AAAA,QAAC,GAAE,GAAG,GAAE,WAAW,WAAU;AAAC,YAAE,CAAC;AAAA,QAAC,GAAE,CAAC;AAAA,MAAE,IAAE,sBAAqB,YAAU,SAASI,IAAED,IAAE,GAAE;AAAC,YAAGA,KAAEA,MAAGC,GAAE,QAAM,YAAW,YAAU,OAAOA;AAAE,oBAAU,iBAAiB,EAAEA,IAAE,CAAC,GAAED,EAAC;AAAA,iBAAU,EAAEC,EAAC;AAAE,YAAEA,IAAED,IAAE,CAAC;AAAA,aAAM;AAAC,cAAI,IAAE,SAAS,cAAc,GAAG;AAAE,YAAE,OAAKC,IAAE,EAAE,SAAO,UAAS,WAAW,WAAU;AAAC,cAAE,CAAC;AAAA,UAAC,CAAC;AAAA,QAAC;AAAA,MAAC,IAAE,SAASJ,IAAEE,IAAEG,IAAEF,IAAE;AAAC,YAAGA,KAAEA,MAAG,KAAK,IAAG,QAAQ,GAAEA,OAAIA,GAAE,SAAS,QAAMA,GAAE,SAAS,KAAK,YAAU,mBAAkB,YAAU,OAAOH;AAAE,iBAAO,EAAEA,IAAEE,IAAEG,EAAC;AAAE,YAAI,IAAE,+BAA6BL,GAAE,MAAK,IAAE,eAAe,KAAK,EAAE,WAAW,KAAG,EAAE,QAAO,IAAE,eAAe,KAAK,UAAU,SAAS;AAAE,aAAI,KAAG,KAAG,KAAG,MAAI,eAAa,OAAO,YAAW;AAAC,cAAI,IAAE,IAAI;AAAW,YAAE,YAAU,WAAU;AAAC,gBAAID,KAAE,EAAE;AAAO,YAAAA,KAAE,IAAEA,KAAEA,GAAE,QAAQ,gBAAe,uBAAuB,GAAEI,KAAEA,GAAE,SAAS,OAAKJ,KAAE,WAASA,IAAEI,KAAE;AAAA,UAAI,GAAE,EAAE,cAAcH,EAAC;AAAA,QAAC,OAAK;AAAC,cAAI,IAAE,EAAE,OAAK,EAAE,WAAU,IAAE,EAAE,gBAAgBA,EAAC;AAAE,UAAAG,KAAEA,GAAE,WAAS,IAAE,SAAS,OAAK,GAAEA,KAAE,MAAK,WAAW,WAAU;AAAC,cAAE,gBAAgB,CAAC;AAAA,UAAC,GAAE,GAAG;AAAA,QAAC;AAAA,MAAC;AAAG,QAAE,SAAO,EAAE,SAAO,GAAE,eAAa,OAAO,WAAS,OAAO,UAAQ;AAAA,IAAE,CAAC;AAAA;AAAA;", "names": ["a", "b", "c", "d", "g", "f", "e"]}