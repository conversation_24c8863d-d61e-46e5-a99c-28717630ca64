# Stage 0, "build-stage", based on Node.js, to build and compile the frontend
FROM node:22-alpine AS buildfrontend

WORKDIR /app

COPY package*.json /app/
RUN npm install

COPY ./ /app/
ARG configuration=production
RUN npm run build -- -c $configuration

# Stage 1, based on Nginx, to have only the compiled app, ready for production with Nginx
FROM nginx:1.27-alpine

#Copy ci-dashboard-dist
COPY --from=buildfrontend /app/dist/browser /usr/share/nginx/html

#Copy default nginx configuration
COPY ./nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
