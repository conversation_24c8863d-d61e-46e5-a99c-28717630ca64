/* You can add global styles to this file, and also import other style files */

html,
body {
  height: 100%;
  color: #282828 !important;
  /*#343a40 !important;*/
  background: #1a1a26;
}

/* // <uniquifier>: Use a uniquifier for the class name
// <weight>: Use a value from 100 to 900

.noto-sans-<uniquifier> {
  font-family: "Noto Sans", sans-serif;
  font-optical-sizing: auto;
  font-weight: <weight>;
  font-style: normal;
  font-variation-settings:
    "wdth" 100;
} */

/* @import url('https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&display=swap'); */

body {
  margin: 0;
  /* font-family: Roboto, "Helvetica Neue", sans-serif; */
  font-family: "Noto Sans", sans-serif;
}

.container {
  margin: 0 auto;
  padding: 0px 20px;
}

.flex {
  display: flex;
}

.spinner-container {
  position: absolute;
  z-index: 99999;
  top: 25%;
  left: 45%;
}

.f-col {
  flex-direction: column;
}

.f-row {
  flex-direction: row;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.bold {
  font-weight: bold;
}

.flex-gap-5 {
  gap: 5px;
}

.flex-gap-10 {
  gap: 10px;
}

.flex-gap-15 {
  gap: 15px;
}

.wrap {
  flex-wrap: wrap;
}

.w-100 {
  width: 100%;
}

.w-50 {
  width: 50%;
}

.mb-10 {
  margin-bottom: 10px;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.clearfix {
  clear: both;
}

.mat-toolbar.mat-primary {
  color: #005192;
  background: #fff;
}

.mat-paginator-page-size-label {
  display: none;
}

.mat-paginator-page-size-value {
  display: none;
}

.mat-paginator-range-label {
  display: none;
}

.mat-paginator-page-size {
  display: none !important;
}

.mat-drawer-inner-container {
  overflow: hidden !important;
}

.highlighted-text {
  background: yellow;
}

.highlighted-desc {
  color: #ffffff;
  font-family: inherit;
}

.highlighted-bullet {
  color: #ffffff;
  font-family: Open Sans;
}

.highlighted-text-reviewkeywords {
  color: #000 !important;
  background-color: #ffe611 !important;
  font-family: Open Sans;
}

.highlighted-text-seokeywords {
  color: #000;
  background-color: #d46b08;
  font-family: Open Sans;
}
