# IsecMapper Azure Functions Migration

This directory contains the Azure Functions version of your IsecMapper application. The migration follows a hybrid approach to maintain functionality while leveraging Azure's serverless capabilities.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │  Azure Function  │    │  Azure Storage  │
│   (Angular)     │───▶│      App         │───▶│    Account      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                          │
                              ▼                          ▼
                    ┌──────────────────┐    ┌─────────────────┐
                    │  Azure Service   │    │  Azure Cosmos   │
                    │      Bus         │    │      DB         │
                    └──────────────────┘    └─────────────────┘
```

## Migration Strategy

### Phase 1: Core API Functions ✅
- **Migrated**: Region/Domain selection endpoints
- **Migrated**: Document retrieval endpoints  
- **Migrated**: Health check and status endpoints

### Phase 2: File Processing (In Progress)
- **Modified**: File upload handling for Azure Functions
- **Added**: Service Bus integration for long-running tasks
- **Added**: Job status tracking with Cosmos DB

### Phase 3: WebSocket Alternative (Planned)
- **Option A**: Keep chatbot as separate Azure Container Instance
- **Option B**: Migrate to Azure SignalR Service

## Key Changes from FastAPI

### 1. **File Upload Handling**
- **Before**: Direct multipart file handling
- **After**: Azure Blob Storage integration
- **Impact**: Files are stored in Azure Blob Storage instead of local filesystem

### 2. **Long-Running Tasks**
- **Before**: Synchronous processing with ThreadPool
- **After**: Asynchronous processing with Service Bus queues
- **Impact**: Better scalability and reliability

### 3. **State Management**
- **Before**: In-memory state and local file storage
- **After**: Azure Cosmos DB for job tracking, Blob Storage for files
- **Impact**: Stateless functions with persistent storage

### 4. **WebSocket Connections**
- **Before**: Direct WebSocket support in FastAPI
- **After**: Azure SignalR Service (recommended) or separate service
- **Impact**: Real-time communication handled by dedicated service

## Prerequisites

1. **Azure CLI** - [Install Azure CLI](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli)
2. **Azure Functions Core Tools** - [Install Functions Core Tools](https://docs.microsoft.com/en-us/azure/azure-functions/functions-run-local)
3. **Python 3.11** - Required for Azure Functions Python runtime
4. **Azure Subscription** - With appropriate permissions

## Quick Start

### 1. Local Development

```bash
# Install Azure Functions Core Tools
npm install -g azure-functions-core-tools@4 --unsafe-perm true

# Navigate to the functions directory
cd azure-functions

# Install Python dependencies
pip install -r requirements.txt

# Update local.settings.json with your Azure credentials
# Copy local.settings.json.template to local.settings.json and fill in values

# Start the function app locally
func start
```

### 2. Deploy to Azure

```powershell
# Run the deployment script
.\deploy.ps1 -ResourceGroupName "rg-isecmapper" -FunctionAppName "func-isecmapper" -StorageAccountName "stisecmapper" -ServiceBusNamespace "sb-isecmapper" -CosmosAccountName "cosmos-isecmapper"
```

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `AZURE_OPENAI_API_KEY` | Your Azure OpenAI API key | Yes |
| `AZURE_OPENAI_ENDPOINT` | Your Azure OpenAI endpoint URL | Yes |
| `AZURE_OPENAI_VERSION` | API version (default: 2025-01-01-preview) | No |
| `AZURE_STORAGE_CONNECTION_STRING` | Azure Storage connection string | Yes |
| `AZURE_SERVICEBUS_CONNECTION_STRING` | Service Bus connection string | Yes |
| `AZURE_COSMOS_CONNECTION_STRING` | Cosmos DB connection string | Yes |

### Azure Resources Required

1. **Azure Function App** - Hosts the serverless functions
2. **Azure Storage Account** - Stores files and function metadata
3. **Azure Service Bus** - Handles long-running task queues
4. **Azure Cosmos DB** - Tracks job status and metadata
5. **Azure OpenAI** - Your existing AI service (no changes needed)

## API Endpoints

### Migrated Endpoints

| Endpoint | Method | Description | Status |
|----------|--------|-------------|--------|
| `/api/health` | GET | Health check | ✅ Migrated |
| `/api/region_selection` | GET | Get region options | ✅ Migrated |
| `/api/domain_selection` | GET | Get domain options | ✅ Migrated |
| `/api/documents_for_selection` | GET | Get documents | ✅ Migrated |
| `/api/analyze_evidences` | POST | Trigger analysis | 🔄 Modified |
| `/api/job_status/{job_id}` | GET | Check job status | ✅ New |

### New Workflow for File Analysis

1. **Upload Files**: POST to `/api/analyze_evidences`
   - Returns `job_id` immediately
   - Files are uploaded to Azure Blob Storage
   - Analysis job is queued in Service Bus

2. **Check Status**: GET `/api/job_status/{job_id}`
   - Returns current status: `queued`, `processing`, `completed`, `failed`
   - Includes progress percentage and messages

3. **Get Results**: When status is `completed`
   - Results are available in the job status response
   - Files can be downloaded from Blob Storage

## Limitations and Considerations

### Current Limitations

1. **Function Timeout**: 10 minutes maximum execution time
2. **Memory Limits**: 1.5GB maximum memory in consumption plan
3. **Cold Starts**: Initial requests may have higher latency
4. **WebSocket Support**: Not available in Azure Functions

### Recommended Solutions

1. **For Long Tasks**: Use Service Bus + Durable Functions
2. **For Large Files**: Stream processing with Blob Storage
3. **For Real-time**: Azure SignalR Service
4. **For Performance**: Premium or Dedicated hosting plans

## Migration Checklist

### Completed ✅
- [x] Core API endpoints migrated
- [x] Azure Storage integration
- [x] Service Bus queue setup
- [x] Job tracking with Cosmos DB
- [x] Deployment scripts
- [x] Configuration management

### In Progress 🔄
- [ ] File upload handling optimization
- [ ] Evidence analysis processing
- [ ] Error handling and retry logic
- [ ] Performance optimization

### Planned 📋
- [ ] WebSocket alternative (SignalR)
- [ ] Monitoring and logging
- [ ] Security enhancements
- [ ] Load testing
- [ ] Documentation updates

## Troubleshooting

### Common Issues

1. **Cold Start Performance**
   - Solution: Use Premium plan or keep functions warm

2. **File Upload Timeouts**
   - Solution: Implement chunked upload for large files

3. **Memory Limits**
   - Solution: Process files in smaller batches

4. **Rate Limiting**
   - Solution: Implement exponential backoff and retry logic

### Monitoring

- Use Azure Application Insights for monitoring
- Set up alerts for function failures
- Monitor Service Bus queue length
- Track Cosmos DB request units

## Next Steps

1. **Test the migrated endpoints** with your existing frontend
2. **Migrate remaining functionality** (evidence analysis)
3. **Implement WebSocket alternative** (Azure SignalR)
4. **Optimize performance** based on usage patterns
5. **Set up monitoring and alerts**

## Support

For issues and questions:
1. Check Azure Functions documentation
2. Review Application Insights logs
3. Test locally with Functions Core Tools
4. Validate Azure resource configurations
