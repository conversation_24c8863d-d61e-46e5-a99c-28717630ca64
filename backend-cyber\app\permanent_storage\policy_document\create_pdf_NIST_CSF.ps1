$fileName = "NIST CSF 2.0"
$title = "NIST CSF 2.0"

$content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1200 >>
stream
BT
/F1 24 Tf
100 700 Td
($title) Tj
/F2 12 Tf
0 -40 Td
(The NIST Cybersecurity Framework 2.0 is a voluntary framework developed by the) Tj
0 -20 Td
(National Institute of Standards and Technology to help organizations better) Tj
0 -20 Td
(manage and reduce cybersecurity risk.) Tj
0 -30 Td
/F1 14 Tf
(Core Functions:) Tj
/F2 12 Tf
0 -20 Td
(• IDENTIFY: Develop organizational understanding to manage cybersecurity risk) Tj
0 -20 Td
(  to systems, people, assets, data, and capabilities.) Tj
0 -20 Td
(• PROTECT: Develop and implement appropriate safeguards to ensure delivery) Tj
0 -20 Td
(  of critical services.) Tj
0 -20 Td
(• DETECT: Develop and implement appropriate activities to identify the) Tj
0 -20 Td
(  occurrence of a cybersecurity event.) Tj
0 -20 Td
(• RESPOND: Develop and implement appropriate activities to take action) Tj
0 -20 Td
(  regarding a detected cybersecurity incident.) Tj
0 -20 Td
(• RECOVER: Develop and implement appropriate activities to maintain plans for) Tj
0 -20 Td
(  resilience and to restore any capabilities or services that were impaired) Tj
0 -20 Td
(  due to a cybersecurity incident.) Tj
0 -30 Td
/F1 14 Tf
(New in CSF 2.0:) Tj
/F2 12 Tf
0 -20 Td
(• Enhanced governance guidance) Tj
0 -20 Td
(• Supply chain risk management) Tj
0 -20 Td
(• Expanded implementation examples) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1625
%%EOF
"@

# Write the PDF file
[System.IO.File]::WriteAllText("$fileName.pdf", $content)

Write-Host "Created PDF with content: $fileName.pdf"
