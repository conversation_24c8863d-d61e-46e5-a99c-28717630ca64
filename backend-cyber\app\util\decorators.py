from time import time


def calc_time(func):
    # Define a wrapper function that executes some logic before and after calling the original function
    def wrapper(*args, **kwargs):
        t1 = time()
        returned_value = func(*args, **kwargs)
        t2 = time()
        print(f"Time taken to execute {func.__name__}: {t2 - t1} seconds")
        return returned_value

    # Return the wrapper function
    return wrapper


def delay(delay_time):
    def decorator(func):
        def wrapper(*args, **kwargs):
            print(f"Sleeping for {delay_time} seconds before calling {func.__name__}")
            time.sleep(delay_time)
            return func(*args, **kwargs)

        return wrapper

    return decorator
