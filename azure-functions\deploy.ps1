# Azure Functions Deployment Script for IsecMapper
# Prerequisites: Azure CLI, Azure Functions Core Tools

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$FunctionAppName,
    
    [Parameter(Mandatory=$true)]
    [string]$StorageAccountName,
    
    [Parameter(Mandatory=$false)]
    [string]$Location = "East US",
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceBusNamespace = "",
    
    [Parameter(Mandatory=$false)]
    [string]$CosmosAccountName = ""
)

Write-Host "Starting Azure Functions deployment for IsecMapper..." -ForegroundColor Green

# Login to Azure (if not already logged in)
Write-Host "Checking Azure login status..." -ForegroundColor Yellow
$context = az account show 2>$null
if (-not $context) {
    Write-Host "Please login to Azure..." -ForegroundColor Yellow
    az login
}

# Create Resource Group
Write-Host "Creating resource group: $ResourceGroupName" -ForegroundColor Yellow
az group create --name $ResourceGroupName --location $Location

# Create Storage Account
Write-Host "Creating storage account: $StorageAccountName" -ForegroundColor Yellow
az storage account create `
    --name $StorageAccountName `
    --resource-group $ResourceGroupName `
    --location $Location `
    --sku Standard_LRS `
    --kind StorageV2

# Get storage connection string
$storageConnectionString = az storage account show-connection-string `
    --name $StorageAccountName `
    --resource-group $ResourceGroupName `
    --query connectionString `
    --output tsv

# Create containers in storage account
Write-Host "Creating storage containers..." -ForegroundColor Yellow
az storage container create --name "isecmapper-documents" --connection-string $storageConnectionString
az storage container create --name "temp-storage" --connection-string $storageConnectionString
az storage container create --name "evidence-files" --connection-string $storageConnectionString
az storage container create --name "policy-documents" --connection-string $storageConnectionString
az storage container create --name "vector-db" --connection-string $storageConnectionString

# Create Service Bus Namespace (if specified)
if ($ServiceBusNamespace) {
    Write-Host "Creating Service Bus namespace: $ServiceBusNamespace" -ForegroundColor Yellow
    az servicebus namespace create `
        --name $ServiceBusNamespace `
        --resource-group $ResourceGroupName `
        --location $Location `
        --sku Standard
    
    # Create queue for analysis jobs
    az servicebus queue create `
        --name "analysis-jobs" `
        --namespace-name $ServiceBusNamespace `
        --resource-group $ResourceGroupName
    
    # Get Service Bus connection string
    $serviceBusConnectionString = az servicebus namespace authorization-rule keys list `
        --name RootManageSharedAccessKey `
        --namespace-name $ServiceBusNamespace `
        --resource-group $ResourceGroupName `
        --query primaryConnectionString `
        --output tsv
}

# Create Cosmos DB Account (if specified)
if ($CosmosAccountName) {
    Write-Host "Creating Cosmos DB account: $CosmosAccountName" -ForegroundColor Yellow
    az cosmosdb create `
        --name $CosmosAccountName `
        --resource-group $ResourceGroupName `
        --location $Location `
        --kind GlobalDocumentDB
    
    # Create database and container
    az cosmosdb sql database create `
        --account-name $CosmosAccountName `
        --resource-group $ResourceGroupName `
        --name "IsecMapperDB"
    
    az cosmosdb sql container create `
        --account-name $CosmosAccountName `
        --resource-group $ResourceGroupName `
        --database-name "IsecMapperDB" `
        --name "Jobs" `
        --partition-key-path "/jobId"
    
    # Get Cosmos DB connection string
    $cosmosConnectionString = az cosmosdb keys list `
        --name $CosmosAccountName `
        --resource-group $ResourceGroupName `
        --type connection-strings `
        --query "connectionStrings[0].connectionString" `
        --output tsv
}

# Create Function App
Write-Host "Creating Function App: $FunctionAppName" -ForegroundColor Yellow
az functionapp create `
    --name $FunctionAppName `
    --resource-group $ResourceGroupName `
    --storage-account $StorageAccountName `
    --consumption-plan-location $Location `
    --runtime python `
    --runtime-version 3.11 `
    --functions-version 4

# Configure Function App Settings
Write-Host "Configuring Function App settings..." -ForegroundColor Yellow
az functionapp config appsettings set `
    --name $FunctionAppName `
    --resource-group $ResourceGroupName `
    --settings `
    "AZURE_STORAGE_CONNECTION_STRING=$storageConnectionString" `
    "AZURE_STORAGE_CONTAINER_NAME=isecmapper-documents"

if ($ServiceBusNamespace) {
    az functionapp config appsettings set `
        --name $FunctionAppName `
        --resource-group $ResourceGroupName `
        --settings `
        "AZURE_SERVICEBUS_CONNECTION_STRING=$serviceBusConnectionString"
}

if ($CosmosAccountName) {
    az functionapp config appsettings set `
        --name $FunctionAppName `
        --resource-group $ResourceGroupName `
        --settings `
        "AZURE_COSMOS_CONNECTION_STRING=$cosmosConnectionString"
}

# Deploy the function
Write-Host "Deploying function code..." -ForegroundColor Yellow
func azure functionapp publish $FunctionAppName --python

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Function App URL: https://$FunctionAppName.azurewebsites.net" -ForegroundColor Cyan

# Display important information
Write-Host "`n=== IMPORTANT CONFIGURATION ===" -ForegroundColor Magenta
Write-Host "Please update your local.settings.json with the following:" -ForegroundColor Yellow
Write-Host "AZURE_STORAGE_CONNECTION_STRING: $storageConnectionString"
if ($ServiceBusNamespace) {
    Write-Host "AZURE_SERVICEBUS_CONNECTION_STRING: $serviceBusConnectionString"
}
if ($CosmosAccountName) {
    Write-Host "AZURE_COSMOS_CONNECTION_STRING: $cosmosConnectionString"
}
Write-Host "`nDon't forget to add your Azure OpenAI credentials!" -ForegroundColor Red
