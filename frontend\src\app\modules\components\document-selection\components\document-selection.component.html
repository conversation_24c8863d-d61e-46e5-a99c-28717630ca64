<div class="main-container">
  <div class="container p-4">
    <h2>IsecMapper - Document Selection</h2>
  </div>

  <div class="container">
    <div class="row justify-content-md-center align-items-center">
      <div class="col-md-10 mx-2 justify-content-md-center">
        <p class="description">
          Select documents based on region and domain to generate risks and recommendations.
          You must make selections in both sections and select at least one document before proceeding.
        </p>
      </div>
    </div>

    <div class="row justify-content-md-center align-items-center mt-4">
      <div class="col-md-10 mx-2 justify-content-md-center">
        <div class="selection-container">
          <!-- Region Selection -->
          <div class="selection-section">
            <h3 class="section-title">Region-Based Selection</h3>

            <!-- Region Selection Component -->
            <div class="mt-3">
              <app-region-selection (selectionChanged)="onRegionSelectionChanged($event)"></app-region-selection>
            </div>

            <!-- No additional document selection here -->
          </div>

          <!-- Domain Selection -->
          <div class="selection-section mt-5">
            <h3 class="section-title">Domain-Based Selection</h3>

            <!-- Domain Selection Component -->
            <div class="mt-3">
              <app-domain-based-selection (selectionChanged)="onDomainSelectionChanged($event)"></app-domain-based-selection>
            </div>

            <!-- No additional document selection here -->
          </div>

          <!-- Summary and Navigation -->
          <div class="mt-5">
            <div class="summary-panel">
              <h4>Selection Summary</h4>
              <p>Total Documents Selected: {{ getTotalSelectedDocuments() }}</p>

              @if (regionSelectedDocuments.length > 0) {
                <div class="selection-summary">
                  <h5>Region Documents</h5>
                  <p>Selected: {{ regionSelectedDocuments.length }}</p>
                </div>
              }

              @if (domainSelectedDocuments.length > 0) {
                <div class="selection-summary">
                  <h5>Domain Documents</h5>
                  <p>Selected: {{ domainSelectedDocuments.length }}</p>
                </div>
              }
            </div>

            <!-- Navigation Button -->
            <div class="navigation-buttons mt-4">
              <button
                class="btn btn-primary"
                (click)="navigateToQuestionSelection()"
                [disabled]="getTotalSelectedDocuments() === 0">
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
