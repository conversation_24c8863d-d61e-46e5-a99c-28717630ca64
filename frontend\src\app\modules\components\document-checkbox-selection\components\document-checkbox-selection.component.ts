import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';

// Define document categories interface
interface DocumentCategories {
  Standard: string[];
  Guidelines: string[];
  Framework: string[];
  Regulation: string[];
  [key: string]: string[];
}

@Component({
  selector: 'app-document-checkbox-selection',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCheckboxModule,
    MatDividerModule,
    MatButtonModule,
    MatExpansionModule
  ],
  templateUrl: './document-checkbox-selection.component.html',
  styleUrls: ['./document-checkbox-selection.component.css']
})
export class DocumentCheckboxSelectionComponent implements OnInit {
  @Input() documents: DocumentCategories = {
    Standard: [],
    Guidelines: [],
    Framework: [],
    Regulation: []
  };

  @Output() selectionChanged = new EventEmitter<string[]>();

  selectedDocuments: { [key: string]: boolean } = {};

  // Track selection state for each category
  categorySelectionState: { [key: string]: boolean } = {
    Standard: false,
    Guidelines: false,
    Framework: false,
    Regulation: false
  };

  constructor() { }

  ngOnInit(): void {
    this.initializeSelectedDocuments();
  }

  initializeSelectedDocuments(): void {
    // Initialize all documents as selected by default
    for (const category in this.documents) {
      if (this.documents[category].length > 0) {
        for (const doc of this.documents[category]) {
          this.selectedDocuments[doc] = true;
        }
      }
    }

    // Update category selection states
    this.updateCategorySelectionStates();

    // Emit initial selection
    this.emitSelectedDocuments();
  }

  toggleDocument(document: string): void {
    this.selectedDocuments[document] = !this.selectedDocuments[document];

    // Update category selection states
    this.updateCategorySelectionStates();

    // Emit updated selection
    this.emitSelectedDocuments();
  }

  toggleCategory(category: string): void {
    const newState = !this.categorySelectionState[category];

    // Update all documents in this category
    for (const doc of this.documents[category]) {
      this.selectedDocuments[doc] = newState;
    }

    // Update category selection state
    this.categorySelectionState[category] = newState;

    // Emit updated selection
    this.emitSelectedDocuments();
  }

  updateCategorySelectionStates(): void {
    for (const category in this.documents) {
      if (this.documents[category].length > 0) {
        // Check if all documents in this category are selected
        const allSelected = this.documents[category].every((doc: string) => this.selectedDocuments[doc]);
        this.categorySelectionState[category] = allSelected;
      }
    }
  }

  emitSelectedDocuments(): void {
    const selected: string[] = [];

    for (const doc in this.selectedDocuments) {
      if (this.selectedDocuments[doc]) {
        selected.push(doc);
      }
    }

    this.selectionChanged.emit(selected);
  }

  selectAll(): void {
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.selectedDocuments[doc] = true;
      }
      this.categorySelectionState[category] = true;
    }

    this.emitSelectedDocuments();
  }

  deselectAll(): void {
    for (const category in this.documents) {
      for (const doc of this.documents[category]) {
        this.selectedDocuments[doc] = false;
      }
      this.categorySelectionState[category] = false;
    }

    this.emitSelectedDocuments();
  }

  getSelectedCount(): number {
    let count = 0;
    for (const doc in this.selectedDocuments) {
      if (this.selectedDocuments[doc]) {
        count++;
      }
    }
    return count;
  }

  getTotalCount(): number {
    let count = 0;
    for (const category in this.documents) {
      count += this.documents[category].length;
    }
    return count;
  }

  getCategorySelectedCount(category: string): number {
    let count = 0;
    for (const doc of this.documents[category]) {
      if (this.selectedDocuments[doc]) {
        count++;
      }
    }
    return count;
  }
}
