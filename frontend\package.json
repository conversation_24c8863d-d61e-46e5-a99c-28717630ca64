{"name": "isecmapper", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "build-prod": "ng build -c production"}, "private": true, "dependencies": {"@angular/animations": "^18.0.3", "@angular/cdk": "^18.0.3", "@angular/common": "^18.0.3", "@angular/compiler": "^18.0.3", "@angular/core": "^18.0.3", "@angular/forms": "^18.0.3", "@angular/material": "^18.0.3", "@angular/platform-browser": "^18.0.3", "@angular/platform-browser-dynamic": "^18.0.3", "@angular/router": "^18.0.3", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "byte-size": "^8.1.1", "chart.js": "^4.4.3", "file-saver": "^2.0.5", "ng2-charts": "^6.0.1", "rxjs": "~7.8.1", "tslib": "^2.6.3", "xlsx": "^0.18.5", "zone.js": "~0.14.7"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.4", "@angular/cli": "^18.0.4", "@angular/compiler-cli": "^18.0.3", "@types/byte-size": "^8.1.2", "@types/file-saver": "^2.0.7", "typescript": "~5.4.5"}}