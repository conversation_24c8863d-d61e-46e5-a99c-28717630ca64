import { Component, OnInit } from '@angular/core';
import { MaterialModule } from 'src/app/modules/material/material.module';

import {
  BreakpointObserver,
  Breakpoints,
  BreakpointState,
} from '@angular/cdk/layout';

import { SidebarViewService } from '../../services/sidebar.service';
import { AsyncPipe } from '@angular/common';
import { RouterOutlet, RouterLinkActive, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { GlobalDataService } from 'src/app/modules/models/gloabl_data.model';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    MaterialModule,
    AsyncPipe,
    RouterOutlet,
    CommonModule,
    RouterOutlet,
    RouterLink,
    RouterLinkActive,
  ],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.css',
})
export class SidebarComponent implements OnInit {

  displayDashboard: boolean = false;

  constructor(
    public sidebarViewService: SidebarViewService,
    private breakPoint: BreakpointObserver,
    private globalDataService: GlobalDataService,
  ) {}

  ngOnInit(): void {
    this.breakPoint
      .observe([Breakpoints.Medium, Breakpoints.Small, Breakpoints.XSmall])
      .subscribe((state: BreakpointState) => {
        if (state.matches) {
          this.sidebarViewService.closeSidebar();
        } else {
          this.sidebarViewService.openSidebar();
        }
      });

      if(this.globalDataService.getAnalysisData().length > 0){
        this.displayDashboard = true;
      }


  }

  updateDisplay(){
    if(this.globalDataService.getAnalysisData().length > 0){
      this.displayDashboard = true;
    }
  }
}
