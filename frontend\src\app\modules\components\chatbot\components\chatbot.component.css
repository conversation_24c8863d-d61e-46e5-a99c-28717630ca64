.backdrop {
  /* background-color: #30304598; */
  padding: 10px;
  /* border-radius: 10px; */

  border-radius: 10px;
  background: #252531;
  /* box-shadow:  5px 5px 0px #282839,
               -5px -5px 0px #383851; */
}

.main-chat-container {
  position: relative;

  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

}

.chats-container {
  padding-bottom: 2.25rem;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  position: relative;
  width: 100%;
  height: 100%;

}

.chat-header {
  flex-shrink: 0;
  position: sticky;
  z-index: 10;
  top: 0;
  font-weight: 600;
  padding: 0.5rem;
  background-color: #1b1b27;
  height: 3.5rem;
  margin-bottom: 0.375rem;
  align-items: center;
  justify-content: space-between;

}


.chat-body {
  /* width: 100%; */
  justify-content: center;
}

.message-container {
  max-width: 48rem;
  /* position: relative; */
  flex: 1 1 0%;
  gap: 0.75rem;

}

.avatar {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  margin-top: 0.300rem;
  border-radius: 100%;
}

.user-avatar {
  background-color: darkgrey;
}

.bot-avatar {
  background-color: lightblue;
}

.name {
  font-weight: bold;
  line-height: 1.5;
  word-wrap: break-word;

}

.message {
  line-height: 1.5;
  word-wrap: break-word;
}

.prompt-container {
  display: flex;
  justify-content: center;
  /* width: 100%; */
  overflow: hidden;

  /* display: flex;
  justify-content: center;

  align-items: center;
  position: relative;
  overflow: hidden;
  max-width: 48rem;
  margin-bottom: 2rem;
  border-radius: 1rem;
  border: 1px solid hsla(white, .2);
  background-color: #1b1b27;


  flex-direction: column; */

}

.prompt {
  max-width: 48rem;
  margin-bottom: 2rem;
  border-radius: 1rem;
  border: 1px solid hsla(white, .2);
  background-color: #1b1b27;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.prompt-textarea {
  resize: none;
  width: 100%;
  outline: none;
  border-width: 0;
  background: none;
  max-height: 200px;
  height: 52px;
  padding: 0.875rem 3rem 0.875rem 0.875rem;
  overflow-y: hidden;
  position: relative;
}

.sendbutton {
  opacity: .1;
  width: 1.75rem;
  height: 1.75rem;
  background-color: hsl(#1b1b27);
  border-radius: 0.5rem;
  border: 1px solid hsl(white);
  color: hsl(white);
  transition: opacity .15s cubic-bezier(.4, 0, .2, 1);

  position: absolute;
  right: 0.75rem;
  bottom: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
}


.custom-input-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  min-width: 600px;
}

.custom-input {
  background-color: #343a40;
  border-radius: 20px;
  padding: 0px;
  /* Adjusted padding */
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

.custom-input textarea {
  background-color: transparent;
  border: none;
  color: white;
  margin-right: auto;
  font-size: 16px;
  width: 100%;
  resize: none;
  /* Prevents resizing of the textarea */
  height: 58px;
  /* Adjusted height to match the input field */
  padding: 18px 20px;
  /* Adjusted padding to vertically center the text */
  overflow: hidden;
  /* Prevents scrollbar when the content is less */
}

.custom-input textarea::placeholder {
  color: #999;
  opacity: 1;
  /* Firefox fix for placeholder color */
}

.custom-input textarea:focus {
  outline: none;
  box-shadow: none;
}

.custom-input .search-icon {
  color: white;
  cursor: pointer;
  /* padding: 18px 20px; /* Adjusted padding to align with the textarea */
  margin-right: 40px;
  border-radius: 10px;
}

.btn {
  background: yellow;
  color: #333;
  border: none;
}