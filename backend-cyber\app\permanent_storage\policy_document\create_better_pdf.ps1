# Function to create a better formatted PDF
function Create-Better-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$Overview,
        [string]$KeyComponents,
        [string]$Implementation,
        [string]$Benefits
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    # Create a more complex PDF with better formatting
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R /F3 6 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 7 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Oblique
>>
endobj
7 0 obj
<< /Length 4000 >>
stream
BT
/F1 28 Tf
50 750 Td
($Title) Tj

/F2 12 Tf
0 -30 Td
(Document Version: 1.0) Tj
0 -15 Td
(Last Updated: April 2025) Tj

/F1 18 Tf
-10 -40 Td
(Description) Tj
/F2 12 Tf
10 -20 Td
($Description) Tj

/F1 18 Tf
-10 -30 Td
(Overview) Tj
/F2 12 Tf
10 -20 Td
($Overview) Tj

/F1 18 Tf
-10 -30 Td
(Key Components) Tj
/F2 12 Tf
10 -20 Td
($KeyComponents) Tj

/F1 18 Tf
-10 -30 Td
(Implementation Guidelines) Tj
/F2 12 Tf
10 -20 Td
($Implementation) Tj

/F1 18 Tf
-10 -30 Td
(Benefits) Tj
/F2 12 Tf
10 -20 Td
($Benefits) Tj

/F3 10 Tf
-20 -40 Td
(This document provides an overview of $Title. For complete and authoritative information,) Tj
0 -15 Td
(please refer to the official documentation and standards publications.) Tj
ET
endstream
endobj
xref
0 8
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
0000000444 00000 n
trailer
<< /Size 8
/Root 1 0 R
>>
startxref
4496
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created improved PDF: $safeFileName.pdf"
}

# Test with one file first
Create-Better-PDF -FileName "ISO/IEC 27001" `
    -Title "ISO/IEC 27001" `
    -Description "ISO/IEC 27001 is an international standard for information security management systems (ISMS). It specifies the requirements for establishing, implementing, maintaining, and continually improving an information security management system within the context of the organization's overall business risks." `
    -Overview "ISO/IEC 27001 was developed by the International Organization for Standardization (ISO) and the International Electrotechnical Commission (IEC). The current version was published in 2013, with minor updates in 2017 and 2022. It provides a systematic approach to managing sensitive company information, including financial data, intellectual property, employee details, and information entrusted by third parties. The standard is designed to be flexible enough to be applied to organizations of all types and sizes." `
    -KeyComponents "• Information Security Policies: Documented policies for information security
• Organization of Information Security: Assignment of security responsibilities
• Human Resource Security: Security aspects for employees joining, moving, and leaving
• Asset Management: Identification of information assets and defining protection responsibilities
• Access Control: Restriction of access rights to networks, systems, applications, functions, and data
• Cryptography: Encryption and key management
• Physical and Environmental Security: Protection of computer facilities
• Operations Security: Procedures and responsibilities for managing and operating information processing facilities
• Communications Security: Network security management and information transfer
• System Acquisition, Development and Maintenance: Security requirements of information systems
• Supplier Relationships: Information security in supplier relationships
• Information Security Incident Management: Reporting and management of security incidents
• Business Continuity Management: Protecting, maintaining and recovering business-critical processes and systems
• Compliance: Compliance with legal and contractual requirements" `
    -Implementation "Implementing ISO/IEC 27001 typically involves the following steps:

1. Define the scope of the ISMS
2. Develop an information security policy
3. Define a risk assessment methodology
4. Conduct a risk assessment
5. Manage identified risks
6. Select control objectives and controls to be implemented
7. Prepare a Statement of Applicability
8. Implement the selected controls
9. Measure the effectiveness of controls
10. Conduct internal ISMS audits
11. Management review of the ISMS
12. Implement corrective actions
13. Seek certification from an accredited certification body" `
    -Benefits "• Enhanced information security posture
• Structured approach to managing information security risks
• Increased reliability and security of systems and information
• Improved customer and business partner confidence
• Competitive advantage through demonstrated commitment to information security
• Legal and regulatory compliance
• Better integration of information security with business objectives
• Reduced costs associated with security incidents
• Improved organizational culture and security awareness
• Systematic approach to risk management"

Write-Host "Test PDF created successfully"
