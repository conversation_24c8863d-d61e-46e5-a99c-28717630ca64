<div class="main-container">

  @if (showSpinner) {
  <div class="spinner-container">
    <mat-spinner></mat-spinner>
  </div>
  }

  <div class="container p-4">
    <h2>ISecMapper</h2>
  </div>



  @if(display === "control-document"){
  <div class="container">
    <div class="row justify-content-md-center align-items-center ">
      <div class="col-md-6 mx-2 justify-content-md-center backdrop">
        <div class="header">
          <h4>Upload Control Questionnaire</h4>
        </div>
        <mat-divider></mat-divider>
        <div class="body py-4">
          <div class="form-group py-2">
            <input type="file" class="form-control-file" id="evidence" (change)="onControlDocChange($event)">

          </div>
          <span class="input-subtitle">Accepted Formats: .xlsx</span>
        </div>
      </div>
    </div>

  </div>
  @if (ControlDocument.length>0) {
  <div class="row justify-content-md-center align-items-center ">
    <div class="col-md-6 gy-4 justify-content-md-center backdrop">
      <div class="header">
        <h4>Selected Questionnaire</h4>
      </div>
      <mat-divider></mat-divider>
      <div class="body py-4">

        <div class="control-document">
          <div class="control-document-table">
            <div class="table-container p-4">
              <div class="d-flex justify-content-start" style="padding-bottom: 12px;">

              </div>
              <div class="table-inner-container">
                <table class="table mb-0 table-dark" style="color: wheat;">
                  <thead>
                    <tr>
                      <th>#</th>
                      <th>File Name</th>
                      <th>File Size</th>
                      <th></th>
                      <!-- Add more table headers as needed -->
                    </tr>
                  </thead>
                  <tbody>
                    @for (item of ControlDocument; track item; let i = $index) {
                    <tr>
                      <td>{{i+1}}</td>
                      <td>{{ item.name}}</td>
                      <td>{{ changeByteSize(item.size) }}</td>
                      <td><mat-icon (click)="deleteControl(i)">delete_forever</mat-icon></td>
                      <!-- Display other properties as needed -->
                    </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <div class="row justify-content-md-center align-items-center ">
          <div class="col-md-2 my-2 justify-content-md-center ">
            <div class="body">
              <div class="form-group">
                <button mat-button-raised class="btn btn-primary"
                  (click)="changeDisplay('upload-evidence')">Next</button>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
  }
  }

  @if(display === "upload-evidence"){
  <div class="container">
    <div class="row justify-content-md-center align-items-center">
      <div class="col-md-10 mx-2 justify-content-md-center backdrop">
        <div class="header">
          <h4><mat-icon class="mr-2 align-middle">cloud_upload</mat-icon> Upload Evidence Files by Domain</h4>
        </div>
        <mat-divider></mat-divider>

        @if (domainList.length === 0) {
          <div class="body py-5 text-center">
            <p class="lead">Loading domains from questionnaire...</p>
            <mat-spinner style="margin: 0 auto;"></mat-spinner>
          </div>
        } @else {
          <div class="body py-4">
            <div class="alert alert-info mb-4">
              <mat-icon class="mr-2 align-middle">info</mat-icon>
              <span>Please upload evidence files for each domain found in your questionnaire. You need to upload at least one file for any domain to proceed.</span>
            </div>

            <div class="domain-progress mb-4">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span><strong>Domain Upload Progress:</strong></span>
                <span>{{getCompletedDomainCount()}} of {{domainList.length}} domains have files</span>
              </div>
              <div class="progress">
                <div class="progress-bar bg-success" role="progressbar"
                     [style.width.%]="(getCompletedDomainCount() / domainList.length) * 100"
                     [attr.aria-valuenow]="getCompletedDomainCount()"
                     aria-valuemin="0" [attr.aria-valuemax]="domainList.length">
                  {{Math.round((getCompletedDomainCount() / domainList.length) * 100)}}%
                </div>
              </div>
            </div>

            <mat-accordion multi="true" class="domain-accordion">
              @for (domain of domainList; track domain) {
                <mat-expansion-panel class="mb-3" [ngClass]="{'has-files': getDomainEvidenceCount(domain) > 0}">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      <span class="domain-name">{{domain}}</span>
                    </mat-panel-title>
                    <mat-panel-description>
                      <span class="file-count" [ngClass]="{'text-success': getDomainEvidenceCount(domain) > 0}">
                        <mat-icon class="mr-1 align-middle">{{getDomainEvidenceCount(domain) > 0 ? 'check_circle' : 'folder_open'}}</mat-icon>
                        {{getDomainEvidenceCount(domain)}} files
                      </span>
                    </mat-panel-description>
                  </mat-expansion-panel-header>

                  <!-- Domain questions collapsible section -->
                  <div class="domain-questions mb-3">
                    <mat-expansion-panel class="questions-panel">
                      <mat-expansion-panel-header>
                        <mat-panel-title>
                          <span>View Questions ({{getDomainQuestionsCount(domain)}})</span>
                        </mat-panel-title>
                      </mat-expansion-panel-header>

                      <div class="questions-list">
                        <ul class="list-group">
                          @for (question of getDomainQuestions(domain, 5); track question; let i = $index) {
                            <li class="list-group-item">{{question}}</li>
                          }
                        </ul>

                        @if (getDomainQuestionsCount(domain) > 5) {
                          <div class="text-center mt-2">
                            <button class="btn btn-sm btn-outline-secondary" (click)="showAllDomainQuestions(domain)">
                              Show more ({{getDomainQuestionsCount(domain) - 5}} more)
                            </button>
                          </div>
                        }
                      </div>
                    </mat-expansion-panel>
                  </div>

                  <div class="domain-evidence-upload">
                    <div class="form-group py-2">
                      <div class="custom-file-upload">
                        <div class="upload-zone p-4 text-center mb-3" (click)="triggerFileInput(domain)">
                          <mat-icon class="upload-icon">cloud_upload</mat-icon>
                          <h5>Upload evidence files for {{domain}}</h5>
                          <p class="text-muted">Click to browse or drag and drop files here</p>
                          <input type="file" #fileInput id="file-input-{{domain}}" class="file-input"
                                 (change)="onDomainEvidenceChange($event, domain)" multiple>
                        </div>
                        <div class="accepted-formats text-center mb-3">
                          <span class="badge badge-light mr-2">PDF</span>
                          <span class="badge badge-light mr-2">DOCX</span>
                          <span class="badge badge-light mr-2">JPG</span>
                          <span class="badge badge-light">PNG</span>
                        </div>
                      </div>
                    </div>

                    @if (domainEvidences[domain] && domainEvidences[domain].length > 0) {
                      <div class="file-list mt-3">
                        <h6 class="mb-3">Uploaded Files ({{domainEvidences[domain].length}})</h6>
                        <div class="table-responsive">
                          <table class="table table-dark table-hover" style="color: wheat;">
                            <thead>
                              <tr>
                                <th>#</th>
                                <th>File Name</th>
                                <th>File Size</th>
                                <th>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              @for (item of domainEvidences[domain]; track item; let i = $index) {
                                <tr>
                                  <td>{{i+1}}</td>
                                  <td>
                                    <div class="d-flex align-items-center">
                                      <mat-icon class="mr-2">{{getFileIcon(item.name)}}</mat-icon>
                                      {{ item.name }}
                                    </div>
                                  </td>
                                  <td>{{ changeByteSize(item.size) }}</td>
                                  <td>
                                    <button class="btn btn-sm btn-danger" (click)="deleteDomainEvidence(domain, i)">
                                      <mat-icon>delete_forever</mat-icon>
                                    </button>
                                  </td>
                                </tr>
                              }
                            </tbody>
                          </table>
                        </div>
                      </div>
                    } @else {
                      <div class="no-files-message text-center p-3 mt-3">
                        <mat-icon class="text-muted">folder_open</mat-icon>
                        <p class="text-muted mb-0">No files uploaded for this domain yet</p>
                      </div>
                    }
                  </div>
                </mat-expansion-panel>
              }
            </mat-accordion>

            <div class="row justify-content-md-center align-items-center mt-5">
              <div class="col-md-6 my-2 justify-content-md-center text-center">
                <button class="btn btn-lg btn-primary" [disabled]="!hasAnyEvidence()" (click)="uploadFile()">
                  <mat-icon class="mr-2">analytics</mat-icon>
                  Perform Analysis
                </button>
                @if (!hasAnyEvidence()) {
                  <p class="text-muted mt-2">
                    <small>Please upload at least one evidence file to proceed</small>
                  </p>
                }
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  </div>
  }

</div>