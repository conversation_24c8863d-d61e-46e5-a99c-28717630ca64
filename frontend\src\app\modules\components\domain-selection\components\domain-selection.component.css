.main-container {
  padding: 0;
  background-size: cover;
  min-height: 100vh;
}

.backdrop {
  padding: 10px;
  border-radius: 10px;
  background: #252531;
}

.header {
  margin-bottom: 10px;
}

.description {
  color: #ccc;
  margin-bottom: 20px;
}

.domain-grid, .benchmark-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.domain-item, .benchmark-item {
  padding: 10px;
  border-radius: 5px;
  background-color: #333340;
}

.btn {
  background: yellow;
  color: #333;
  border: none;
  padding: 8px 20px;
  font-weight: 500;
}

.btn:disabled {
  background: #888;
  color: #333;
}
