import json
import logging
import os
import tempfile
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from azure.cosmos import CosmosClient
from azure.storage.blob import BlobServiceClient
from azure.servicebus import ServiceBusClient, ServiceBusMessage
from .config import configs


class JobProcessor:
    """Handles job processing for evidence analysis in Azure Functions environment"""
    
    def __init__(self):
        self.cosmos_client = None
        self.blob_client = configs.blob_service_client
        self.servicebus_client = configs.service_bus_client
        
        # Initialize Cosmos DB client if connection string is available
        if configs.AZURE_COSMOS_CONNECTION_STRING:
            self.cosmos_client = CosmosClient.from_connection_string(
                configs.AZURE_COSMOS_CONNECTION_STRING
            )
            self.database = self.cosmos_client.get_database_client("IsecMapperDB")
            self.container = self.database.get_container_client("Jobs")
    
    def create_job(self, job_data: Dict[str, Any]) -> str:
        """Create a new analysis job and queue it for processing"""
        try:
            job_id = str(uuid.uuid4())
            
            # Create job record in Cosmos DB
            job_record = {
                "id": job_id,
                "jobId": job_id,
                "status": "queued",
                "progress": 0,
                "message": "Job queued for processing",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "job_data": job_data
            }
            
            if self.cosmos_client:
                self.container.create_item(job_record)
            
            # Queue the job for processing
            self.queue_analysis_job(job_id, job_data)
            
            return job_id
            
        except Exception as e:
            logging.error(f"Error creating job: {str(e)}")
            raise
    
    def queue_analysis_job(self, job_id: str, job_data: Dict[str, Any]):
        """Queue an analysis job in Service Bus"""
        try:
            if not self.servicebus_client:
                logging.warning("Service Bus client not available, skipping queue operation")
                return
            
            message_data = {
                "job_id": job_id,
                "job_data": job_data
            }
            
            with self.servicebus_client:
                sender = self.servicebus_client.get_queue_sender(queue_name="analysis-jobs")
                with sender:
                    message = ServiceBusMessage(json.dumps(message_data))
                    sender.send_messages(message)
                    
            logging.info(f"Job {job_id} queued successfully")
            
        except Exception as e:
            logging.error(f"Error queuing job {job_id}: {str(e)}")
            raise
    
    def update_job_status(self, job_id: str, status: str, result: Optional[Dict] = None, error: Optional[str] = None):
        """Update job status in Cosmos DB"""
        try:
            if not self.cosmos_client:
                logging.warning("Cosmos DB client not available, skipping status update")
                return
            
            # Get current job record
            job_record = self.container.read_item(item=job_id, partition_key=job_id)
            
            # Update status and timestamp
            job_record["status"] = status
            job_record["updated_at"] = datetime.utcnow().isoformat()
            
            # Update progress based on status
            if status == "processing":
                job_record["progress"] = 10
                job_record["message"] = "Analysis in progress..."
            elif status == "completed":
                job_record["progress"] = 100
                job_record["message"] = "Analysis completed successfully"
                if result:
                    job_record["result"] = result
            elif status == "failed":
                job_record["progress"] = 0
                job_record["message"] = f"Analysis failed: {error}" if error else "Analysis failed"
                if error:
                    job_record["error"] = error
            
            # Update the record
            self.container.replace_item(item=job_id, body=job_record)
            
            logging.info(f"Job {job_id} status updated to {status}")
            
        except Exception as e:
            logging.error(f"Error updating job status for {job_id}: {str(e)}")
            # Don't raise here to avoid failing the main processing
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status from Cosmos DB"""
        try:
            if not self.cosmos_client:
                return {
                    "job_id": job_id,
                    "status": "unknown",
                    "message": "Job tracking not available"
                }
            
            job_record = self.container.read_item(item=job_id, partition_key=job_id)
            
            return {
                "job_id": job_id,
                "status": job_record.get("status", "unknown"),
                "progress": job_record.get("progress", 0),
                "message": job_record.get("message", ""),
                "created_at": job_record.get("created_at"),
                "updated_at": job_record.get("updated_at"),
                "result": job_record.get("result"),
                "error": job_record.get("error")
            }
            
        except Exception as e:
            logging.error(f"Error getting job status for {job_id}: {str(e)}")
            return {
                "job_id": job_id,
                "status": "error",
                "message": f"Error retrieving job status: {str(e)}"
            }
    
    def process_evidence_analysis(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process evidence analysis job"""
        try:
            logging.info("Starting evidence analysis processing")
            
            # Extract job parameters
            files_info = job_data.get("files", [])
            selected_benchmarks = job_data.get("selected_benchmarks", [])
            question_method = job_data.get("question_method")
            hardcoded_questions = job_data.get("hardcoded_questions")
            domain_evidence_mapping = job_data.get("domain_evidence_mapping")
            
            # Download files from Blob Storage to temporary location
            temp_dir = tempfile.mkdtemp()
            excel_file = None
            evidence_files = []
            
            try:
                for file_info in files_info:
                    blob_name = file_info.get("blob_name")
                    file_name = file_info.get("file_name")
                    file_extension = file_name.split(".")[-1].lower()
                    
                    # Download file from blob storage
                    local_path = os.path.join(temp_dir, file_name)
                    self.download_blob_to_file(blob_name, local_path)
                    
                    if file_extension == "xlsx":
                        excel_file = local_path
                    else:
                        evidence_files.append(local_path)
                
                if not excel_file:
                    raise ValueError("No Excel questionnaire file found")
                
                # Import and run the analysis (you'll need to adapt your existing code)
                # from .evidence_main import perform_analysis
                
                # For now, return a placeholder result
                result = {
                    "analysis_id": str(uuid.uuid4()),
                    "processed_questions": 10,
                    "total_questions": 10,
                    "evidence_files_processed": len(evidence_files),
                    "results": [
                        {
                            "question": "Sample question",
                            "compliance_status": "Compliant",
                            "risk": "Low",
                            "recommendation": "Continue current practices"
                        }
                    ]
                }
                
                logging.info("Evidence analysis completed successfully")
                return result
                
            finally:
                # Clean up temporary files
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                
        except Exception as e:
            logging.error(f"Error in evidence analysis: {str(e)}")
            raise
    
    def download_blob_to_file(self, blob_name: str, local_path: str):
        """Download a blob to a local file"""
        try:
            if not self.blob_client:
                raise ValueError("Blob client not available")
            
            blob_client = self.blob_client.get_blob_client(
                container=configs.AZURE_STORAGE_CONTAINER_NAME,
                blob=blob_name
            )
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            with open(local_path, "wb") as download_file:
                download_file.write(blob_client.download_blob().readall())
                
            logging.info(f"Downloaded blob {blob_name} to {local_path}")
            
        except Exception as e:
            logging.error(f"Error downloading blob {blob_name}: {str(e)}")
            raise
    
    def upload_file_to_blob(self, local_path: str, blob_name: str) -> str:
        """Upload a local file to blob storage"""
        try:
            if not self.blob_client:
                raise ValueError("Blob client not available")
            
            blob_client = self.blob_client.get_blob_client(
                container=configs.AZURE_STORAGE_CONTAINER_NAME,
                blob=blob_name
            )
            
            with open(local_path, "rb") as data:
                blob_client.upload_blob(data, overwrite=True)
            
            logging.info(f"Uploaded file {local_path} to blob {blob_name}")
            return blob_name
            
        except Exception as e:
            logging.error(f"Error uploading file to blob: {str(e)}")
            raise
