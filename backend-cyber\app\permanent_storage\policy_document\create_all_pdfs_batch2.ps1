# Define a function to create a PDF with content
function Create-PDF {
    param(
        [string]$FileName,
        [string]$Title,
        [string]$Description,
        [string]$KeyPoints
    )
    
    # Replace invalid characters in filename
    $safeFileName = $FileName -replace '/', '-' -replace '@', 'at'
    
    $content = @"
%PDF-1.4
1 0 obj
<< /Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<< /Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<< /Type /Page
/Parent 2 0 R
/Resources << /Font << /F1 4 0 R /F2 5 0 R >> >>
/MediaBox [0 0 612 792]
/Contents 6 0 R
>>
endobj
4 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica-Bold
>>
endobj
5 0 obj
<< /Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
6 0 obj
<< /Length 1500 >>
stream
BT
/F1 24 Tf
100 700 Td
($Title) Tj
/F2 12 Tf
0 -40 Td
($Description) Tj
0 -30 Td
/F1 14 Tf
(Key Components:) Tj
/F2 12 Tf
0 -20 Td
($KeyPoints) Tj
ET
endstream
endobj
xref
0 7
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000234 00000 n
0000000306 00000 n
0000000373 00000 n
trailer
<< /Size 7
/Root 1 0 R
>>
startxref
1925
%%EOF
"@

    # Write the PDF file
    [System.IO.File]::WriteAllText("$safeFileName.pdf", $content)
    Write-Host "Created PDF with content: $safeFileName.pdf"
}

# 6. SOC 2 (AICPA)
Create-PDF -FileName "SOC 2 (AICPA)" `
    -Title "SOC 2 (AICPA)" `
    -Description "SOC 2 is a framework developed by the American Institute of CPAs (AICPA) that defines criteria for managing customer data based on five 'trust service principles'." `
    -KeyPoints "• Security: Protection against unauthorized access
• Availability: System availability for operation and use
• Processing Integrity: System processing is complete, accurate, timely, and authorized
• Confidentiality: Information designated as confidential is protected
• Privacy: Personal information is collected, used, retained, disclosed, and disposed of properly"

# 7. MITRE ATT@CK
Create-PDF -FileName "MITRE ATT@CK" `
    -Title "MITRE ATT&CK" `
    -Description "MITRE ATT&CK is a globally-accessible knowledge base of adversary tactics and techniques based on real-world observations. It provides a common language for describing cyber attacks and defenses." `
    -KeyPoints "• Initial Access: Techniques for getting into your network
• Execution: Techniques to run malicious code
• Persistence: Techniques to maintain access
• Privilege Escalation: Techniques to gain higher-level permissions
• Defense Evasion: Techniques to avoid detection
• Credential Access: Techniques to steal credentials
• Discovery: Techniques to learn about the environment
• Lateral Movement: Techniques to move through the environment
• Collection: Techniques to gather data of interest
• Command and Control: Techniques for communication with compromised systems
• Exfiltration: Techniques to steal data
• Impact: Techniques to manipulate, interrupt, or destroy systems and data"

# 8. AWS Security Practice
Create-PDF -FileName "AWS Security Practice" `
    -Title "AWS Security Practice" `
    -Description "AWS Security Practice refers to the best practices and guidelines for securing resources and workloads in the Amazon Web Services cloud environment." `
    -KeyPoints "• Shared Responsibility Model
• Identity and Access Management (IAM)
• Detective Controls
• Infrastructure Protection
• Data Protection
• Incident Response
• Compliance Validation
• Resilience
• Security Monitoring and Automation
• Well-Architected Security Pillar"

# 9. Azure Security Practice
Create-PDF -FileName "Azure Security Practice" `
    -Title "Azure Security Practice" `
    -Description "Azure Security Practice refers to the best practices and guidelines for securing resources and workloads in the Microsoft Azure cloud environment." `
    -KeyPoints "• Azure Security Center
• Identity and Access Management
• Network Security
• Data Protection
• Security Monitoring
• Threat Protection
• Azure Policy and Compliance
• Azure Key Vault
• Azure Information Protection
• Azure Sentinel
• Security Baselines"

# 10. GCP Security Practice
Create-PDF -FileName "GCP Security Practice" `
    -Title "GCP Security Practice" `
    -Description "GCP Security Practice refers to the best practices and guidelines for securing resources and workloads in the Google Cloud Platform environment." `
    -KeyPoints "• Cloud Identity and Access Management
• Resource Hierarchy and Organization Policy
• Network Security
• Data Protection
• Security Command Center
• Cloud Logging and Monitoring
• Key Management
• Compliance and Regulatory Requirements
• Security by Design
• Shared Responsibility Model
• Security Operations"

Write-Host "Created second batch of PDFs (6-10)"
