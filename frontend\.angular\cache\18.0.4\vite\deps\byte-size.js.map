{"version": 3, "sources": ["../../../../../node_modules/byte-size/index.js"], "sourcesContent": ["/**\n * @module byte-size\n */\n\nlet defaultOptions = {}\nconst _options = new WeakMap()\n\nconst referenceTables = {\n  metric: [\n    { from: 0, to: 1e3, unit: 'B', long: 'bytes' },\n    { from: 1e3, to: 1e6, unit: 'kB', long: 'kilobytes' },\n    { from: 1e6, to: 1e9, unit: 'MB', long: 'megabytes' },\n    { from: 1e9, to: 1e12, unit: 'GB', long: 'gigabytes' },\n    { from: 1e12, to: 1e15, unit: 'TB', long: 'terabytes' },\n    { from: 1e15, to: 1e18, unit: 'PB', long: 'petabytes' },\n    { from: 1e18, to: 1e21, unit: 'EB', long: 'exabytes' },\n    { from: 1e21, to: 1e24, unit: 'ZB', long: 'zettabytes' },\n    { from: 1e24, to: 1e27, unit: 'YB', long: 'yottabytes' }\n  ],\n  metric_octet: [\n    { from: 0, to: 1e3, unit: 'o', long: 'octets' },\n    { from: 1e3, to: 1e6, unit: 'ko', long: 'kilooctets' },\n    { from: 1e6, to: 1e9, unit: 'Mo', long: 'megaoctets' },\n    { from: 1e9, to: 1e12, unit: 'Go', long: 'gigaoctets' },\n    { from: 1e12, to: 1e15, unit: 'To', long: 'teraoctets' },\n    { from: 1e15, to: 1e18, unit: 'Po', long: 'petaoctets' },\n    { from: 1e18, to: 1e21, unit: 'Eo', long: 'exaoctets' },\n    { from: 1e21, to: 1e24, unit: 'Zo', long: 'zettaoctets' },\n    { from: 1e24, to: 1e27, unit: 'Yo', long: 'yottaoctets' }\n  ],\n  iec: [\n    { from: 0, to: Math.pow(1024, 1), unit: 'B', long: 'bytes' },\n    { from: Math.pow(1024, 1), to: Math.pow(1024, 2), unit: 'KiB', long: 'kibibytes' },\n    { from: Math.pow(1024, 2), to: Math.pow(1024, 3), unit: 'MiB', long: 'mebibytes' },\n    { from: Math.pow(1024, 3), to: Math.pow(1024, 4), unit: 'GiB', long: 'gibibytes' },\n    { from: Math.pow(1024, 4), to: Math.pow(1024, 5), unit: 'TiB', long: 'tebibytes' },\n    { from: Math.pow(1024, 5), to: Math.pow(1024, 6), unit: 'PiB', long: 'pebibytes' },\n    { from: Math.pow(1024, 6), to: Math.pow(1024, 7), unit: 'EiB', long: 'exbibytes' },\n    { from: Math.pow(1024, 7), to: Math.pow(1024, 8), unit: 'ZiB', long: 'zebibytes' },\n    { from: Math.pow(1024, 8), to: Math.pow(1024, 9), unit: 'YiB', long: 'yobibytes' }\n  ],\n  iec_octet: [\n    { from: 0, to: Math.pow(1024, 1), unit: 'o', long: 'octets' },\n    { from: Math.pow(1024, 1), to: Math.pow(1024, 2), unit: 'Kio', long: 'kibioctets' },\n    { from: Math.pow(1024, 2), to: Math.pow(1024, 3), unit: 'Mio', long: 'mebioctets' },\n    { from: Math.pow(1024, 3), to: Math.pow(1024, 4), unit: 'Gio', long: 'gibioctets' },\n    { from: Math.pow(1024, 4), to: Math.pow(1024, 5), unit: 'Tio', long: 'tebioctets' },\n    { from: Math.pow(1024, 5), to: Math.pow(1024, 6), unit: 'Pio', long: 'pebioctets' },\n    { from: Math.pow(1024, 6), to: Math.pow(1024, 7), unit: 'Eio', long: 'exbioctets' },\n    { from: Math.pow(1024, 7), to: Math.pow(1024, 8), unit: 'Zio', long: 'zebioctets' },\n    { from: Math.pow(1024, 8), to: Math.pow(1024, 9), unit: 'Yio', long: 'yobioctets' }\n  ]\n}\n\nclass ByteSize {\n  constructor (bytes, options) {\n    options = Object.assign({\n      units: 'metric',\n      precision: 1,\n      locale: undefined // Default to the user's system locale\n    }, defaultOptions, options)\n    _options.set(this, options)\n\n    Object.assign(referenceTables, options.customUnits)\n\n    const prefix = bytes < 0 ? '-' : ''\n    bytes = Math.abs(bytes)\n    const table = referenceTables[options.units]\n    if (table) {\n      const units = table.find(u => bytes >= u.from && bytes < u.to)\n      if (units) {\n        const defaultFormat = new Intl.NumberFormat(options.locale, {\n          style: 'decimal',\n          minimumFractionDigits: options.precision,\n          maximumFractionDigits: options.precision\n        })\n        const value = units.from === 0\n          ? prefix + bytes\n          : prefix + defaultFormat.format(bytes / units.from)\n        this.value = value\n        this.unit = units.unit\n        this.long = units.long\n      } else {\n        this.value = prefix + bytes\n        this.unit = ''\n        this.long = ''\n      }\n    } else {\n      throw new Error(`Invalid units specified: ${options.units}`)\n    }\n  }\n\n  toString () {\n    const options = _options.get(this)\n    return options.toStringFn ? options.toStringFn.bind(this)() : `${this.value} ${this.unit}`\n  }\n}\n\n/**\n * Returns an object with the spec `{ value: string, unit: string, long: string }`. The returned object defines a `toString` method meaning it can be used in any string context.\n * @param {number} - The bytes value to convert.\n * @param [options] {object} - Optional config.\n * @param [options.precision] {number} - Number of decimal places. Defaults to `1`.\n * @param [options.units] {string} - Specify `'metric'`, `'iec'`, `'metric_octet'`, `'iec_octet'` or the name of a property from the custom units table in `options.customUnits`. Defaults to `metric`.\n * @param [options.customUnits] {object} - An object containing one or more custom unit lookup tables.\n * @param [options.toStringFn] {function} - A `toString` function to override the default.\n * @param [options.locale] {string|string[]} - *Node >=13 or modern browser only - on earlier platforms this option is ignored*. The locale to use for number formatting (e.g. `'de-DE'`). Defaults to your system locale. Passed directed into [Intl.NumberFormat()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat).\n * @returns {object}\n * @alias module:byte-size\n */\nfunction byteSize (bytes, options) {\n  return new ByteSize(bytes, options)\n}\n\n/**\n * Set the default `byteSize` options for the duration of the process.\n * @param options {object} - A `byteSize` options object.\n */\nbyteSize.defaultOptions = function (options) {\n  defaultOptions = options\n}\n\nexport default byteSize\n"], "mappings": ";;;AAIA,IAAI,iBAAiB,CAAC;AACtB,IAAM,WAAW,oBAAI,QAAQ;AAE7B,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,EAAE,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,MAAM,QAAQ;AAAA,IAC7C,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,YAAY;AAAA,IACpD,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,YAAY;AAAA,IACpD,EAAE,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AAAA,IACrD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AAAA,IACtD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AAAA,IACtD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,WAAW;AAAA,IACrD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACvD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,EACzD;AAAA,EACA,cAAc;AAAA,IACZ,EAAE,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,MAAM,SAAS;AAAA,IAC9C,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,aAAa;AAAA,IACrD,EAAE,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,aAAa;AAAA,IACrD,EAAE,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACtD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACvD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACvD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AAAA,IACtD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,cAAc;AAAA,IACxD,EAAE,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,cAAc;AAAA,EAC1D;AAAA,EACA,KAAK;AAAA,IACH,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,KAAK,MAAM,QAAQ;AAAA,IAC3D,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,IACjF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,YAAY;AAAA,EACnF;AAAA,EACA,WAAW;AAAA,IACT,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,KAAK,MAAM,SAAS;AAAA,IAC5D,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,IAClF,EAAE,MAAM,KAAK,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,aAAa;AAAA,EACpF;AACF;AAEA,IAAM,WAAN,MAAe;AAAA,EACb,YAAa,OAAO,SAAS;AAC3B,cAAU,OAAO,OAAO;AAAA,MACtB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA;AAAA,IACV,GAAG,gBAAgB,OAAO;AAC1B,aAAS,IAAI,MAAM,OAAO;AAE1B,WAAO,OAAO,iBAAiB,QAAQ,WAAW;AAElD,UAAM,SAAS,QAAQ,IAAI,MAAM;AACjC,YAAQ,KAAK,IAAI,KAAK;AACtB,UAAM,QAAQ,gBAAgB,QAAQ,KAAK;AAC3C,QAAI,OAAO;AACT,YAAM,QAAQ,MAAM,KAAK,OAAK,SAAS,EAAE,QAAQ,QAAQ,EAAE,EAAE;AAC7D,UAAI,OAAO;AACT,cAAM,gBAAgB,IAAI,KAAK,aAAa,QAAQ,QAAQ;AAAA,UAC1D,OAAO;AAAA,UACP,uBAAuB,QAAQ;AAAA,UAC/B,uBAAuB,QAAQ;AAAA,QACjC,CAAC;AACD,cAAM,QAAQ,MAAM,SAAS,IACzB,SAAS,QACT,SAAS,cAAc,OAAO,QAAQ,MAAM,IAAI;AACpD,aAAK,QAAQ;AACb,aAAK,OAAO,MAAM;AAClB,aAAK,OAAO,MAAM;AAAA,MACpB,OAAO;AACL,aAAK,QAAQ,SAAS;AACtB,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MACd;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,4BAA4B,QAAQ,KAAK,EAAE;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,WAAY;AACV,UAAM,UAAU,SAAS,IAAI,IAAI;AACjC,WAAO,QAAQ,aAAa,QAAQ,WAAW,KAAK,IAAI,EAAE,IAAI,GAAG,KAAK,KAAK,IAAI,KAAK,IAAI;AAAA,EAC1F;AACF;AAcA,SAAS,SAAU,OAAO,SAAS;AACjC,SAAO,IAAI,SAAS,OAAO,OAAO;AACpC;AAMA,SAAS,iBAAiB,SAAU,SAAS;AAC3C,mBAAiB;AACnB;AAEA,IAAO,oBAAQ;", "names": []}