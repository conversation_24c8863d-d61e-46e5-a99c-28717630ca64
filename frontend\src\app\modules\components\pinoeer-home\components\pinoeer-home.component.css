.container-fluid {
    padding: 0;
    overflow: hidden;

    background: linear-gradient(rgba(0, 0, 0, 0.5),
            rgba(0, 0, 0, 0.5)), url('../../../../../assets/images/pinnoer-bg.jpg') no-repeat center center;

    background-size: cover;
    height: 100vh;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    vertical-align: middle;
    overflow: hidden;


}

.left-pane {
    vertical-align: middle;
    margin-top: calc(50vh - 250px);
    color: white;
    padding: 50px;
    display: flex;
    
}



.right-pane {
    vertical-align: middle;
    margin-top: calc(50vh - 250px);
    background: #333;
    color: #fff;
    padding-top: 50px;
    padding-left: 50px;
    padding-right: 50px;
    padding-bottom: 50px;
    display: flex;
    border-radius: 10px;
    border: 2px solid #333;


}

.right-pane .btn {
    background: yellow;
    color: #333;
    border: none;
    font-weight: bold ;
}