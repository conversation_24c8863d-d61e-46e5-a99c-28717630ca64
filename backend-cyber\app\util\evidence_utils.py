from fastapi.responses import FileResponse
import pandas as pd
import os
import glob
from langchain_community.document_loaders import PyPDFLoader
from langchain_community.document_loaders import Docx2txtLoader
from langchain_community.document_loaders import TextLoader
import base64
from mimetypes import guess_type
from typing import List, Optional
import fitz  # PyMuPDF
from langchain.docstore.document import Document


# custom imports
from app.core.config import configs
from app.util.utils import create_dir
from app.util.decorators import calc_time
from app.util.azure_blob_storage import (
    upload_file_to_blob,
    download_blob_to_file,
    list_blobs
)


@calc_time
def write_to_excel(data, filename):
    path = os.path.join(
        configs.curr_dir, configs.TEMP_CONSTANT, configs.EVIDENCE_DOWNLOAD_TEMP_STORAGE
    )

    create_dir(path)

    modified_data = []

    for record in data:
        record["Evidence Files"] = ",".join(record["evidence_files"])
        modified_item = {
            "Control ID": record["control_id"],
            "Control": record["control"],
            "Observation": record["observation"],
            "Risk": record["risk"],
            "Recommendation": record["Recommendation"],
            "Compliant": record["compliant"],
        }
        modified_data.append(modified_item)

    file_path = os.path.join(path, filename)

    df = pd.DataFrame(modified_data)
    df.to_excel(file_path, index=False)

    # media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

    with open(file_path, "rb") as file:
        file_content = file.read()  # noqa: F841

    return FileResponse(path=file_path, filename=filename)


@calc_time
def load_pdf_with_pymupdf(file_path):
    """
    Load a PDF file using PyMuPDF (faster and more robust than PyPDFLoader).

    Args:
        file_path: Path to the PDF file

    Returns:
        List of Document objects
    """
    try:
        # Open the PDF file
        doc = fitz.open(file_path)

        # Extract the file name for metadata
        file_name = os.path.basename(file_path)

        # Create a list to store Document objects
        documents = []

        # Process each page
        for i, page in enumerate(doc):
            # Extract text from the page
            text = page.get_text()

            # Skip empty pages
            if not text.strip():
                continue

            # Create a Document object
            document = Document(
                page_content=text,
                metadata={
                    "source": file_path,
                    "file_name": file_name,
                    "page": i + 1,
                    "total_pages": len(doc)
                }
            )

            # Add the Document to the list
            documents.append(document)

        # Close the PDF
        doc.close()

        print(f"Successfully loaded {len(documents)} pages from {file_name} using PyMuPDF")
        return documents

    except Exception as e:
        print(f"Error loading PDF with PyMuPDF: {str(e)}")
        return []


@calc_time
def load_document(file):
    """
    Load a document file (PDF, DOCX, TXT) and return a list of Document objects.
    Uses PyMuPDF for PDFs for better performance and reliability.

    Args:
        file: Path to the document file

    Returns:
        List of Document objects
    """
    _, extension = os.path.splitext(file)

    try:
        if extension.lower() == ".pdf":
            # Use PyMuPDF for PDFs (faster and more reliable)
            return load_pdf_with_pymupdf(file)
        elif extension.lower() == ".docx":
            loader = Docx2txtLoader(file)
        elif extension.lower() == ".txt":
            loader = TextLoader(file)
        else:
            print(f"Document format {extension} is not supported!")
            return []

        # For non-PDF files, use the original loaders
        if extension.lower() != ".pdf":
            pages = loader.load_and_split()
            return pages

    except Exception as e:
        print(f"Error loading document {file}: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


@calc_time
def local_image_to_data_url(image_path):
    # Guess the MIME type of the image based on the file extension
    mime_type, _ = guess_type(image_path)
    if mime_type is None:
        mime_type = "application/octet-stream"  # Default MIME type if none is found

    # Read and encode the image file
    with open(image_path, "rb") as image_file:
        base64_encoded_data = base64.b64encode(image_file.read()).decode("utf-8")

    # Construct the data URL

    return f"data:{mime_type};base64,{base64_encoded_data}"


@calc_time
def chunk_data(pages, chunk_size=1200, chunk_overlap=200):
    from langchain.text_splitter import RecursiveCharacterTextSplitter

    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size, chunk_overlap=chunk_overlap
    )
    chunks = text_splitter.split_documents(pages)
    return chunks


@calc_time
def create_embeddings(chunks):
    print("Creating Embeddings")
    from langchain_community.vectorstores import FAISS

    embeddings = configs.Azure_langchain_client_embedding
    vector_store = FAISS.from_documents(chunks, embeddings)
    folder_path = os.path.join(
        configs.curr_dir, configs.permanent_storage, configs.FAISS_INDEX_STORE
    )
    vector_store.save_local(folder_path=folder_path, index_name=configs.index_name)
    return vector_store


@calc_time
def create_save_vectordb(selected_benchmarks: List[str] = None):
    """
    Create a Vector DB

    Args:
        selected_benchmarks: List of benchmark names to include in the vector DB
                            If None, all benchmarks will be included
    """
    try:
        print("Started to create vector DB")

        # Get policy documents from either Azure Blob Storage or local storage
        pdf_files = get_policy_documents(selected_benchmarks)

        if not pdf_files:
            print("Warning: No PDF files found to process!")
            return "No PDF files found to process!"

        data = []
        for file in pdf_files:
            print(f"Loading document: {os.path.basename(file)}")
            doc_data = load_document(file)
            if doc_data:
                data.extend(doc_data)
                print(f"Loaded {len(doc_data)} pages from {os.path.basename(file)}")
            else:
                print(f"Failed to load document: {os.path.basename(file)}")

        if not data:
            print("Warning: No data loaded from PDF files!")
            return "No data loaded from PDF files!"

        print(f"Chunking {len(data)} pages of data")
        chunks = chunk_data(data)
        print(f"Created {len(chunks)} chunks")

        print("Creating embeddings from chunks")
        vector_store = create_embeddings(chunks)  # noqa: F841
        print("Embeddings created and saved successfully")

        return "Vector DB created successfully!"
    except Exception as e:
        print(f"Error in create_save_vectordb: {str(e)}")
        import traceback
        traceback.print_exc()
        return f"Error creating vector DB: {str(e)}"


def get_policy_documents(selected_benchmarks: Optional[List[str]] = None) -> List[str]:
    """
    Get policy documents from either Azure Blob Storage or local storage.

    Args:
        selected_benchmarks: List of benchmark names to include

    Returns:
        List of file paths to policy documents
    """
    if configs.USE_AZURE_BLOB_STORAGE:
        return get_policy_documents_from_blob(selected_benchmarks)
    else:
        return get_policy_documents_from_local(selected_benchmarks)


def get_policy_documents_from_local(selected_benchmarks: Optional[List[str]] = None) -> List[str]:
    """
    Get policy documents from local storage.

    Args:
        selected_benchmarks: List of benchmark names to include

    Returns:
        List of file paths to policy documents
    """
    pdf_path = os.path.join(
        configs.curr_dir, configs.permanent_storage, configs.POLICY_FILE_PATH
    )
    print(f"Looking for policy documents in local storage: {pdf_path}")

    # Use glob to get a list of PDF files in the folder
    pdf_files = glob.glob(f"{pdf_path}/*.pdf")
    print(f"Found {len(pdf_files)} PDF files in policy document folder")

    # Filter PDF files based on selected benchmarks if provided
    if selected_benchmarks and len(selected_benchmarks) > 0:
        print(f"Filtering PDF files based on selected benchmarks: {selected_benchmarks}")
        filtered_pdf_files = []
        for file in pdf_files:
            file_name = os.path.basename(file)
            file_name_without_ext = os.path.splitext(file_name)[0]

            # Check if the file name matches any of the selected benchmarks
            if any(benchmark.lower().replace(" ", "_") in file_name_without_ext.lower() for benchmark in selected_benchmarks):
                filtered_pdf_files.append(file)
                print(f"Including file: {file_name} (matches selected benchmark)")
            else:
                print(f"Excluding file: {file_name} (does not match any selected benchmark)")

        # Use filtered files if any match, otherwise use all files
        if filtered_pdf_files:
            pdf_files = filtered_pdf_files
            print(f"Using {len(pdf_files)} filtered PDF files")
        else:
            print(f"No matching files found for selected benchmarks. Using all {len(pdf_files)} PDF files.")
    else:
        print("No benchmarks selected. Using all PDF files.")

    return pdf_files


def get_policy_documents_from_blob(selected_benchmarks: Optional[List[str]] = None) -> List[str]:
    """
    Get policy documents from Azure Blob Storage.

    Args:
        selected_benchmarks: List of benchmark names to include

    Returns:
        List of file paths to policy documents
    """
    print("Looking for policy documents in Azure Blob Storage")

    # List all blobs in the container
    blob_names = list_blobs()
    print(f"Found {len(blob_names)} blobs in Azure Blob Storage")

    # Filter blobs to only include PDFs
    pdf_blobs = [blob for blob in blob_names if blob.lower().endswith('.pdf')]
    print(f"Found {len(pdf_blobs)} PDF blobs")

    # Filter PDF blobs based on selected benchmarks if provided
    if selected_benchmarks and len(selected_benchmarks) > 0:
        print(f"Filtering PDF blobs based on selected benchmarks: {selected_benchmarks}")
        filtered_pdf_blobs = []
        for blob in pdf_blobs:
            blob_name_without_ext = os.path.splitext(blob)[0]

            # Check if the blob name matches any of the selected benchmarks
            if any(benchmark.lower().replace(" ", "_") in blob_name_without_ext.lower() for benchmark in selected_benchmarks):
                filtered_pdf_blobs.append(blob)
                print(f"Including blob: {blob} (matches selected benchmark)")
            else:
                print(f"Excluding blob: {blob} (does not match any selected benchmark)")

        # Use filtered blobs if any match, otherwise use all blobs
        if filtered_pdf_blobs:
            pdf_blobs = filtered_pdf_blobs
            print(f"Using {len(pdf_blobs)} filtered PDF blobs")
        else:
            print(f"No matching blobs found for selected benchmarks. Using all {len(pdf_blobs)} PDF blobs.")
    else:
        print("No benchmarks selected. Using all PDF blobs.")

    # Create a temporary directory to store downloaded files
    temp_dir = os.path.join(
        configs.curr_dir, configs.TEMP_CONSTANT, "blob_downloads"
    )
    os.makedirs(temp_dir, exist_ok=True)

    # Download each blob to the temporary directory
    local_files = []
    for blob in pdf_blobs:
        local_path = os.path.join(temp_dir, blob)
        if download_blob_to_file(blob, local_path):
            local_files.append(local_path)

    print(f"Downloaded {len(local_files)} PDF files from Azure Blob Storage")
    return local_files
