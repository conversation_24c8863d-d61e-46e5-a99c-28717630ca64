import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, <PERSON><PERSON><PERSON><PERSON>n, <PERSON><PERSON><PERSON>ist, ElementRef } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { MaterialModule } from 'src/app/modules/material/material.module';
import { CommonModule, NgClass, NgIf } from '@angular/common';

import { environment } from 'src/environments/environment';
import { GlobalDataService } from 'src/app/modules/models/gloabl_data.model';
import { Router } from '@angular/router';
// import * as byteSize from 'byte-size';
import byteSize from 'byte-size';
import { throwError } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-evidence-analyzer',
  standalone: true,
  imports: [MaterialModule, CommonModule, NgClass, NgIf],
  templateUrl: './evidence-analyzer.component.html',
  styleUrl: './evidence-analyzer.component.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class EvidenceAnalyzerComponent implements OnInit {
  // globalDataService: GlobalDataService = new GlobalDataService();

  showSpinner: boolean = false;

  // Old evidence array (for backward compatibility)
  evidences: any = [];

  // New domain-based evidence structure
  domainList: string[] = [];
  domainEvidences: { [domain: string]: any[] } = {};

  // Store questions for each domain
  domainQuestions: { [domain: string]: string[] } = {};
  expandedDomains: Set<string> = new Set();

  ControlDocument: any = [];
  displayedColumns: string[] = ['name', 'size', 'type'];

  display: string = "control-document";

  // Reference to file input elements
  @ViewChildren('fileInput') fileInputs!: QueryList<ElementRef>;

  // Make Math available in the template
  Math = Math;

  constructor(
    private globalDataService: GlobalDataService,
    private httpClient: HttpClient,
    private router: Router,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit() {
    // Check if we're coming from the question selection page
    const questionMethod = this.globalDataService.getQuestionSelectionMethod();
    if (questionMethod === 'hardcoded') {
      // If hardcoded questions were selected, skip the questionnaire upload
      this.display = 'upload-evidence'; // Set display directly to avoid triggering domain extraction

      // Clear any existing domains first
      this.domainList = [];
      this.domainEvidences = {};
      this.domainQuestions = {};
      this.expandedDomains.clear();

      // Load domains from hardcoded questions
      this.loadDomainsFromHardcodedQuestions();
    } else if (questionMethod === 'generate') {
      // This will be implemented later
      this.openSnackBar('Question generation feature is coming soon!');
    }
  }

  changeDisplay(display: string) {
    this.display = display;

    // If changing to evidence upload and we have a questionnaire, extract domains
    if (display === 'upload-evidence' && this.ControlDocument.length > 0) {
      // Clear any existing domains first
      this.domainList = [];
      this.domainEvidences = {};
      this.domainQuestions = {};
      this.expandedDomains.clear();

      // Try to extract domains from the backend first, with fallback to local extraction
      this.extractDomainsFromBackend();
    }
  }

  changeByteSize(size: number) {
    const customUnits = {
      simple: [
        { from: 0, to: 1.024e3, unit: '' },
        { from: 1.024e3, to: 1.024e6, unit: 'KB', long: 'kilobyte' },
        { from: 1.024e6, to: 1.024e9, unit: 'MB', long: 'megabyte' },
        { from: 1.024e9, to: 1.024e12, unit: 'GB', long: 'gigabyte' },
      ],
    };

    return byteSize(size, { customUnits, units: 'simple' });
  }

  // Method to handle domain-specific evidence upload
  onDomainEvidenceChange($event: any, domain: string) {
    // Initialize the domain's evidence array if it doesn't exist
    if (!this.domainEvidences[domain]) {
      this.domainEvidences[domain] = [];
    }

    // Add the files to the domain's evidence array
    for (let index = 0; index < $event.target.files.length; index++) {
      const file = $event.target.files[index];
      this.domainEvidences[domain].push(file);
    }
  }

  // Method to delete a domain-specific evidence file
  deleteDomainEvidence(domain: string, index: number) {
    if (this.domainEvidences[domain]) {
      this.domainEvidences[domain].splice(index, 1);
    }
  }

  // Method to get the count of evidence files for a domain
  getDomainEvidenceCount(domain: string): number {
    return this.domainEvidences[domain] ? this.domainEvidences[domain].length : 0;
  }

  // Method to check if any domain has evidence files
  hasAnyEvidence(): boolean {
    return Object.values(this.domainEvidences).some(files => files.length > 0);
  }

  // Method to get the count of domains that have at least one file
  getCompletedDomainCount(): number {
    return Object.values(this.domainEvidences).filter(files => files.length > 0).length;
  }

  // Method to trigger the file input click event
  triggerFileInput(domain: string): void {
    // Find the file input for this domain
    const fileInputElement = this.fileInputs.find(input =>
      input.nativeElement.id === `file-input-${domain}`
    );

    if (fileInputElement) {
      fileInputElement.nativeElement.click();
    }
  }

  // Method to get the appropriate icon for a file based on its extension
  getFileIcon(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'pdf':
        return 'picture_as_pdf';
      case 'doc':
      case 'docx':
        return 'description';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'image';
      default:
        return 'insert_drive_file';
    }
  }

  // Legacy method for backward compatibility
  onEvidencesChange($event: any) {
    for (let index = 0; index < $event.target.files.length; index++) {
      const file = $event.target.files[index];
      this.evidences.push(file);
    }
  }

  onControlDocChange($event: any) {
    this.ControlDocument = [];
    const document = $event.target.files[0];
    this.ControlDocument.push(document);

    // If we're already on the evidence upload screen, extract domains
    if (this.display === 'upload-evidence') {
      // Try to extract domains from the backend first, with fallback to local extraction
      this.extractDomainsFromBackend();
    }
  }

  // Method to extract domains from Excel using the backend API
  extractDomainsFromBackend() {
    if (this.ControlDocument.length === 0) {
      this.openSnackBar('No questionnaire file uploaded');
      return;
    }

    // Show loading state
    this.domainList = [];

    const formData = new FormData();
    formData.append('file', this.ControlDocument[0]);

    this.httpClient
      .post(environment.apiBaseUrl + '/extract_domains_from_excel', formData)
      .subscribe(
        (response: any) => {
          console.log('Domains extracted from backend:', response);

          if (response && response.domains && response.domains.length > 0) {
            // Sort domains alphabetically
            this.domainList = response.domains.sort();

            // Initialize evidence arrays and question arrays for each domain
            this.domainEvidences = {};
            this.domainQuestions = {};
            this.expandedDomains.clear();

            this.domainList.forEach(domain => {
              this.domainEvidences[domain] = [];
              this.domainQuestions[domain] = [];
            });

            // Now extract questions for each domain
            this.extractDomainsFromExcel();

            console.log(`Successfully extracted ${this.domainList.length} domains from backend`);
          } else {
            console.warn('No domains returned from backend API');
            this.openSnackBar('No domains found in the Excel file. Please check your file format.');
            // Fall back to local extraction as a last resort
            this.extractDomainsFromExcel();
          }
        },
        (error) => {
          console.error('Error extracting domains from backend:', error);
          this.openSnackBar('Error extracting domains. Falling back to local extraction.');
          // Fall back to local extraction
          this.extractDomainsFromExcel();
        }
      );
  }

  // Method to extract domains from the Excel file
  extractDomainsFromExcel() {
    if (this.ControlDocument.length === 0) {
      this.openSnackBar('No questionnaire file uploaded');
      return;
    }

    const file = this.ControlDocument[0];
    const reader = new FileReader();

    reader.onload = (e: any) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        // Get the first sheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Extract unique domains and questions
        this.domainList = [];
        this.domainEvidences = {};
        this.domainQuestions = {};
        this.expandedDomains.clear();

        // Check if we have any data
        if (jsonData.length > 0) {
          const firstRow = jsonData[0] as Record<string, unknown>;
          console.log("First row columns:", Object.keys(firstRow));

          // Define possible column names for domain and question fields
          const possibleDomainColumns = [
            'Domain', 'Security Domain', 'Category', 'Section',
            'Area', 'Control Domain', 'Control Category', 'Topic'
          ];

          const possibleQuestionColumns = [
            'Assessment Questions', 'Control', 'Question', 'Description',
            'Requirement', 'Control Objective', 'Control Text', 'Assessment'
          ];

          // Find the best matching columns
          let domainColumn = '';
          let questionColumn = '';

          // Find the first matching column for domain
          for (const col of possibleDomainColumns) {
            if (Object.keys(firstRow).some(key => key.toLowerCase() === col.toLowerCase())) {
              domainColumn = Object.keys(firstRow).find(key =>
                key.toLowerCase() === col.toLowerCase()
              ) || '';
              break;
            }
          }

          // If no domain column found, look for any column that might contain "domain" in its name
          if (!domainColumn) {
            const domainRelatedColumn = Object.keys(firstRow).find(key =>
              key.toLowerCase().includes('domain') ||
              key.toLowerCase().includes('category') ||
              key.toLowerCase().includes('area')
            );

            if (domainRelatedColumn) {
              domainColumn = domainRelatedColumn;
            }
          }

          // Find the first matching column for question
          for (const col of possibleQuestionColumns) {
            if (Object.keys(firstRow).some(key => key.toLowerCase() === col.toLowerCase())) {
              questionColumn = Object.keys(firstRow).find(key =>
                key.toLowerCase() === col.toLowerCase()
              ) || '';
              break;
            }
          }

          // If no question column found, look for any column that might contain question-related terms
          if (!questionColumn) {
            const questionRelatedColumn = Object.keys(firstRow).find(key =>
              key.toLowerCase().includes('question') ||
              key.toLowerCase().includes('control') ||
              key.toLowerCase().includes('requirement') ||
              key.toLowerCase().includes('assessment')
            );

            if (questionRelatedColumn) {
              questionColumn = questionRelatedColumn;
            }
          }

          // If we still don't have a domain column, use the first non-numeric column as a fallback
          if (!domainColumn) {
            for (const key of Object.keys(firstRow)) {
              const value = firstRow[key];
              if (typeof value === 'string' && key !== questionColumn) {
                domainColumn = key;
                break;
              }
            }
          }

          // If we still don't have a question column, use the longest text column as a fallback
          if (!questionColumn) {
            let longestTextLength = 0;
            for (const key of Object.keys(firstRow)) {
              const value = firstRow[key];
              if (typeof value === 'string' && value.length > longestTextLength && key !== domainColumn) {
                longestTextLength = value.length;
                questionColumn = key;
              }
            }
          }

          // If we have both columns, extract domains and questions
          if (domainColumn && questionColumn) {
            console.log(`Using detected columns: "${domainColumn}" for domain and "${questionColumn}" for questions`);
            this.extractDomainsAndQuestions(jsonData, domainColumn, questionColumn);
            console.log(`Extracted ${this.domainList.length} domains with questions:`, this.domainQuestions);
          } else if (domainColumn) {
            // If we only have a domain column, use it and create a generic question
            console.log(`Only domain column "${domainColumn}" found, using generic question`);
            this.extractDomainsAndQuestions(jsonData, domainColumn, domainColumn);
          } else {
            // If we couldn't find any suitable columns, show an error
            this.openSnackBar('Could not identify domain and question columns in the Excel file. Please ensure your file has columns for domain/category and questions/controls.');
            return;
          }
        } else {
          this.openSnackBar('Excel file is empty or has no valid data');
        }
      } catch (error) {
        console.error('Error parsing Excel file:', error);
        this.openSnackBar('Error parsing Excel file. Please check the format.');
      }
    };

    reader.readAsArrayBuffer(file);
  }

  // Helper method to extract domains and questions
  extractDomainsAndQuestions(jsonData: any[], domainField: string, questionField: string): void {
    console.log(`Extracting domains and questions from ${jsonData.length} rows`);
    console.log(`Using domain field: "${domainField}" and question field: "${questionField}"`);

    // Log the first few rows to see what we're working with
    console.log("Sample data:", jsonData.slice(0, 3));

    // Extract unique domains - accept any domain value without validation
    const uniqueDomains = new Set<string>();

    // Log all domains found
    const allDomains: string[] = [];

    for (const row of jsonData) {
      const typedRow = row as Record<string, unknown>;

      // Check if the domain field exists in this row
      if (!(domainField in typedRow)) {
        console.warn(`Domain field "${domainField}" not found in row:`, typedRow);
        continue;
      }

      // Get the domain value - if it's empty, use "General" as a fallback
      let domain = typedRow[domainField] as string;
      if (typeof domain === 'string') {
        domain = domain.trim();
      }
      if (!domain || domain === '') {
        domain = 'General';
      }

      allDomains.push(domain);
      uniqueDomains.add(domain);
    }

    console.log("All domains found:", allDomains);
    console.log("Unique domains:", Array.from(uniqueDomains));

    // Convert to array and sort alphabetically for better user experience
    this.domainList = Array.from(uniqueDomains).sort();

    // Initialize evidence arrays and question arrays for each domain
    this.domainList.forEach(domain => {
      this.domainEvidences[domain] = [];
      this.domainQuestions[domain] = [];
    });

    // Extract questions for each domain
    for (const row of jsonData) {
      const typedRow = row as Record<string, unknown>;

      // Skip rows without the required fields
      if (!(domainField in typedRow) || !(questionField in typedRow)) {
        continue;
      }

      // Get the domain and question values
      let domain = typedRow[domainField] as string;
      if (!domain || domain.trim() === '') {
        domain = 'General';
      }

      const question = typedRow[questionField] as string;

      if (question && this.domainQuestions[domain]) {
        this.domainQuestions[domain].push(question);
      }
    }

    // Log the final results
    console.log(`Extracted ${this.domainList.length} unique domains`);
    for (const domain of this.domainList) {
      console.log(`Domain "${domain}" has ${this.domainQuestions[domain].length} questions`);
    }
  }

  // Methods to get domain questions
  getDomainQuestionsCount(domain: string): number {
    return this.domainQuestions[domain]?.length || 0;
  }

  getDomainQuestions(domain: string, limit: number = -1): string[] {
    if (!this.domainQuestions[domain]) {
      return [];
    }

    if (this.expandedDomains.has(domain) || limit < 0) {
      return this.domainQuestions[domain];
    }

    return this.domainQuestions[domain].slice(0, limit);
  }

  showAllDomainQuestions(domain: string): void {
    this.expandedDomains.add(domain);
  }

  // Method to load domains from hardcoded questions
  loadDomainsFromHardcodedQuestions() {
    const hardcodedQuestions = this.globalDataService.getHardcodedQuestions();
    console.log('Loading domains from hardcoded questions:', hardcodedQuestions);

    if (hardcodedQuestions && hardcodedQuestions.length > 0) {
      // Log all domains from hardcoded questions - accept any domain value
      const allDomains = hardcodedQuestions.map(q => {
        // If domain is missing or empty, use 'General' as fallback
        return (q.domain && q.domain.trim() !== '') ? q.domain : 'General';
      });
      console.log('All domains from hardcoded questions:', allDomains);

      // Extract unique domains
      const uniqueDomains = new Set(allDomains);
      console.log('Unique domains from hardcoded questions:', Array.from(uniqueDomains));

      // Convert to array and sort alphabetically for better user experience
      this.domainList = Array.from(uniqueDomains).sort() as string[];

      // Initialize evidence arrays and question arrays for each domain
      this.domainList.forEach(domain => {
        this.domainEvidences[domain] = [];
        this.domainQuestions[domain] = [];
      });

      // Extract questions for each domain
      for (const q of hardcodedQuestions) {
        // If domain is missing or empty, use 'General' as fallback
        const domain = (q.domain && q.domain.trim() !== '') ? q.domain : 'General';

        if (q.question && this.domainQuestions[domain]) {
          this.domainQuestions[domain].push(q.question);
        }
      }

      // Log the final results
      console.log(`Extracted ${this.domainList.length} domains with questions from hardcoded questions`);
      for (const domain of this.domainList) {
        console.log(`Domain "${domain}" has ${this.domainQuestions[domain].length} questions`);
      }
    } else {
      // If no hardcoded questions found, use default domains
      console.log('No hardcoded questions found, using default domains');

      // Use the domains from the Excel file instead
      if (this.ControlDocument.length > 0) {
        console.log('Extracting domains from Excel file');
        this.extractDomainsFromBackend();
      } else {
        this.openSnackBar('No questionnaire file uploaded');
      }
    }
  }

  uploadFile() {
    this.showSpinner = true;
    const fileList: any = [];

    // Check if we're using hardcoded questions or uploaded questionnaire
    const questionMethod = this.globalDataService.getQuestionSelectionMethod();
    console.log('Question method:', questionMethod);

    if (questionMethod === 'hardcoded') {
      // Create a dummy Excel file with hardcoded questions
      // This would be handled by the backend
      console.log('Using hardcoded questions');
    } else if (this.ControlDocument.length > 0) {
      // Using uploaded questionnaire
      console.log('Using uploaded questionnaire');
      for (let i = 0; i < this.ControlDocument.length; i++) {
        fileList.push(this.ControlDocument[i]);
      }
    } else {
      // No questionnaire provided
      this.showSpinner = false;
      this.openSnackBar('Please upload a questionnaire or select hardcoded questions.');
      return;
    }

    // Add domain-based evidence files
    let hasEvidence = false;

    // Check if we have any evidence files
    if (Object.keys(this.domainEvidences).length > 0) {
      console.log('Adding domain-based evidence files');

      // Add all domain evidence files to the file list
      for (const domain of Object.keys(this.domainEvidences)) {
        const files = this.domainEvidences[domain];
        if (files && files.length > 0) {
          console.log(`Adding ${files.length} files for domain: ${domain}`);
          for (let i = 0; i < files.length; i++) {
            fileList.push(files[i]);
          }
          hasEvidence = true;
        }
      }
    } else if (this.evidences.length > 0) {
      // Fallback to legacy evidence files
      console.log('Adding legacy evidence files:', this.evidences.length);
      for (let i = 0; i < this.evidences.length; i++) {
        fileList.push(this.evidences[i]);
      }
      hasEvidence = true;
    }

    if (!hasEvidence) {
      this.showSpinner = false;
      this.openSnackBar('Please upload at least one evidence file for any domain.');
      return;
    }

    const formData = new FormData();
    for (let index = 0; index < fileList.length; index++) {
      formData.append('files', fileList[index]);
    }

    // Add selected benchmarks to the request (legacy support)
    const selectedBenchmarks = this.globalDataService.getSelectedBenchmarkNames();
    console.log('Selected benchmarks:', selectedBenchmarks);
    if (selectedBenchmarks && selectedBenchmarks.length > 0) {
      formData.append('benchmarks', JSON.stringify(selectedBenchmarks));
    } else {
      // If no benchmarks were selected, use all benchmarks
      console.log('No benchmarks selected, using all benchmarks');
    }

    // Add region selection to the request
    const regionSelection = this.globalDataService.getRegionSelection();
    if (regionSelection) {
      console.log('Adding region selection to request:', regionSelection);
      formData.append('region_selection', JSON.stringify({
        region: regionSelection.region,
        industry: regionSelection.industry,
        sector: regionSelection.sector
      }));
    }

    // Add domain selection to the request
    const domainSelection = this.globalDataService.getDomainSelection();
    if (domainSelection) {
      console.log('Adding domain selection to request:', domainSelection);
      formData.append('domain_selection', JSON.stringify({
        domain: domainSelection.domain,
        csp: domainSelection.csp
      }));
    }

    // Add question method to the request
    if (questionMethod) {
      formData.append('question_method', questionMethod);
      console.log('Added question method to request:', questionMethod);

      // If using hardcoded questions, add them to the request
      if (questionMethod === 'hardcoded') {
        const hardcodedQuestions = this.globalDataService.getHardcodedQuestions();
        if (hardcodedQuestions && hardcodedQuestions.length > 0) {
          formData.append('hardcoded_questions', JSON.stringify(hardcodedQuestions));
          console.log('Added hardcoded questions to request:', hardcodedQuestions.length);
        } else {
          console.log('No hardcoded questions found');
        }
      }
    }

    // Add domain evidence mapping to the request
    // This helps the backend know which files belong to which domain
    const domainEvidenceMapping: Record<string, string[]> = {};
    for (const domain of Object.keys(this.domainEvidences)) {
      const files = this.domainEvidences[domain];
      if (files && files.length > 0) {
        domainEvidenceMapping[domain] = files.map(f => f.name);
      }
    }

    if (Object.keys(domainEvidenceMapping).length > 0) {
      formData.append('domain_evidence_mapping', JSON.stringify(domainEvidenceMapping));
      console.log('Added domain evidence mapping to request:', domainEvidenceMapping);
    }

    console.log('Sending request to backend...');
    this.httpClient
      .post(environment.apiBaseUrl + '/analyze_evidences', formData)
      .subscribe(
        (response) => {
          this.showSpinner = false;
          console.log('Response from backend:', response);
          this.globalDataService.setAnalysisData(response);
          this.router.navigateByUrl('/control-result');
        },
        (error) => {
          this.showSpinner = false;
          console.error('Error during analysis:', error);
          let errorMessage = 'Error during analysis. Please try again.';
          if (error.error && error.error.detail) {
            errorMessage = error.error.detail;
          }
          this.openSnackBar(errorMessage);
        }
      );
  }

  deleteControl(index: number) {
    this.ControlDocument.splice(index, 1);
  }

  deleteEvidence(index: number) {
    this.evidences.splice(index, 1);
  }


  openSnackBar(message: string, action: string = "Close", duration: number = 5 * 1000) {
    this.snackBar.open(message, action, {
      duration: duration * 1000,
    });
  }

  handleError(error: HttpErrorResponse) {
    this.showSpinner = false;
    if (error.status === 0) {
      // A client-side or network error occurred. Handle it accordingly.
      console.error('An error occurred:', error.error);
      this.openSnackBar('An error occurred: ' + error.error);

    } else {
      // The backend returned an unsuccessful response code.
      // The response body may contain clues as to what went wrong.
      console.error(
        `Backend returned code ${error.status}, body was: `, error.error);
      this.openSnackBar(`Backend returned code ${error.status}, body was: `, error.error);
    }
    // Return an observable with a user-facing error message.
    return throwError(() => new Error('Something bad happened; please try again later.'));
  }
}
