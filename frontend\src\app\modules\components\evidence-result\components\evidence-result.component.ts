import { Component, OnInit, ViewChild, ElementRef, Inject } from '@angular/core';
import { GlobalDataService } from '../../../models/gloabl_data.model';
import { MaterialModule } from 'src/app/modules/material/material.module';
import { CommonModule } from '@angular/common';
import { MatSnackBar } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { saveAs } from 'file-saver';
import { Chart, registerables } from 'chart.js/auto';
import { DOCUMENT } from '@angular/common';
import { ChartOptions } from 'chart.js';
import { BaseChartDirective } from 'ng2-charts';
import { RouterLink } from '@angular/router';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';


@Component({
  selector: 'app-evidence-result',
  standalone: true,
  imports: [MaterialModule, CommonModule, BaseChartDirective, FormsModule],
  templateUrl: './evidence-result.component.html',
  styleUrl: './evidence-result.component.css',
})
export class EvidenceResultComponent implements OnInit {
  // GlobalDataService: GlobalDataService = new GlobalDataService();

  analysisResult: any = [];
  resultRows: string[] = [
    'control_id',
    'control',
    'observation',
    'evidence_files',
  ];
  showSpinner: boolean = false;

  mychart: any;
  canvas: any;
  ctx: any;
  summary: string = '';

  display: string = 'summary';

  pieChartData: any = [];


  // Pie
  public pieChartOptions: ChartOptions<'pie'> = {
    responsive: false,
  };
  public pieChartLabels = ['Compliant', 'Partially Compliant', 'Non Compliant'];
  public pieChartDatasets = [{
    data: [1, 1, 1]
  }];
  public pieChartColor: any[] = [
    {
      backgroundColor: [
        '#7DDA58',
        '#FE9900',
        '#C61A09'
      ],
    }
  ]
  public pieChartLegend = true;
  public pieChartPlugins = [];




  constructor(private globalDataService: GlobalDataService,
    private snackBar: MatSnackBar,
    private httpClient: HttpClient,
    private router: Router,) { }

  ngOnInit(): void {
    console.log('EvidenceResultModule initialized');
    this.analysisResult = this.globalDataService.getAnalysisData();
    // this.analysisResult = [
    //   {
    //     "observation": "Observation:\n\nThe evidence documents provided from \"Sonata Finance Private Limited\" demonstrate compliance with the control for background investigation checks, including identity verification, educational background verification, employment/professional background verification, and criminal/police record check.\n\n1. Identity and Address Verification: The policy outlines that Sonata will verify the identity and address of all positions (Employee Background Verification Policy).\n\n2. Educational Background Verification: For freshers, a character certificate attested by the registrar of the college or any certified gazetted officer is required (Employee Background Verification Policy).\n\n3. Employment/Professional Background Verification: Reference checks with individuals from the candidate’s previous employment are conducted, and for internal applicants, checks with immediate and previous supervisors are performed. Employment checks via EQUIFAX EMPLOYEE Bureau platform or via mail/No dues certificate from the previous employer are also mentioned (Employee Background Verification Policy, Annexure II – Employee Background Verification form).\n\n4. Criminal/Police Record Check: Although not explicitly stated in the provided documents, the comprehensive nature of the background checks, including the \"IQRARNAMA\" submission which involves a notarized agreement in the presence of authorized persons, suggests that a criminal/police record check could be part of the overall background investigation process.\n\nThe documents that comply with the control are:\n- Employee Background Verification Policy\n- Annexure II – Employee Background Verification form",
    //     "control_id": "4",
    //     "control": "Are your employee subjected to background investigation checks including below aspects?\n- Identity verification\n- Educational background verification\n- Employment/professional background verification\n- Criminal/police record check\n",
    //     "evidence_files": [
    //       "4_Background Verification Document.pdf"
    //     ],
    //     "risk": "NA",
    //     "Recommendation": "NA",
    //     "compliant": "Compliant"
    //   },
    //   {
    //     "observation": "Final Observation:\n\nAccording to the assessments conducted through the provided evidences, the organization does partially comply with the specified control requirements for cryptographic protection; however, there are notable deficiencies that need to be addressed to fully meet the standards.\n\nIn the case of encryption for data at-rest, the organization uses AES128 with GCM mode for Whole Disk Encryption, Filesystem Encryption, and Transparent Database Encryption. This information, as depicted in the \"Applications using Cryptography\" section of their policy document, does not meet the AES256 standard as required. Therefore, this represents a deviation from the control which necessitates the use of encryption at AES256 or higher and could pose a significant risk if more sensitive data is not adequately protected.\n\nFor data in-transit, TLS 1.2 is used as evidenced by the reference to HTTPS and email transport encryption standards on the respective pages of the organization’s Cryptography Standard document. This adherence is in line with the control requirement that mandates the use of TLS V1.2 or higher and suggests that data being transmitted over the organization's network is properly secured.\n\nThe key generation evidence showing RSA with a 2048-bit key length falls short of the control requirement since the control mandates the use of AES256 for at-rest data encryption. The absence of a key rotation policy within the evidence further undermines the cryptographic infrastructure's resilience, which indicates an additional security risk.\n\nFurthermore, the provided image evidence lacks clear information on the encryption standards and protocols utilized for ensuring the security of data in-transit, leaving the compliance status for this component of the control indeterminate.\n\nIn conclusion, while there are aspects of the organization's security posture that align with the required control for protecting data in-transit, their protection of data at-rest and key management practices require immediate improvements, specifically upgrading at-rest encryption to AES256 and establishing a proper key rotation policy. Additionally, full compliance with the control for data in-transit can only be confirmed once the missing information is provided and reviewed.",
    //     "control_id": "18",
    //     "control": "Are you using encryption to protect data at-rest (AES256 or higher) and data in-transit (TLS V1.2 or higher)?\n\nIf yes, please specify the encryption algorithm in \"Third Party Remark\"",
    //     "evidence_files": [
    //       "18_Cyber Standard Document - 1.pdf",
    //       "18_Key Creation - 2.png"
    //     ],
    //     "risk": "The use of AES128 for data at-rest encryption does not meet the AES256 standard as required, potentially leaving sensitive data insufficiently protected against threats that may exploit weaker encryption (NIST SP 800-53, Rev. 5, SC-28). The lack of a key rotation policy could lead to the compromise of cryptographic keys and the data they protect, increasing the risk of unauthorized data disclosure and manipulation (NIST SP 800-53, Rev. 5, SC-12, SC-13).",
    //     "Recommendation": "Upgrade the encryption for data at-rest to AES256 to align with the control requirements and enhance the protection of sensitive data (NIST SP 800-53, Rev. 5, SC-28). Implement a key rotation policy to ensure the cryptographic infrastructure's resilience and reduce the risk of key compromise (NIST SP 800-53, Rev. 5, SC-12, SC-13).",
    //     "compliant": "Partially Compliant"
    //   },
    //   {
    //     "observation": "The provided network diagram does not indicate the implementation of a multi-tier network architecture with a demilitarized zone (DMZ) to segregate public-facing systems from internal back-end systems. All assets are shown within a single network without clear segregation, potentially exposing back-end systems to security vulnerabilities.",
    //     "control_id": "31",
    //     "control": "Do you implement multi-tier network architecture (i.e. use of demilitarized zone - DMZ) to segment public-facing systems from back-end systems?",
    //     "evidence_files": [
    //       "31_Physical Network.PNG"
    //     ],
    //     "risk": "The absence of a multi-tier network architecture with a demilitarized zone (DMZ) increases the risk of unauthorized access to back-end systems and potential compromise of sensitive organizational data due to inadequate segregation of public-facing systems from internal systems.",
    //     "Recommendation": "Implement a multi-tier network architecture with a demilitarized zone (DMZ) to physically or logically separate public-facing systems from internal back-end systems, as per SC-7 Boundary Protection control from NIST SP 800-53, Rev. 5.",
    //     "compliant": "Non Compliant"
    //   }
    // ]

    console.log(this.analysisResult);

    this.pieChartData = this.getComplianceData();
    this.generateSummary(this.pieChartData);
  }

  ngAfterViewInit(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.
    this.createChart();


  }



  generateSummary(pieChartData: any) {
    this.summary = "nothing now"
    var totalControl = this.analysisResult.length
    var compliant = pieChartData[0]
    var partially_compliant = pieChartData[1]
    var non_compliant = pieChartData[2]

    var compliant_percentage = ((compliant / totalControl) * 100).toFixed(2)
    var partially_compliant_percentage = ((partially_compliant / totalControl) * 100).toFixed(2)
    var non_compliant_percentage = ((non_compliant / totalControl) * 100).toFixed(2)

    var summary = ""

    summary += "Out of " + totalControl + " controls which are tested. "
    summary += compliant_percentage + "% are compliant, "
    summary += partially_compliant_percentage + "% are partially compliant, and "
    summary += non_compliant_percentage + "% are non-compliant. "

    this.summary = summary;

  }


  createChart() {


    this.canvas = document.getElementById('myChart');
    console.log(this.canvas)



    this.ctx = this.canvas.getContext('2d');
    console.log(this.ctx)
    Chart.register(...registerables);
    Chart.defaults.borderColor = '#000';
    Chart.defaults.color = '#fff';


    this.mychart = new Chart(this.ctx, {
      type: 'pie',
      data: {
        labels: [
          'Compliant',
          'Partially Compliant',
          'Non Compliant'
        ],
        datasets: [{
          // label: 'My First Dataset',
          data: this.pieChartData,
          backgroundColor: [
            '#7DDA58', // compliant green
            '#FE9900', // partially compliant orange
            '#C61A09' // non-compliant red
          ],
          hoverOffset: 4
        }]
      },
      options: {
        layout: {
          padding: 0
        }
      }

    });

  }

  showAllresult() {
    this.router.navigateByUrl('/all-result');
  }

  changeDisplay(display: string) {
    this.display = display;

    if (display === "summary") {
      this.createChart();
    }
    else {
      // this.mychart.clear();
      this.mychart.destroy();
    }
  }

  getComplianceData() {
    var compliant = 0
    var partially_Compliant = 0
    var non_compliant = 0

    for (let i = 0; i < this.analysisResult.length; i++) {
      if (this.analysisResult[i]['compliant'] === 'Compliant') {
        compliant += 1;
      } else if (this.analysisResult[i]['compliant'] === 'Partially Compliant' || this.analysisResult[i]['compliant'] === 'Partially-Compliant' || this.analysisResult[i]['compliant'] === 'PartiallyCompliant') {
        partially_Compliant += 1;
      } else if (this.analysisResult[i]['compliant'] === 'Non-Compliant' || this.analysisResult[i]['compliant'] === 'Non Compliant' || this.analysisResult[i]['compliant'] === 'NonCompliant') {
        non_compliant += 1;
      }

    }

    var result = [compliant, partially_Compliant, non_compliant]

    return result;
  }


  getComplianceClass(compliant: string) {

    compliant = compliant.trim();

    if (compliant === 'Compliant') {
      return 'compliant';
    } if (compliant === 'Partially Compliant' || compliant === 'Partially-Compliant' || compliant === 'Partially-Compliant') {
      return 'partially-compliant';
    } if (compliant === 'Non-Compliant' || compliant === 'Non Compliant' || compliant === 'NonCompliant') {
      return 'non-compliant';
    }
    return '';
  }


  onImpactChange(result: any, value: string) {
    console.log(`Impact changed for control ${result.control_id} to ${value}`);
    result.impact = value;
    // You can add additional logic here if needed
  }

  onLikelihoodChange(result: any, value: string) {
    console.log(`Likelihood changed for control ${result.control_id} to ${value}`);
    result.likelihood = value;
    // You can add additional logic here if needed
  }

  downloadEvidence() {
    // this.download_csv(this.resultRows, this.analysisResult);
    this.showSpinner = true;

    console.log(this.analysisResult)
    this.httpClient
      .post(environment.apiBaseUrl + '/download_data', this.analysisResult, {
        observe: 'response',
        responseType: 'arraybuffer'
      })
      .subscribe((response: any) => {
        this.showSpinner = false;
        console.log('This is the response from backend', response);
        console.log(response.headers);
        const blob = new Blob([response.body], { type: response.headers.get('content-type') });
        // const fileName = 'project_plan.xlsx';

        const string = response.headers.get('content-disposition');
        console.log(string)

        const regex = /filename="([^"]*)"/;
        const match = string.match(regex);

        let filename = 'Project Plan.xlsx'
        if (match && match.length > 1) {
          filename = match[1];

        } else {
          filename = 'Project Plan.xlsx';
        }

        const file = new File([blob], filename, { type: response.headers.get('content-type') });
        const url = window.URL.createObjectURL(file);
        saveAs(file);

      });
  }

}
