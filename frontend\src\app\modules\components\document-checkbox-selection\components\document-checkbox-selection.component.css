.document-selection-container {
  padding: 20px;
  background-color: #252531;
  border-radius: 10px;
  border: 1px solid #333340;
}

.header {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.selection-count {
  color: #ccc;
  margin-top: 5px;
  margin-bottom: 10px;
  font-weight: bold;
}

.selection-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.action-button {
  background-color: #333340;
  color: #fff;
  font-size: 14px;
  padding: 5px 10px;
  border: 1px solid #444;
  cursor: pointer;
}

.action-button:hover {
  background-color: #444450;
}

.document-categories {
  margin-top: 20px;
}

.category-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.category-count {
  margin-left: auto;
  color: #ccc;
  font-size: 14px;
  font-weight: bold;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px 0;
  max-height: 300px;
  overflow-y: auto;
}

.document-item {
  padding: 8px 5px;
  border-bottom: 1px solid #333340;
}

.document-item:hover {
  background-color: #333340;
}

/* Override Angular Material styles */
::ng-deep .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background {
  background-color: yellow !important;
  border-color: yellow !important;
}

::ng-deep .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background {
  border-color: #888 !important;
  border-width: 2px !important;
}

::ng-deep .mat-mdc-checkbox .mdc-form-field {
  color: #fff;
}

::ng-deep .mat-mdc-checkbox .mdc-checkbox {
  margin-right: 8px;
}

::ng-deep .mat-expansion-panel {
  background-color: #333340;
  margin-bottom: 10px;
  border: 1px solid #444;
}

::ng-deep .mat-expansion-panel-header {
  padding: 0 16px;
  height: 48px !important;
}

::ng-deep .mat-expansion-panel-body {
  padding: 0 16px 16px;
}

::ng-deep .mat-expansion-indicator::after {
  color: #ccc;
}

/* Scrollbar styling */
.document-list::-webkit-scrollbar {
  width: 8px;
}

.document-list::-webkit-scrollbar-track {
  background: #252531;
}

.document-list::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 4px;
}

.document-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
