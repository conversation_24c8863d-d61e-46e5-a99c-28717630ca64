#!/usr/bin/env python3
"""
Test script for Azure Functions migration
Run this script to validate that your functions are working correctly
"""

import requests
import json
import time
import sys
from typing import Dict, Any

class FunctionTester:
    def __init__(self, base_url: str):
        """
        Initialize the tester with the base URL of your function app
        
        Args:
            base_url: Base URL like 'http://localhost:7071' or 'https://your-function-app.azurewebsites.net'
        """
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api"
        
    def test_health_check(self) -> bool:
        """Test the health check endpoint"""
        try:
            print("Testing health check endpoint...")
            response = requests.get(f"{self.api_base}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health check passed: {data}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Health check error: {str(e)}")
            return False
    
    def test_region_selection(self) -> bool:
        """Test the region selection endpoint"""
        try:
            print("Testing region selection endpoint...")
            response = requests.get(f"{self.api_base}/region_selection", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                regions = data.get('regions', [])
                print(f"✅ Region selection passed: Found {len(regions)} regions")
                return True
            else:
                print(f"❌ Region selection failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Region selection error: {str(e)}")
            return False
    
    def test_domain_selection(self) -> bool:
        """Test the domain selection endpoint"""
        try:
            print("Testing domain selection endpoint...")
            response = requests.get(f"{self.api_base}/domain_selection", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                domains = data.get('domains', [])
                print(f"✅ Domain selection passed: Found {len(domains)} domains")
                return True
            else:
                print(f"❌ Domain selection failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Domain selection error: {str(e)}")
            return False
    
    def test_documents_for_selection(self) -> bool:
        """Test the documents for selection endpoint"""
        try:
            print("Testing documents for selection endpoint...")
            
            # Test with sample parameters
            params = {
                'region': 'North America',
                'industry': 'Technology',
                'sector': 'Software'
            }
            
            response = requests.get(
                f"{self.api_base}/documents_for_selection", 
                params=params, 
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                total_docs = sum(len(docs) for docs in data.values())
                print(f"✅ Documents selection passed: Found {total_docs} total documents")
                return True
            else:
                print(f"❌ Documents selection failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Documents selection error: {str(e)}")
            return False
    
    def test_analyze_evidences_trigger(self) -> bool:
        """Test the analyze evidences trigger endpoint"""
        try:
            print("Testing analyze evidences trigger endpoint...")
            
            # Create a simple test payload
            test_data = {
                'files': [],
                'benchmarks': json.dumps(['test-benchmark']),
                'question_method': 'hardcoded',
                'hardcoded_questions': json.dumps([
                    {'question': 'Test question', 'domain': 'Test'}
                ])
            }
            
            response = requests.post(
                f"{self.api_base}/analyze_evidences", 
                data=test_data,
                timeout=30
            )
            
            if response.status_code == 202:  # Accepted
                data = response.json()
                job_id = data.get('job_id')
                print(f"✅ Analyze evidences trigger passed: Job ID {job_id}")
                return True
            else:
                print(f"❌ Analyze evidences trigger failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Analyze evidences trigger error: {str(e)}")
            return False
    
    def test_job_status(self, job_id: str = "test-job-id") -> bool:
        """Test the job status endpoint"""
        try:
            print(f"Testing job status endpoint with job ID: {job_id}...")
            
            response = requests.get(f"{self.api_base}/job_status/{job_id}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')
                print(f"✅ Job status passed: Status is '{status}'")
                return True
            else:
                print(f"❌ Job status failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Job status error: {str(e)}")
            return False
    
    def test_extract_domains_from_excel(self) -> bool:
        """Test the extract domains from Excel endpoint"""
        try:
            print("Testing extract domains from Excel endpoint...")
            
            # Create a simple test file (empty request for now)
            response = requests.post(
                f"{self.api_base}/extract_domains_from_excel",
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                domains = data.get('domains', [])
                print(f"✅ Extract domains passed: Found {len(domains)} domains")
                return True
            else:
                print(f"❌ Extract domains failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Extract domains error: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests and return results"""
        print(f"🚀 Starting Azure Functions tests for: {self.base_url}")
        print("=" * 60)
        
        tests = [
            ("Health Check", self.test_health_check),
            ("Region Selection", self.test_region_selection),
            ("Domain Selection", self.test_domain_selection),
            ("Documents for Selection", self.test_documents_for_selection),
            ("Analyze Evidences Trigger", self.test_analyze_evidences_trigger),
            ("Job Status", self.test_job_status),
            ("Extract Domains from Excel", self.test_extract_domains_from_excel),
        ]
        
        results = {}
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 Running: {test_name}")
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name} crashed: {str(e)}")
                results[test_name] = False
            
            time.sleep(1)  # Brief pause between tests
        
        print("\n" + "=" * 60)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        print("=" * 60)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        
        if passed == total:
            print("\n🎉 All tests passed! Your Azure Functions are working correctly.")
        else:
            print(f"\n⚠️  {total - passed} tests failed. Please check the logs above.")
        
        return results


def main():
    """Main function to run the tests"""
    if len(sys.argv) < 2:
        print("Usage: python test_functions.py <base_url>")
        print("Examples:")
        print("  python test_functions.py http://localhost:7071")
        print("  python test_functions.py https://your-function-app.azurewebsites.net")
        sys.exit(1)
    
    base_url = sys.argv[1]
    tester = FunctionTester(base_url)
    results = tester.run_all_tests()
    
    # Exit with error code if any tests failed
    if not all(results.values()):
        sys.exit(1)


if __name__ == "__main__":
    main()
