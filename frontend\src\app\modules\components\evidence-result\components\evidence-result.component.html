<!-- <div class="container p-4">
  <h2>Result</h2>
</div> -->

<div class="container">


  @if (analysisResult.length===0 ) {
  <div class="row justify-content-md-center align-items-center ">
    <div class="col-md-11 gy-4 justify-content-md-center backdrop">
      <div class="header">
        <h4>No Result</h4>
      </div>
      <div class="body">
        <div class="d-flex justify-content-end">
          <p>No existing results are available, as an analysis has yet to be performed. Initiate
            an analysis in IsecMapper in order to generate results.</p>
        </div>
      </div>
    </div>

  </div>
  } @else {
  <div class="row justify-content-md-center align-items-center ">
    <div class="col-md-11 gy-4 justify-content-between backdrop">
      <div class="d-flex justify-content-between" style="padding-bottom: 12px;">

        <div class="header gap-2">

          @if(display === "all-result"){
          <mat-icon class="back-button" matRipple (click)="changeDisplay('summary')">arrow_back</mat-icon>

          }

          <h4>Control Result</h4>
        </div>
        <button class="btn btn-primary" (click)="downloadEvidence()">Export</button>
      </div>
      <mat-divider></mat-divider>
      <div class="body py-4">
        @if(display === "summary"){
        <div class="dashboard-container container-fluid d-flex justify-content-between">
          <div class="summary">
            <div class="header">
              <h2>Summary</h2>
            </div>
            <div class="summary-content">
              <span>
                <p>{{summary}}</p>
              </span>
            </div>
          </div>
          <div class="chart">
            <canvas id="myChart" #myChart></canvas>
            <!-- <canvas baseChart [type]="'pie'" [datasets]="pieChartDatasets" [labels]="pieChartLabels"
              [options]="pieChartOptions" [plugins]="pieChartPlugins" [legend]="pieChartLegend">
            </canvas> -->

          </div>
        </div>

        <div class=" container-fluid d-flex justify-content-start">
          <button mat-button-raised class="btn btn-primary" (click)="showAllresult()">Show all
            result</button>

        </div>
        }



        @if(display==="all-result"){
        <div class="d-flex justify-content-start gy-4 backdrop py-4">

          <div class="header">
            <h4>Automated Control Analysis Result from GenAI</h4>
          </div>

        </div>

        @for (result of analysisResult; track result; let i = $index) {

        <div class="card text-bg-dark mb-3">
          <div class="card-header">
            <div class="control-header d-flex p-2 gap-4 justify-content-between">
              <div class="d-flex align-items-center">
                <span><b>Control ID : </b> {{result.control_id}}</span>
                <span class="custom-badge ms-3" [class]="getComplianceClass(result.compliant)"
                  matRipple>{{result.compliant}}</span>
              </div>
              <div class="d-flex gap-3">
                <mat-form-field appearance="outline" class="dropdown-field">
                  <mat-label>Impact</mat-label>
                  <mat-select [(ngModel)]="result.impact" (selectionChange)="onImpactChange(result, $event.value)">
                    <mat-option value="1">Negligible-1</mat-option>
                    <mat-option value="2">Low-2</mat-option>
                    <mat-option value="3">Medium-3</mat-option>
                    <mat-option value="4">High-4</mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" class="dropdown-field">
                  <mat-label>Likelihood</mat-label>
                  <mat-select [(ngModel)]="result.likelihood" (selectionChange)="onLikelihoodChange(result, $event.value)">
                    <mat-option value="1">Negligible-1</mat-option>
                    <mat-option value="2">Low-2</mat-option>
                    <mat-option value="3">Medium-3</mat-option>
                    <mat-option value="4">High-4</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="card-body">
            <table class="table table-dark table-bordered">
              <tbody>
                <tr>
                  <td scope="row"><b>Control</b></td>
                  <td class="with_white_space">{{result.control}}</td>
                </tr>
                <tr>
                  <td scope="row"><b>Evidence Files</b></td>
                  <td class="with_white_space">{{result.evidence_files.join(', ')}}</td>
                </tr>
                <tr>
                  <td scope="row"><b>Observation</b></td>
                  <td class="with_white_space">{{result.observation}}</td>
                </tr>

                <tr>
                  <td scope="row"><b>Risk</b></td>
                  <td class="with_white_space">{{result.risk}}</td>
                </tr>
                <tr>
                  <td scope="row"><b>Recommendation</b></td>
                  <td class="with_white_space">{{result.Recommendation}}</td>
                </tr>
                <tr>
                  <td scope="row"><b>Control Reference</b></td>
                  <td class="with_white_space">{{result.control_reference}}</td>
                </tr>

              </tbody>
            </table>
          </div>
        </div>


        }

        }


      </div>
    </div>
  </div>


  }


</div>