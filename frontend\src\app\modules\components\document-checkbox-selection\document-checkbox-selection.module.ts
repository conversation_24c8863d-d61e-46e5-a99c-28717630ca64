import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DocumentCheckboxSelectionComponent } from './components/document-checkbox-selection.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    MatCheckboxModule,
    MatDividerModule,
    MatButtonModule,
    MatExpansionModule,
    DocumentCheckboxSelectionComponent
  ],
  exports: [
    DocumentCheckboxSelectionComponent
  ]
})
export class DocumentCheckboxSelectionModule { }
