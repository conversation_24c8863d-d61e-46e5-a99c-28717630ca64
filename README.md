# IsecMapper

This project consists of a Frontend developed using Angular 17 and Backend using Python Fast API.

## Project architecture

This project has a clear separation between the backend and the frontend services. The backend server is implemented with Python's FastAPI framework, whereas the frontend is built with Angular 17.

Folder details:
- 'Frontend' - Contains Angular codebase
- 'Backend' - Contains FastAPI codebase

## Prerequisites

To run this project, you will need:
- Node.js (for Angular 17)
- Python 3.11 or above 
- pip (Python package installer)
- An IDE of your choice
- A web browser

## Getting Started (Backend)

Create a virtual environment by following these steps:

```shell
# navigate to project root folder
cd Backend 

# create a new Python virtual environment
python -m venv venv

# Activate the virtual environment
venv/Scripts/activate  
```

Once the virtual environment is activated, install the necessary dependencies:

```
pip install -r requirements.txt
```
or 
```
pip install -r requirements_lock.txt
```
note - requirements_lock.txt is preferred

To run the backend server, use the following command:

```
uvicorn app.main:app
```

The backend API can now be accessed at `http://localhost:8000`.

## Getting Started (Frontend)

First, navigate to the Frontend directory:

```shell
cd Frontend
```

Next, install project dependencies:

```
npm install
```

Start your Angular server with:

```
ng serve --open
```
or 
```
ng s -o
```

The application can now be accessed at `http://localhost:4200`.

## Contributing

Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.


## License
EY Internal only


